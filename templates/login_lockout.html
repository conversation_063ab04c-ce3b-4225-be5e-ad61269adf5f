{% extends "base.html" %}

{% block title %}Temporary lock out{% endblock %}
{% block heading %}Temporary lock out{% endblock %}

{% block content %}

    <p>You have made <b>too many failed sign in attempts</b> and have been temporarily locked out.</p>

    <P>Please wait <span id="timer">10 minutes</span> from your lockout time before trying to sign in again. Attempting to sign in before then will reset the lockout timer.</p>

    <p><a href="{% url 'login' %}">Back to sign in</a></p>

    <script>
        function setTime(interval) {
            var minutes = Math.floor(interval/60);
            var seconds = interval - (minutes*60);
            var timer = minutes + "m " + seconds + "s";
            document.getElementById("timer").textContent = timer;
        };
        function countDown(interval) {
            timer = setInterval(function() {
                if (interval-- > 0) {
                    setTime(interval);
                } else {
                    clearInterval(timer);
                };
            }, 1000);
        };
        window.onload = function() {
            const interval = 600;
            setTime(interval);
            countDown(interval);
        };
    </script>

{% endblock %}
