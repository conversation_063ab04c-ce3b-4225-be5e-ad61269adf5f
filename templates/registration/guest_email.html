{% extends "base.html" %}

{% block title %}Sign in with email{% endblock %}
{% block heading %}Sign in with email{% endblock %}

{% block content %}
<div>
  <div>
    {% if mapName %}<h3>{{mapName}}</h3>{% endif %}
    <p>Please enter your email. We will send an email to this address to verify your identity.</p>
    {% if form.errors %}
    <p style="color:red" role="alert">Invalid email format.</p>
    {% endif %}
  </div>
  <form method="post" action="{% url 'guest-email' %}?next={{ next|urlencode|default:'/' }}" class="pure-form pure-form-stacked" style="margin: var(--spacing)">
  {% csrf_token %}

  {% for field in form %}
  <div class="pure-control-group">
    {{ field.label_tag }}
    {{ field }}
  </div>
  {% endfor %}

  <div class="pure-controls" style="margin: 1em auto 0;">
    <input type="submit" value="Submit" class="pure-button pure-button-primary signin-link" />
  </div>
  <input type="hidden" name="next" value="{{ next|urlencode|default:'/' }}" />
  </form>
  <p>Please review our <a href="{% url 'privacy' %}">privacy policy</a> before submitting your email.</p>
  <p>or</p>
  <p><a href="{% url 'login' %}?next={{ next|urlencode|default:'/' }}">Sign in as admin</a></p>
</div>
{% endblock %}
