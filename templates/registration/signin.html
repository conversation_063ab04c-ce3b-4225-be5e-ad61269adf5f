{% extends "base.html" %}

{% block title %}Sign in{% endblock %}
{% block heading %}Sign in{% endblock %}

{% block content %}
<div style="width: 100%">
  <div>
    {% if mapName %}<h3>{{mapName}}</h3>{% endif %}
  </div>
  {% if user_email %}
  <p>You are currently signed in as <b>{{ user_email }}</b>. 
    To sign in with a different account, please sign out first using the button below.
  {% else %}
  <div class="signin-content">
    {% if sso %}

    {% if sso_error %}
    <p class="error">Microsoft Sign In was unsuccessful!</p>
    {% endif %}

    <div class="link-container">
      <a class="signin-link" href="{% url 'ms-auth-login' %}?next={{ next|urlencode|default:'/' }}">
        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" viewBox="0 0 21 21"><title>MS-SymbolLockup</title><rect x="1" y="1" width="9" height="9" fill="#f25022"/><rect x="1" y="11" width="9" height="9" fill="#00a4ef"/><rect x="11" y="1" width="9" height="9" fill="#7fba00"/><rect x="11" y="11" width="9" height="9" fill="#ffb900"/></svg>
        Sign in with Microsoft
      <a />
    </div>
    {% endif %}
    {% if guest %}
    <div class="link-container">
      <a class="signin-link" href="{% url 'guest-email' %}?next={{ next|urlencode|default:'/' }}"> 
      <svg width="21" height="21" viewBox="0 0 18 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 13.2572V2.74292M1 13.2572L6.33486 7.95663L1 2.74292M1 13.2572H17M1 2.74292H17M1 2.74292L8.93143 10.4938L17 2.74292M17 13.2572L11.5874 7.95663L17 2.74292M17 13.2572V2.74292" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
        Sign in with email
      <a />
    </div>
    {% endif %}
  </div>
  <p>Please review our <a href="{% url 'privacy' %}">privacy policy</a> before submitting your email.</p>
  <p>or</p>
  <p><a href="{% url 'login' %}?next={{ next|urlencode|default:'/' }}">Sign in as admin</a></p>
  {% endif %}
</div>
{% endblock %}
