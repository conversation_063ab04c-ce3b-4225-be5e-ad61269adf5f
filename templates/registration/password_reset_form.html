{% extends "base.html" %}
{% load i18n static %}

{% block title %}Password reset{% endblock %}
{% block heading %}Password reset{% endblock %}

{% block content %}

<p style="text-align: center;">{% translate 'Forgotten your password? Enter your email address below, and we’ll email instructions for setting a new one.' %}</p>

<form method="post" class="pure-form pure-form-stacked">{% csrf_token %}
<div class="pure-control-group">
    {{ form.email.errors }}
    <label for="id_email">{% translate 'Email address:' %}</label>
    {{ form.email }}
</div>
<div class="pure-controls" style="margin: 1em auto;">
    <input type="submit" value="{% translate 'Reset my password' %}" class="pure-button pure-button-primary" />
</div>
</form>

<p style="align-self: flex-start;"><a href="{% url 'welcome' %}">Back to root</a></p>
{% endblock %}
