{% extends "base.html" %}

{% block title %}Sign in as admin{% endblock %}
{% block heading %}Sign in as admin{% endblock %}

{% block content %}

{% if form.errors %}
  <p>Your username and password didn't match. Please try again.</p>
{% elif next %}
  <p>
  {% if user.is_authenticated %}
    Your account doesn't have access to this page. To proceed,
    please sign in with an account that has access.
  {% else %}
    Sign in to see this page.
  {% endif %}
  </p>
{% endif %}

<form method="post" action="{% url 'login' %}?next={{ next|default:'/' }}" class="pure-form pure-form-stacked">
{% csrf_token %}

{% for field in form %}
<div class="pure-control-group">
  {{ field.label_tag }}
  {{ field }}
</div>
{% endfor %}

<div class="pure-controls" style="margin: 1em auto;">
  <input type="submit" value="Sign in" class="pure-button pure-button-primary" />
  {# Swap from password_reset view temporarily as the mail function is not enabled #}
  <span class="pure-form-message-inline"><a href="{% url 'password_reset' %}">Reset password</a></span>
</div>
<input type="hidden" name="next" value="{{ next }}" />
</form>


{% endblock %}
