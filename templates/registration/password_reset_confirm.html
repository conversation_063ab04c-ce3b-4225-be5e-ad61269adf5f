{% extends "base.html" %}
{% load i18n static %}

{% block title %}Password reset{% endblock %}
{% block heading %}Password reset{% endblock %}

{% block content %}

{% if validlink %}

<p style="text-align: center;">{% translate "Please enter your new password twice so we can verify you typed it in correctly." %}</p>

<form method="post" class="pure-form pure-form-stacked">{% csrf_token %}
    <fieldset>
    <input class="hidden" autocomplete="username" value="{{ form.user.get_username }}">
    <div class="pure-control-group">
        {{ form.new_password1.errors }}
        <label for="id_new_password1">{% translate 'New password:' %}</label>
        {{ form.new_password1 }}
    </div>
    <div class="pure-control-group">
        {{ form.new_password2.errors }}
        <label for="id_new_password2">{% translate 'Confirm password:' %}</label>
        {{ form.new_password2 }}
    </div>
    <div class="pure-controls" style="margin: 1em auto;">
        <input type="submit" value="{% translate 'Change my password' %}" class="pure-button pure-button-primary" >
    </div>
    </fieldset>
</form>

{% if form.new_password1.help_text %}
<span class="pure-form-message">{{ form.new_password1.help_text|safe }}</span>
{% endif %}
{% elif request.user.is_authenticated %}
<div>
    <p>You are signed in as <b>{{ request.user.email }}</b>. It is not possible to reset your password while signed in to a user account - please sign out and request a new password reset.</p>

    <p><a href="{% url 'welcome' %}">Back to root</a></p>
</div>
{% else %}
<div>
    <p>{% translate "The password reset link was invalid, possibly because it has already been used.  Please request a new password reset." %}</p>

    <p><a href="{% url 'welcome' %}">Back to root</a></p>
</div>
{% endif %}

{% endblock %}
