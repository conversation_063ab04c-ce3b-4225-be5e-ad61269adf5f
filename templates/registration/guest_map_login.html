{% extends "base.html" %}

{% block title %}Guest access to {{mapName}}{% endblock %}

{% block heading %}{{projectName}}{% endblock %}

{% block content %}


<h3>{{mapName}}</h3>
<p>Welcome. Enter password to access map:</p>
<form id="pw-form" class="pure-form">
{% csrf_token %}
<input type = "password" id = "pwtext"/>
<button type="submit" class="pure-button pure-button-primary">Submit</button>
</form>
{% if messages %}
    {% for message in messages %}
    <p style="color:red">{{ message }}</p>
    {% endfor %}
{% endif %}
<!-- For sha1 function -->
<script src="https://unpkg.com/crypto-js@3.3.0/core.js" crossorigin></script>
<script src="https://unpkg.com/crypto-js@3.3.0/sha1.js" crossorigin></script>

<script>
function submitPassword (e) {
    e.preventDefault();
    var MAP_IRI  = "{{ mapIRI }}";

    var pw    = document.getElementById ("pwtext").value;
    var pwKey = CryptoJS.SHA1(pw + MAP_IRI).toString();

    document.cookie = "guestmapkey=" + pwKey + "; " +
                      "expires=Fri, 31 Dec 9999 23:59:59 GMT; " +
                      "path=/maps/{{ guestMapPath }}";

    window.location.replace ("/maps/{{ nextPath }}");
}

document.getElementById("pw-form").addEventListener("submit", submitPassword);
</script>

{% endblock %}
