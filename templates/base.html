{% load request_filters %}

<!DOCTYPE html>
<html lang="en">
<head>
  <title>{% block title %}{% endblock %}</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="shortcut icon" href="{% url 'favicon' %}">
  <link rel="stylesheet" href="/static/css/pure-min.css">
  <link rel="stylesheet" href="/static/css/base.css">
  <link rel="stylesheet" href="{% url 'custom-theme' %}">
  {% if metrics_site and not user.is_staff %}
    <script defer data-domain="{{ metrics_site }}" src="https://plausible.io/js/plausible.js"></script>
  {% endif %}
</head>
{% block style %}{% endblock %}
<body>
  {% if request.session.site %}
  <header>
    <p class="footer-navigation">SITE OVERRIDE | {{ request.session.site }} | 
      <a href="/api/site/reset?next={{ request|fullpath }}">Clear override</a></p>
  </header>
  {% endif %}
  <div class="main-container">
    <main>
      <h1>{% block heading %}{% endblock %}</h1>
      <section>{% block content %}{% endblock %}</section>
    </main>
  </div>
  {% if not hide_footer %}
  <footer>
    {% if auth_framework != "vm" %}
    <p class="footer-navigation">
      <a href="{% url 'privacy' %}">Privacy</a>
    </p>
    {% else %}
    <p class="footer-user">
      {% if request.user.is_authenticated %}
      Admin: <b>{{ request.user.email }}</b>
      {% elif request.session.email %}
      <b>{{ request.session.email }}</b>
      {% else %}
      You are not currently signed in.
      {% endif %}
    </p>
    <div class="footer-navigation">
      {% if request.user.is_authenticated or request.session.email %}
      <form class="logout-form" method="post" action="{% url 'logout' %}">
        {% csrf_token %}
        <button type="submit">Sign out</button>
      </form>
      {% else %}
      <a href="{% url 'signin' %}">Sign in</a>
      {% endif %}
      | <a href="{% url 'privacy' %}">Privacy</a>
    </div>
    {% endif %}
  </footer>
  {% endif %}
</body>
</html>
<script src="/static/js/login.js"></script>
