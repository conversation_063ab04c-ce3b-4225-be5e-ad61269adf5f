{% extends "base.html" %}

{% load request_filters %}

{% block title %}Permission Denied{% endblock %}

{% block content %}

    <h3>403 - Permission Denied</h3>
    {% if request.session.email and auth_framework == "vm" %}
    <p>Your email <b>{{ request.session.email }}</b> is not listed as having access to this map.
        You can contact the map owner to get your email whitelisted for access.</p>

    {% if request.user.is_authenticated %}
    <p>If you would like to use a different email to access the map, please
        sign out using the link at the bottom of the page and try again.</p>
    {% else %}
    <p>If you think you've entered the wrong email, you can try accessing the map
        with a <a href="{% url 'guest-email' %}?next={{ request|fullpath }}">different email.</a></p>
    {% endif %}

    {% else %}
    <p>You do not have permission to view this page.</p>
    {% endif %}
    <p style="align-self: flex-start;"><a href="{% url 'welcome' %}">Back to root</a></p>

{% endblock %}
