---
name: Gate

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  check:
    name: Basic gating validation
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: "21"
      - uses: actions/setup-python@v5
        with:
          python-version: "3.13"
      - name: Install
        run: |
          make get-deps-frontend
          pip3 install pipenv
          make get-deps-server
      - name: Check
        run: |
          make check
      - name: Build
        run: |
          make all-js
