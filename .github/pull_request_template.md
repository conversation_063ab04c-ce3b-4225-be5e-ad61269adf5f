Please include a brief description of the change, including relevant motivation and context.

# Checklist

As part of code review:

- [ ] Review requested from: @VisualMeaning/devs
- [ ] Include a link to a [JIRA ticket](https://visual-meaning.atlassian.net/jira/software/c/projects/SMP/boards/2).
- [ ] Ensure that new code is documented inline and that updated code has also had its documentation updated/added.
- [ ] If applying new database migrations, ensure that they have been tested on a local DB instance including rolling back to the previous migration.
- [ ] Verify that this PR does not introduce broken CSS selectors or data-testids for [automated testing](https://github.com/VisualMeaning/smp-automated-testing), or ensure that any issues are addressed.

After landing:

- Update ticket with any testing requirements
- Post on Teams if wider team need informing
