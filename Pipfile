[[source]]
name = "pypi"
url = "https://pypi.org/simple"
verify_ssl = true

[dev-packages]
black = "==25.1.0"
ruff = "==0.11.10"

[packages]
Django = "~=5.2"
dj-database-url = "~=2.3"
whitenoise = "~=6.9"
gunicorn = "*"
uvicorn = "*"
psycopg = ">=3.2"
django-cors-headers = "*"
django-prometheus = "*"
django-axes = "~=8.0"
django-simple-task = "*"
tzdata = "*"
# for blob proxy
aiohttp = "*"
azure-storage-blob = "*"
# boto3 is only required for the Heroku install to run DB backups
boto3 = "*"
# opencensus is used by Azure install and not installed by default
# bump these when they update to update Pipfile last-modified timestamp
# and then update the actual install commands in the Makefile with same versions
# opencensus-ext-azure==1.1.14
# opencensus-ext-django==0.8.0

msal = "~=1.31"

[requires]
python_version = "3.13"
