/*
 * Default brand colours, styles and logos. Will fall back to default if no custom branding is applied.
 * TODO: This is the EOM branding used as placeholder. We need to generate some default VM branding and replace.
 */

 :root {
	--color-dark: #2b2b2b;
	--color-medi: #949494;
	--color-lite: #f0f0f0;
	--color-lite-stripe: #f4f4f4;
	--gap: 3rem;
	--spacing: 1.5rem;
	--minus-spacing: -1.5rem;
}

/*
 * Styles for general layout, some specific to this board
 */

html {
	height: 100%;
}

body {
	color: var(--color-dark);
	font-family: var(--font-body);
	line-height: 1.5;
}

h1,
h2,
h3,
h4,
h5 {
	font-family: var(--font-head);
}

/*
 * Styles for headings, text blocks, and general typography flow.
 */

h1,
h2,
h3,
h4,
h5,
p,
dl,
ol,
ul {
	margin: var(--spacing);
}

ul ol,
ul ul,
ol ul,
ol ol {
	margin: 0;
}

* + h1,
* + h2,
* + h3 {
	margin-top: var(--gap);
}

h1 {
	font-size: 2em;
	font-weight: 800;
}
h2 { font-size: 1.6em; }
h3 { font-size: 1.4em; }
h4 { font-size: 1.2em; }
h5 { font-size: 1em; }

h1 + p {
	font-size: 1.2em;
}

/*
 * Styles for <a/> elements and linking
 */

a:link,
a:visited {
	color: inherit;
	text-decoration: none;
	cursor: pointer;
}

a:focus {
	background-color: transparent;
	color: #000;
	transition: background-color 0.2s ease, color 0.2s ease;
}

p a:link,
p a:visited {
	color: var(--color-blue);
	text-decoration: underline;
}

p a:focus {
	background-color: var(--color-lite);
	color: #000;
}

@media (hover: hover) and (pointer: fine) {
	a:hover {
		background-color: transparent;
		color: #000;
		transition: background-color 0.2s ease, color 0.2s ease;
	}

	p a:hover {
		background-color: var(--color-lite);
		color: #000;
	}
}

/*
 * Styles for template specific content
 */

input {
	outline: 0
}

input:focus-visible {
	outline: 1px solid var(--color-dark);
	outline-offset: -1px;
	background: white;
}

input:placeholder-shown {
	text-overflow: ellipsis;
}

button.expand {
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
	height: 1em;
	width: 1em;
	padding: 0;
}

button.expand:after {
	content: '\FF0B';
}

button.expand[aria-pressed=true]:after {
	content: '\FF0D';
}

.vm-triples {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	display: flex;
	flex-direction: column;
}

.vm-triples .title {
	background: var(--color-medi);
	padding: 0 2em;
	margin: var(--spacing);
	font-family: var(--font-head);
	font-size: 1.2em;
	font-weight: bold;
}

.vm-triples table {
	display: flex;
	flex-direction: column;
}

.vm-triples tbody {
	min-width: 0;
}

.triples-table {
	margin: var(--spacing);
	min-height: 0;
	display: flex;
	flex-direction: column;
	position: relative;
	gap: var(--spacing);
}

.triples-table .controls {
	align-self: flex-end;
	display: grid;
	grid-template-rows: auto auto; /* Two rows */
	grid-template-columns: 1fr 1fr; /* Two equal columns */
	width: 400px;
	max-width: 100%;
	gap: 10px;
}

.controls > *:nth-child(1) {
	grid-column: 1 / -1; /* Span across all columns */
}

.reshape input {
	height: 20px;
	width: 20px;
	vertical-align: text-bottom;
	margin-left: 4px;
}

.triples-table button.expand {
	position: absolute;
	top: 6.5em;
	left: 0.5em;
	cursor: pointer;
}

.triples-table .results {
	align-self: flex-end;
	margin-inline-end: 10%;
}

table {
	table-layout: fixed;
	text-align: left;
	border-spacing: 0;
	flex: 1;
	min-height: 0;
}

thead {
	margin-block-end: var(--spacing);
}

thead input {
	width: 80%;
}

tbody {
	display: block;
	overflow: auto;
}

tbody > tr.o {
	background: var(--color-lite);
}

th,
td {
	padding: 0 1em;
}

th button {
	background: transparent;
	border: 0;
	padding: 0.5em;
	margin: 0.5em 0;
	cursor: pointer;
	width: 100%;
}

th h4 {
	margin: 0;
	display: flex;
	justify-content: space-between;
}

th input {
	display: block;
}

tr {
	display: grid;
	grid-template-columns: repeat(2, 3fr) 4fr;
	padding: 0 1em;
}

tbody input {
	width: calc(100% - 6px);
	border: 1px solid transparent;
	background: transparent;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
}

@media (hover: hover) and (pointer: fine) {
	tbody input:hover {
		border-color: var(--color-dark);
		background: white;
	}
}
