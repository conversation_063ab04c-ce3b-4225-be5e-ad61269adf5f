@import url('https://fonts.googleapis.com/css2?family=<PERSON>ito&display=swap');

/*
 * Default brand colours, styles and logos. Will fall back to default if no custom branding is applied.
 * TODO: This is the EOM branding used as placeholder. We need to generate some default VM branding and replace.
 */
 
:root {
	--font: "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Noto Sans", sans-serif;

	/* TODO: replace these colours with neutrals to be overridden */
	--color-blue: #5d7fa4;
	--color-dark: #2b2b2b;
	--color-medi: #949494;
	--color-lite: #f0f0f0;
	--color-turq: #7fbebe;
	--color-roug: #b23e53;
	--color-purp: #6d5f88;
	--color-head: var(--color-blue);
	--color-body: var(--color-lite);
	--color-lite-stripe: #f4f4f4;
	--color-error: #ba1a1a;
	--gap: 3rem;
	--spacing: 1.5rem;
	--minus-spacing: -1.5rem;
	--line-width: 2px;
	/* will be overridden with url(/some/image) for projects */
	--logo: local;
	--text-heading-1-size: 1.375rem;
	--text-heading-2-size: 1.125rem;
}

/*
 * Styles for general layout, some specific to this board
 */

html {
	height: 100%;
}

body {
	background: var(--color-lite);
	color: var(--color-dark);
	font-family: var(--font);
	line-height: 1.5;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
}

h1,
h2,
h3,
h4,
h5 {
	font-family: var(--font);
}

/*
 * Styles for headings, text blocks, and general typography flow.
 */

h1,
h2,
h3,
h4,
h5,
p,
dl,
ol,
ul {
	margin: var(--spacing);
}

ul ol,
ul ul,
ol ul,
ol ol {
	margin: 0;
}

* + h1,
* + h2,
* + h3 {
	margin-top: var(--gap);
}

h1 {
	font-size: 2em;
	font-weight: 800;
}
h2 { font-size: 1.6em; }
h3 { font-size: var(--text-heading-1-size) }
h4 { font-size: var(--text-heading-2-size) }
h5 { font-size: 1em; }

h1 + p {
	font-size: 1.2em;
}

/*
 * Styles for <a/> elements and linking
 */

a:link,
a:visited {
	color: inherit;
	text-decoration: none;
	cursor: pointer;
}

a:focus {
	background-color: transparent;
	color: #000;
	transition: background-color 0.2s ease, color 0.2s ease;
}

p a:link,
p a:visited,
.footer-navigation a:link,
.footer-navigation a:visited,
.logout-form button {
	color: var(--color-blue);
	text-decoration: underline;
}

p a:focus,
.footer-navigation a:focus
{
	background-color: var(--color-lite);
	color: #000;
}

.link-container a:focus
{
	cursor: pointer;
	background-image: linear-gradient(transparent,rgba(0,0,0,.05) 40%,rgba(0,0,0,.1));
}

@media (hover: hover) and (pointer: fine) {
	a:hover {
		background-color: transparent;
		color: #000;
		transition: background-color 0.2s ease, color 0.2s ease;
	}

	p a:hover,
	.footer-navigation a:hover,
	.logout-form button:hover {
		background-color: var(--color-lite);
		color: #000;
	}

	.link-container a:hover {
		cursor: pointer;
		background-image: linear-gradient(transparent,rgba(0,0,0,.05) 40%,rgba(0,0,0,.1));
	}
}

/*
 * Styles for template specific content
 */

 /* it's flex boxes all the way down  - lets us center and appropriately size main inside this div class */
.main-container {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	width: 100%;
	/* set min-height to zero so that flex-shrink knows it's allowed to shrink this */
	min-height: 0;
	flex-grow: 1;
}

main {
	width: 100%;
	max-height: 100%;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	background-color: #fcfcfc;
	border-radius: 15px;
	box-shadow: -3px 0 2px rgba(0, 0, 0, 0.5);
}

main h1 {
	background: var(--color-head) no-repeat var(--logo);
	background-position: left center;
	background-size: auto 2em;
	color: var(--color-lite);
	font-size: 1.625em;
	font-weight: normal;
	margin: 0;
	text-align: center;
	min-height: 2.4375rem;
}

section {
	overflow-y: auto;
	display: flex;
	flex-direction: column;
	padding: var(--spacing);
	align-items: center;
}

footer {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex-shrink: 0;
}

.logout-form {
	display: inline;
}

.logout-form button {
	background: none;
	border: none;
	cursor: pointer;
	padding: 0;
}

.footer-user {
	margin-bottom: 0;
}

.footer-navigation {
	display: flex;
	gap: 0.5em;
	margin-top: 0;
	margin-bottom: var(--spacing);
}

.vm-project-items {
	display: flex;
	flex-direction: column;
	width: 100%;
}

.vm-project-items:nth-of-type(odd) {
	background: var(--color-lite-stripe);
}

.vm-item {
	width: 100%;
}

.vm-item:after {
	content: "";
	display: table;
	clear: left;
}

.vm-item h3 {
	margin: var(--spacing);
}

.vm-item img {
	display: block;
	float: left;
	margin: var(--spacing);
	width: 6em;
}

.vm-item aside {
	font-size: smaller;
}

.link-container {
	margin: var(--spacing);
}

a.signin-link {
	font-family: 'Segoe UI', Helvetica, sans-serif;
	font-size: 1em;
	font-weight: 600;
	color: #fff;
	background-color: var(--color-head);
	padding: 0.75em;
	border: none;
	text-decoration: none;
	border-radius: 5px;
	display: flex;
	align-items: center;
	justify-content: center;
	width: fit-content;
}

.signin-link svg {
	margin-right: 0.75em;
}

.error {
	color: var(--color-error);
	font-weight: 600;
}

@media (min-width: 32em) {
	main {
		width: 32em;
	}
}
@media (min-width: 56em) {
	main {
		width: 38em;
	}
	/* if we're on a screen big enough to clamp width we can also add a top-bottom margin for a border */
	.main-container {
		margin: 2em auto;
	}
}
