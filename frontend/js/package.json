{"name": "sm_frontend", "licence": "UNLICENCED", "private": true, "repository": "https://github.com/VisualMeaning/sm_platform.git", "version": "0.0.0", "browserslist": [">0.2% and since 2020", "firefox ESR", "not dead"], "dependencies": {"flexsearch": "=0.8.149", "globals": "^16.1.0", "leaflet": "^1.9.4", "markdown-it": "^14.1.0", "markdown-it-marked": "^0.3.3", "ol": "^10.5.0", "preact": "^10.26.6", "preact-render-to-string": "^6.5.12", "purecss": "^3.0.0", "rbush": "^4.0.1", "react-intl": "^7.1.11", "react-swipeable": "^7.0.2"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/eslint-parser": "^7.27.1", "@babel/plugin-transform-react-jsx": "^7.27.1", "@babel/preset-env": "^7.27.1", "@babel/preset-typescript": "^7.27.0", "@babel/register": "^7.27.1", "@eslint/compat": "^1.2.9", "@formatjs/cli": "^6.7.1", "@prefresh/babel-plugin": "^0.5.1", "@prefresh/webpack": "^4.0.2", "@stylelint/postcss-css-in-js": "^0.38.0", "@stylistic/eslint-plugin": "^4.2.0", "@testing-library/preact": "^3.2.4", "@types/jest": "^29.5.14", "@types/leaflet": "^1.9.18", "@types/markdown-it": "^14.1.2", "@types/rbush": "^4.0.0", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "babel-loader": "^10.0.0", "babel-plugin-formatjs": "^10.5.38", "csstype": "^3.1.3", "eslint": "^9.27.0", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "license-checker-webpack-plugin": "^0.2.1", "npm-run-all": "^4.1.5", "postcss-syntax": "^0.36.2", "source-map-loader": "^5.0.0", "stylelint": "^16.19.1", "stylelint-config-recommended": "^16.0.0", "typescript": "~5.8.3", "webpack": "^5.99.8", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.0"}, "overrides": {"react": "npm:preact", "@formatjs/fast-memoize": "2.2.2", "http-proxy-middleware": {"http-proxy": "github:kisoso/node-http-proxy#666ed05bfd2d9ca425a94e1cb9aa34079477d9e9"}}, "jest": {"testEnvironment": "jsdom", "testRegex": ".*\\.spec\\.tsx$", "transformIgnorePatterns": ["/node_modules/(?!ol)/"], "moduleFileExtensions": ["tsx", "js"], "moduleNameMapper": {"^react$": "preact/compat", "^react-dom$": "preact/compat", "^@testing-library/preact$": "<rootDir>/node_modules/@testing-library/preact/dist/cjs/index.js"}}, "scripts": {"lint": "eslint --max-warnings 0", "build": "webpack --config webpack.config.babel.js", "start": "webpack serve --config webpack.config.babel.js --env mode=development", "prepare": "run-p build translations", "test": "run-p lint style type unit", "style": "stylelint src/ style/", "type": "tsc", "unit": "jest", "extract": "formatjs extract 'src/**/*.tsx' --additional-component-names=Translate --additional-function-names=translate --id-interpolation-pattern=[contenthash:5] --out-file=lang/en.json", "compile": "formatjs compile-folder ./lang ./dist/lang --ast", "translations": "npm run extract && npm run compile"}, "sideEffects": ["*.css"]}