// Configuration for compiling modules using Babel

module.exports = (api) => {
  const plugins = [
    '@babel/plugin-transform-nullish-coalescing-operator',
    '@babel/plugin-transform-optional-chaining',
    ['formatjs', {
      'additionalComponentNames': ['Translate'],
      'additionalFunctionNames': ['translate'],
      'idInterpolationPattern': '[contenthash:5]',
      'removeDefaultMessage': !api.env('development'),
    }],
  ];
  const presets = [];
  const envForNode = ['@babel/preset-env', {targets: {node: 'current'}}];
  if (api.caller(c => c && c.name === '@babel/register')) {
    /* Transpiling local scripts */
    presets.push(envForNode);
  } else {
    /* Transpiling source to run in-browser */
    plugins.push(
      '@babel/plugin-transform-react-jsx',
    );
    if (api.env('development')) {
      /* Add plugin for @prefresh/webpack hook support */
      plugins.push('@prefresh/babel-plugin');
    }
    if (api.env('test')) {
      presets.push(envForNode);
    } else {
      presets.push('@babel/preset-env');
    }
    presets.push(
      ['@babel/preset-typescript', {
        'allExtensions': true,
        'isTSX': true,
        'optimizeConstEnums': true,
      }],
    );
  }
  return {plugins, presets};
};
