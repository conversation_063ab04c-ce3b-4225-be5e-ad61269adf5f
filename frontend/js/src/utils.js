// General utility code


//  ======================================================================
//  == Runtime checks

export function should (cond, msg) {
    if (!cond) {
        const m = msg
                  ? ('ENFORCEMENT FAILURE: ' + msg)
                  : 'ENFORCEMENT FAILURE';
        console.trace(m);
    }
}

export function enforce (cond, msg) {
    if (!cond) {
        const m = msg
                  ? ('ENFORCEMENT FAILURE: ' + msg)
                  : 'ENFORCEMENT FAILURE';
        throw new Error (m);
    }
}

// Enforcing object structure
//
// Given 'obj' an object to test, and matchers an object of type predicates:
//   matchers eg = { key1 : pred1, key2 : pred2 }
// Checks that all named keys are present in obj, and that each key has a value
// returning true when the relevant pred is called on it.
// (null can be used a pred to mean "don't care, just check key is there").
// This is particularly useful for type checking.
export function enforceMembers (obj, matchers) {
    Object.keys(matchers) .forEach (key => {
        const pred = matchers [key];
        if (isNull (pred)) {
            if (!(key in obj))
                console.error ('ERR enforceMembers: key "' + key +
                               '" not in:', obj);
            enforce (key in obj);
        } else {
            if (!(pred (obj[key]))) {
                console.error ('ERR enforceMembers: pred for key "' + key +
                               '" failed in: ', obj, pred);
            }
            enforce (pred (obj[key]));
        }
    });
}

// Throw if is undefined, otherwise return as is
export function ensureNotUndefined (e) {
    enforce (! isUndefined(e));
    return e;
}


//  ======================================================================
//  == Type predicates

// -- Simple

export function isString (e) {
    return typeof (e) === 'string';
}

export function isNumber (e) {
    return typeof (e) === 'number';
}

export function isArray (e) {
    return Array.isArray (e);
}

export function isBool (e) {
    return e === true || e === false;
}

// Usage: isOfClass(SomeClass) (someObj) -> bool
export function isOfClass (klass) {
    return function (e) {
        return e.__proto__ === klass.prototype;
    }
}

export function isFunction (e) {
    return e instanceof Function;
}

// -- Compsites

export const isNumberOrNull = e => isNumber(e) || isNull(e);

export const isArrayOrNull = e => isArray(e) || isNull(e);

export const isStringOrNull = e => isString(e) || isNull(e);


// -- Compound

// TODO find out how to do these

// function isStringArray (e)
// function isNumberArray (e)


//  ======================================================================
//  == Value predicates

export function isUndefined (e) {
    return e === undefined;
}

export function isNull (e) {
    return e === null;
}


//  ======================================================================
//  == Math-related

// Clamps the given number within the specified min-max range
export function clamp(num, min, max) {
    return Math.min(Math.max(num, min), max);
}
