// Display of media content
/** @jsx h */

import {h} from 'preact';

import {WithEnlargedDialog} from './components/EnlargedMediaDialog';
import {IRIS} from './rdf';

import type {MapModel} from './rdfmodels';
import type {RNode} from './types';

const API_BASE = '/api/content-item?uuid=';

export function maybeMedia(mm: MapModel, iri: string): RNode|null {
  const uuid = mm.uuidOf(iri);
  if (!uuid) {
    return null;
  }
  const src = API_BASE + encodeURIComponent(uuid);

  const contentType = mm.contentTypeOf(iri);
  const simpleType = /.*\/([^/]+)$/.exec(contentType)?.[1].toLowerCase() || '';
  const className = 'vm-media-container' + (simpleType ? ` ${simpleType}` : '');

  let elem: RNode|null = null;
  switch (mm.contentTypeOf(iri)) {
    case IRIS.VM.CONTENTTYPE.image:
      elem = <WithEnlargedDialog mediaType={simpleType}>
        <div className="vm-media" key={uuid}>
          <img src={src} />
        </div>
      </WithEnlargedDialog>;
      break;
    case IRIS.VM.CONTENTTYPE.video:
      elem = <div className="vm-media" key={uuid}>
        <video muted controls autoPlay><source src={src} /></video>
      </div>;
      break;
    default:
  }

  return <div className={className}>{elem}</div>;
}
