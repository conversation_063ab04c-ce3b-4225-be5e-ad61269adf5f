// Fetch map details over the api

import {getMapInfo} from './functions/htmlData';

import type {MapData, Terms} from './types';

function handleFetchError(what: string): (response: Response) => Response {
  return response => {
    if (!response.ok) {
      throw new Error(`fetch ${what} failed: ${response.statusText}`);
    }
    return response;
  };
}

export function loadMapData(apiRoot: string, mapIri: string) : Promise<MapData> {
  const termsUrl = `${apiRoot}v1/map-model-terms?iri=${encodeURIComponent(mapIri)}`;
  return fetch(termsUrl)
    .then(handleFetchError('map terms'))
    .then(response => response.json())
    .then((terms) => {
      const mapInfo = getMapInfo();
      if (enforceTerms(terms)) {
        return {...mapInfo, terms};
      }
      throw new Error('unexpected map model');
    });
}

function enforceTerms(json: unknown): json is Terms {
  const model = json as Terms;
  // TODO: Fill in from enforceMapModelDataValid()
  return model != null;
}
