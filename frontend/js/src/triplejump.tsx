// Slightly more typed map model operations

import {IRIS} from './rdf';
import {BASE_LENS} from './constants';

import {PREF_PROPERTY} from './rdfmodels';

import type {Triple} from './rdf';
import type {MapModel} from './rdfmodels';
import type {Existence, Inclusion} from './types';

export type ChainMap = {[iri: string]: string[]};

// Use private cache from triplestore but must be careful not to modify it
type SubjCache = Readonly<{[subj: string]: Triple[]}>;

// TODO: Difficult to value literal types more specific without ontology
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type PartitionResult = [{[pred: string]: any}, [string, any][]];

type LocationLensMap = {[location: string]: string};

// Classes used for RDF properties within ontologies
// Not an exhaustive list, just the ones we're tending to use.
const propertyTypes: Readonly<Existence> = {
  [IRIS.OWL.DatatypeProperty]: true,
  [IRIS.OWL.ObjectProperty]: true,
};

const lensTypes: Readonly<Existence> = {
  [IRIS.VM.Lens]: true,
};

function partitionReducer(preds: Inclusion, def: boolean, pref: boolean) {
  return (acc: PartitionResult, {pred, obj}: Triple) => {
    if (preds[pred] ?? def) {
      // Preferred or first obj
      acc[0][pred] = pref && PREF_PROPERTY[pred] && obj in PREF_PROPERTY[pred] && obj || acc[0][pred] || obj;
    } else {
      acc[1].push([pred, obj]);
    }
    return acc;
  };
}

export function partitionSubj(
  mm: MapModel,
  subj: string,
  preds: Inclusion,
  def = true,
  pref = true,
): PartitionResult {
  const triples: Triple[] = mm._store._bySubj(subj);
  const reducer = partitionReducer(preds, def, pref);
  return triples.reduce<PartitionResult>(reducer, [{}, []]);
}

// TODO: Could be an iterator rather than a list, may not be an improvement.
export type NamedResult = {iri: string; name: string}[];

export function allNamedItems(mm: MapModel): NamedResult {
  const result: NamedResult = [];
  const _subjs: Readonly<{[subj: string]: Triple[]}> = mm._store._getSubjCache();
  for (const subj in _subjs) {
    _subjs[subj].some((t: Triple) => {
      // Taking the first we find, rather than a consistent one... for now
      if (t.pred === IRIS.RDFS.label || t.pred === IRIS.VM.name) {
        result.push({iri: subj, name: t.obj});
        return true;
      }
      return false;
    });
  }
  return result;
}

export function buildLensChains(mm: MapModel): ChainMap {
  const result: ChainMap = {};
  const _subjs: SubjCache = mm._store._getSubjCache();
  for (const subj in _subjs) {
    _subjs[subj].forEach(t => {
      if (t.pred === IRIS.RDF.type && lensTypes[t.obj]) {
        result[t.subj] ??= [];
      }
      if (t.pred === IRIS.VM.parentLens) {
        result[t.obj] ??= [];
        (result[t.subj] ??= []).push(t.obj);
      }
    });
  }
  return _expandChains(result);
}

export function buildPropertyChains(mm: MapModel): ChainMap {
  const result: ChainMap = {};
  const _subjs: SubjCache = mm._store._getSubjCache();
  for (const subj in _subjs) {
    _subjs[subj].forEach(t => {
      result[t.pred] ??= [];
      if (t.pred === IRIS.RDF.type && propertyTypes[t.obj]) {
        result[t.subj] ??= [];
      }
      if (t.pred === IRIS.RDFS.subPropertyOf) {
        result[t.obj] ??= [];
        (result[t.subj] ??= []).push(t.obj);
      }
    });
  }
  return _expandChains(result);
}

export function buildClassChains(mm: MapModel): ChainMap {
  const result: ChainMap = {};
  const _subjs: SubjCache = mm._store._getSubjCache();
  for (const subj in _subjs) {
    _subjs[subj].forEach(t => {
      if (t.pred === IRIS.RDF.type) {
        result[t.obj] ??= [];
      }
      if (t.pred === IRIS.RDFS.subClassOf) {
        result[t.obj] ??= [];
        (result[t.subj] ??= []).push(t.obj);
      }
    });
  }
  return _expandChains(result);
}

/**
 * Builds and returns a map of primary properties from the provided MapModel instance.
 * Primary properties are identified as those of the type `IRIS.VM.PrimaryProperty`
 * or subproperties of that type.
 *
 * @param {MapModel} mm - The MapModel instance to retrieve index properties from.
 * @returns {Existence} A map of RDF primary properties.
 */
export function buildPrimaryProperties(mm: MapModel): Existence {
  return _buildProperties(mm, IRIS.VM.PrimaryProperty);
}

/**
 * Builds and returns a map of index properties from the provided MapModel instance.
 * Index properties are identified as those of the type `IRIS.VM.IndexProperty`
 * or subproperties of that type.
 *
 * @param {MapModel} mm - The MapModel instance to retrieve index properties from.
 * @returns {Existence} A map of RDF index properties.
 */
export function buildIndexProperties(mm: MapModel): Existence {
  return _buildProperties(mm, IRIS.VM.IndexProperty);
}

/**
 * Constructs and returns a map of properties that are of the specified type
 * or are subproperties of the specified type from the provided MapModel instance.
 *
 * @param {MapModel} mm - The MapModel instance to retrieve properties from.
 * @param {string} property - The type of property or its subproperty to map.
 * @returns {Existence} A map of RDF properties that match the specified type or its subproperties.
 */
function _buildProperties(mm: MapModel, property: string): Existence {
  const properties: ChainMap = mm._propertyChains();
  const primaries = Object.keys(properties).reduce<Existence>((acc, prop) => {
    if (property in mm.classesOf(prop)) {
      acc[prop] = true;
    }
    return acc;
  }, {});
  return Object.values(properties).reduce<Existence>((acc, chain) => {
    if (chain.some(prop => prop in primaries)) {
      acc[chain[0]] = true;
    }
    return acc;
  }, {});
}

export function classesOf(mm: MapModel, iri: string, direct: boolean): Existence {
  const result: Existence = {};
  const chains = mm._classChains();
  const _subjs: SubjCache = mm._store._getSubjCache();
  _subjs[iri]?.forEach((t: Triple) => {
    if (t.pred === IRIS.RDF.type) {
      if (direct) {
        result[t.obj] = true;
        return;
      }
      chains[t.obj].forEach((cls: string) => {
        result[cls] = true;
      });
    }
  });
  return result;
}

export function buildSubClassForest(mm: MapModel): ChainMap {
  const result: ChainMap = {};
  const _subjs: SubjCache = mm._store._getSubjCache();
  for (const subj in _subjs) {
    _subjs[subj].forEach(t => {
      if (t.pred === IRIS.RDF.type) {
        result[t.obj] ??= [];
      }
      if (t.pred === IRIS.RDFS.subClassOf) {
        result[t.subj] ??= [];
        (result[t.obj] ??= []).push(t.subj);
      }
    });
  }
  return _expandChains(result);
}

export function buildAncestorLensChains(mm: MapModel): ChainMap {
  const result: ChainMap = {};
  const _subjs: SubjCache = mm._store._getSubjCache();
  for (const subj in _subjs) {
    _subjs[subj].forEach(t => {
      if (t.pred === IRIS.RDF.type && t.obj === IRIS.VM.Lens) {
        result[t.subj] ??= [];
      }
      if (t.pred === IRIS.VM.parentLens) {
        result[t.subj] ??= [];
        (result[t.obj] ??= []).push(t.subj);
      }
    });
  }
  return _expandChains(result);
}

function _expandChains(result: ChainMap): ChainMap {
  const pending: string[] = [];
  const seen: Inclusion = {};
  let k;
  for (k in result) {
    if (k in seen) {
      continue;
    }
    do {
      if (k in seen) {
        if (!seen[k]) {
          seen[k] = true;
          result[k] = [k, ...result[k].flatMap(sk => result[sk])];
        }
      } else {
        pending.push(k, ...result[k]);
        seen[k] = false;
      }
    } while ((k = pending.pop()));
  }
  return result;
}

export type Relations = {[iri: string]: {[iri: string]: number}};

// Flags could be an enum but leads to odd compiled output anyway.
const enum RelF {
  Outgoing = 1,
  Incoming = 2,
  Direct = 4,
  Plane = 8,
}

// Gives a function that returns a matching function for a plane mask
export function getPlaneMatch(n: number): (v: number) => number {
  // 1 << 3 === RelF.Plane
  const bit = n << 3;
  return v => v & bit;
}

// Isolate lowest matching plane for given set of relation flags
export function toFirstPlane(v: number): number {
  // RelF.Plane >> 3 === 1
  const planeBits = v >> 3;
  // Note: returns (1 << plane) not plane, saving a log2 operation
  return planeBits & -planeBits;
}

// Make a number for use as mask for matching specific plane
export function getPlaneMask(n: number, outgoing: boolean, incoming: boolean): number {
  return (n << 3) | (outgoing ? RelF.Outgoing : 0) | (incoming ? RelF.Incoming : 0);
}

export function buildRelations(
  mm: MapModel,
  planePredicates: Readonly<Existence[]>,
  transitClasses: Readonly<Existence>,
): Relations {
  const rels: Relations = {};
  const toFold: Existence = {};
  const planeForPredicate: {[iri: string]: number} = {};
  planePredicates.forEach((existence, i) => {
    for (const k in existence) {
      planeForPredicate[k] |= RelF.Plane << i;
    }
  });
  const _subjs: SubjCache = mm._store._getSubjCache();
  for (const subj in _subjs) {
    _subjs[subj].forEach(t => {
      const obj = t.obj;
      if (transitClasses[obj] && t.pred === IRIS.RDF.type) {
        toFold[subj] = true;
        return;
      }
      // Drop just label items from the matrix which are never required
      if ((t.pred === IRIS.RDF.type && t.obj === IRIS.VM.Label) || t.pred === IRIS.VM.forThing) {
        return;
      }
      if (obj in _subjs) {
        const properties = RelF.Direct | (planeForPredicate[t.pred] || 0);
        (rels[subj] ??= {})[obj] |= properties | RelF.Outgoing;
        (rels[obj] ??= {})[subj] |= properties | RelF.Incoming;
      }
    });
  }
  for (const foldIRI in toFold) {
    const outgoing: string[] = [];
    const incoming: string[] = [];
    const r = rels[foldIRI];
    for (const iri in r) {
      const v = r[iri];
      if (v & RelF.Outgoing) {
        outgoing.push(iri);
      }
      if (v & RelF.Incoming) {
        incoming.push(iri);
      }
    }
    outgoing.forEach(outIRI => {
      incoming.forEach(inIRI => {
        (rels[outIRI] ??= {})[inIRI] |= RelF.Outgoing;
        (rels[inIRI] ??= {})[outIRI] |= RelF.Incoming;
      });
    });
    delete rels[foldIRI];
  }
  return rels;
}

export function subjsMatching(mm: MapModel, test: (t: Triple) => boolean): string[] {
  const result = [];
  const _subjs: SubjCache = mm._store._getSubjCache();
  for (const subj in _subjs) {
    if (_subjs[subj].some(test)) {
      result.push(subj);
    }
  }
  return result;
}

export function locationToLensMapping(mm: MapModel): LocationLensMap {
  // each distinct location (view or view + domains) must correspond to a single end-most leaf lens.
  const locationToLens: LocationLensMap = {};
  const mapsToViews: {[lensMap: string]: string[]} = {};
  const lensMapNodes = mm._getEntitiesWithClass(IRIS.VM.LensMapNode);
  mm._store._triples.forEach((t: Triple) => {
    if (t.pred === IRIS.VM.onLens && !lensMapNodes.includes(t.subj)) {
      locationToLens[t.subj] = t.obj;
    }
    if (t.pred === IRIS.VM.onLensMap) {
      (mapsToViews[t.obj] ||= []).push(t.subj);
    }
    // For labels reshaped to be on BASE_LENS, a lens object is created, but no view object.
    // The reason is the latter is used as map content, while the former is abstract for lens world.
    // This is odd spelling, but this object is inverted by other functions to get an
    // object[BASE_LENS] = [null] lookup so easiest to add it here.
    if (t.pred === IRIS.VM.ofLens && t.obj === BASE_LENS) {
      locationToLens['null'] = BASE_LENS;
    }
  });
  lensMapNodes.forEach((nodeIri: string) => {
    // note that only nodes with domains set will have their locations added to to the map
    // this means nodes can't overwrite onLens info for views, as those are keyed under the
    // base view location with no domains
    const nodeDomains = mm.forDomainOf(nodeIri);
    if (nodeDomains && nodeDomains.length) {
      const nodeViews = mapsToViews[mm.forLensMapOf(nodeIri)] || [];
      const lens = mm.onLensOf(nodeIri);
      nodeViews.forEach(view => {
        const location = [view].concat(nodeDomains.sort()).join(',');
        locationToLens[location] = lens;
      });
    }
  });
  return locationToLens;
}

export function thingToLocationsMapping(mm: MapModel): {[lens: string]: string[]} {
  const lensToLocations: {[lens: string]: string[]} = {};
  const locationToLens = locationToLensMapping(mm);
  // need to convert to {lens: [list of locations]}, and need to do this for all lenses,
  // not just leaf lenses
  // this is dumb, but javascript is dumb and has weird behaviour when I do Object.entries
  Object.keys(locationToLens).forEach(location => {
    let currentLens = locationToLens[location];
    while (currentLens) {
      (lensToLocations[currentLens] ||= []).push(location);
      currentLens = mm.parentLensOf(currentLens);
    }
  });

  const labels: string[] = mm.labels();
  const thingLocations = labels.reduce<{[thing: string]: string[]}>((acc, label) => {
    const lensDetail = mm._getField(label, IRIS.VM.forLensDetail);
    const lens = lensDetail && mm._getField(lensDetail, IRIS.VM.ofLens);
    const locations = lens && lensToLocations[lens];
    const iri = mm._getField(label, IRIS.VM.forThing);
    if (iri && locations) {
      (acc[iri] ||= []).push(...locations);
    }
    return acc;
  }, {});

  // Filtering duplicates at the end seem to produce lower timings
  // as oppose to keeping each list in the previous steps duplicate free.
  for (const key in thingLocations) {
    thingLocations[key] = [...new Set(thingLocations[key])];
  }

  return thingLocations;
}

// Ensures lens
export function lensFromLocation(mm: MapModel, location: string|null): string {
  if (!location) {
    return BASE_LENS;
  }
  const locationLensMap: LocationLensMap = mm.locationLensMap();
  const targetLens = locationLensMap[location];
  if (targetLens) {
    return targetLens;
  }
  const locArray = location.split(',').filter(n => n);
  const view = locArray[0];
  return mm.onLensOf(view) || BASE_LENS;
}
