// Special handling of tile information for map layers

import type {CSSProperties} from './types';

type ParseResult = {src: string; styles: CSSProperties};

// Split tiles source template into url and extra styles
//
// Slightly hacky method of propogating this information but can describe
// proper type later.
export function parseTilesSrc(urlTemplate: string): ParseResult {
  const hashAt = urlTemplate.indexOf('#');
  if (hashAt < 0) {
    return {src: urlTemplate, styles: {}};
  }
  const rules = urlTemplate.slice(hashAt + 1).split(';');
  /* eslint-disable-next-line @typescript-eslint/no-explicit-any */
  const styles = rules.reduce<CSSProperties>((acc: any, s) => {
    const match = /^\s*([^:]+)\s*:\s*(\S+)/.exec(s);
    if (match) {
      acc[match[1]] = match[2];
    }
    return acc;
  }, {background: '#fff'});
  return {src: urlTemplate.slice(0, hashAt), styles};
}
