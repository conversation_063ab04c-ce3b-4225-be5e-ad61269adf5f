// Encapsulates searching helpers

import {envFromUnicodeTerms} from 'markdown-it-marked';
import {supportLookBehind} from './functions/markdown';
import type {MapModel} from './rdfmodels';


function esc(s: string): string {
  return s.replace(/[\\[\](){}*?+.^$|]/g, '\\$&');
}

function buildWordFromMarkdown(word: string): string {
  const prefixOptions = {prefix: true, suffix: false};
  return `(?=.*${envFromUnicodeTerms([word], prefixOptions).markedPattern?.source})`;
}

function buildWord(word: string): string {
  const pattern = /^\W/;
  return pattern.test(word) ? `(?=.*(\\s+|^)${esc(word)})` : `(?=.*\\b${esc(word)})`;
}

export function buildSearch(s: string): RegExp {
  const words = s.match(/\S+/g) || [];
  const buildWordMethod = supportLookBehind ? buildWordFromMarkdown : buildWord;
  const flags = supportLookBehind ? 'iu' : 'i';
  const pattern = words.length ? words.map(w => buildWordMethod(w)).join('') : '';
  return new RegExp(pattern, flags);
}

export function containsSearch(mm: MapModel, iri: string, r: RegExp): boolean {
  const desc = mm.descriptionOf(iri);
  const name = mm.nameOf(iri);
  return (desc && r.test(desc)) || (name && r.test(name));
}

export function nameContainsSearch(mm: MapModel, iri: string, r: RegExp): boolean {
  const name = mm.nameOf(iri);
  return name && r.test(name);
}

