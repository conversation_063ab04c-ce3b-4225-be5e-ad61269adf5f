// Local debugging tools

import type {RefObject} from './types';

type Deps = {[name: string]: unknown};

export function recompare(note: string, ref: RefObject<Deps>, depMap: Deps): true {
  const old = ref.current;
  if (old) {
    const changed = Object.keys(old).filter(key => old[key] !== depMap[key]);
    console.log('GZ blown memo for', note, changed);
  }
  ref.current = depMap;
  return true;
}
