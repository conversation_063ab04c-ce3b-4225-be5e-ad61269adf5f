// Simple feature flagging that can be managed from local storage

import type {MapModel} from './rdfmodels';

const _isNotRelease = /^staging\.|:/.test(location.host) || undefined;

const flagDefaults: Flags = {
};

// TODO: No regular way of having flags reflected in the model currently so
// this mapping is used to look up by equivalent prefix.
const modelPredicateSuffixes = {
  absentLabels: 'absentLabels',
  edgeDisplay: 'showEdges',
  geek: null,
  itemSidebar: 'itemSidebar',
  markerAggregation: 'markerAggregation',
  hideMapSwitcher: 'hideMapSwitcher',
  noReshape: 'noReshape',
};

type FlagKeys = keyof typeof modelPredicateSuffixes;
export type Flags = Readonly<Partial<{[K in FlagKeys]: boolean}>>;

let flagCache: Flags;

export function flagInStorage(name: keyof Flags) : boolean | undefined {
  if (!flagCache) {
    flagCache = {};
    try {
      const flagString = localStorage.getItem('vmFlags');
      if (flagString) {
        Object.assign(flagCache, JSON.parse(flagString) as Flags);
      }
    } catch (e) {
      console.error(e);
    }
  }
  return flagCache[name];
}

export default function flag(mm: MapModel, name: keyof Flags) : boolean {
  return flagInStorage(name) ?? mm.mapHasFlagOn(modelPredicateSuffixes[name]) ?? !!flagDefaults[name];
}
