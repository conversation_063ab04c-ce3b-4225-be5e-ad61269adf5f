// Relations with labels

import {IRIS} from './rdf';
import {defaultCompare, localeInsensitive} from './sort';
import {partitionSubj} from './triplejump';

import type {Triple} from './rdf';
import type {MapModel} from './rdfmodels';
import type {Existence} from './types';

export type LabelledRelation = {
  iri: string;
  label: string;
  other: null|string;
  defaultOtherLabel: () => string;
  otherLabel?: string;
};

// sort by label and other's iri
export function sortLabelledRelations(relations: LabelledRelation[], mm: MapModel): LabelledRelation[] {
  const primaries: Existence = mm._primaryProperties();
  return relations.sort((a, b) => {
    // Sort primary properties first, then in ~alphabetical order
    return +(primaries[b.iri] || 0) - +(primaries[a.iri] || 0)
      || localeInsensitive(a.label, b.label)
      || defaultCompare(
        a.otherLabel ??= (a.other ? mm.nameOf(a.other) : a.defaultOtherLabel()),
        b.otherLabel ??= (b.other ? mm.nameOf(b.other) : b.defaultOtherLabel()),
      );
  });
}

export const FUNCTIONAL_PREDS: Readonly<Existence> = {
  [IRIS.RDF.type]: true,
  [IRIS.RDFS.label]: true,
  [IRIS.VM.plural]: true,
  [IRIS.VM.name]: true,
  [IRIS.VM.atGeoPoint]: true,
  [IRIS.VM.hasAspect]: true,
  [IRIS.VM.hasIcon]: true,
  [IRIS.VM.hasUUID]: true,
  [IRIS.VM.ofStory]: true,
  [IRIS.VM.order]: true,
};

export function getLabelledRelations(mm: MapModel, iri: string, preds?: Existence): LabelledRelation[] {
  const [_, genericPredObjs] = partitionSubj(mm, iri, {...FUNCTIONAL_PREDS, ...preds}, false);
  const genericPredSubjs: [string, string][] = mm.reversedRelationFor(iri);

  return [
    ...genericPredObjs.map(([iri, obj]) => ({iri, label: mm.className(iri), other: obj, defaultOtherLabel: () => '?'})),
    ...genericPredSubjs.map(([iri, subj]) => ({iri, label: mm.reverseClassName(iri), other: subj, defaultOtherLabel: () => '?'})),
  ];
}

function invIn(mm: MapModel, inv: string): Pick<LabelledRelation, 'label'|'other'> {
  return {
    label: mm._getField(inv, IRIS.VMHE.passiveLabel) || 'involved in',
    // TODO: Currently O(n) to find where the involvement is from.
    other: mm._store.triplesWithPredObj(IRIS.VMHE.hasInvolvement, inv).map((t: Triple) => t.subj)[0],
  };
}

function invOut(mm: MapModel, inv: string): Pick<LabelledRelation, 'label'|'other'> {
  return {
    label: mm._getField(inv, IRIS.VMHE.activeLabel) || 'involves',
    other: mm._getField(inv, IRIS.VMHE.relatesTo),
  };
}

export type Involvement = {inv: string} & LabelledRelation;

export function getInvolvementRelations(mm: MapModel, iri: string): Involvement[] {
  const fromActivity = mm.classOf(iri) === IRIS.VMHE.Activity;
  const fn = fromActivity ? invIn : invOut;

  return ((fromActivity
    ? mm.involvementsOf(iri)
    : mm._store.triplesWithSubjPred(iri, IRIS.VMHE.hasInvolvement).map((t: Triple) => t.obj)) as string[]
  ).map((inv: string) => ({
    // A bit weird as the predicate here is somewhat unreal, but okay.
    iri: fromActivity ? IRIS.VMHE.relatesTo : IRIS.VMHE.hasInvolvement,
    inv,
    ...fn(mm, inv),
    defaultOtherLabel: () => mm.className(inv),
  }));
}
