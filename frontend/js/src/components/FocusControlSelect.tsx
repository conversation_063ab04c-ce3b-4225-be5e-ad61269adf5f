
// Component for navigation to things within or between maps

/** @jsx h */
import {Fragment, h} from 'preact';
import {useEffect, useRef, useState} from 'preact/hooks';

import {usePlace} from '../context/place';
import useExistOnView from '../hooks/useExistOnView';
import {useFilterOfInterest} from '../hooks/useFilterOfInterest';
import useOtherMaps from '../hooks/useOtherMaps';
import {useTranslation} from '../intl';
import SVGCrosshair from '../svg/SVGCrosshair';
import SVGPointer from '../svg/SVGPointer';
import Button from './Button';
import LinkForViewSelectionAndFilter from './LinkForViewSelectionAndFilter';

import type {Filters, RNode} from '../types';

type LinkMapItemProps = Readonly<{
  iri: string;
  view: string|null;
  title?: string;
}>;
export default function FocusControlSelect({iri, view, title}: LinkMapItemProps): RNode|null {
  const [expand, setExpand] = useState(false);
  const selectRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const {filters} = usePlace();
  const filterOfInterest = useFilterOfInterest();
  const intl = useTranslation();
  const checkExistOnView = useExistOnView();
  const calculateOtherMaps = useOtherMaps();

  useEffect(() => {
    let handler: (e: MouseEvent) => void;
    if (expand) {
      handler = (e: MouseEvent) => {
        if (e.target instanceof Node) {
          if (selectRef.current && !selectRef.current.contains(e.target)
            && buttonRef.current && !buttonRef.current.contains(e.target)) {
            setExpand(false);
          }
        }
      };
      document.addEventListener('pointerdown', handler);
    }

    return () => handler && document.removeEventListener('pointerdown', handler);
  }, [expand]);

  useEffect(() => () => {
    setExpand(false);
  }, [iri, view]);

  const existOnView = checkExistOnView(iri);
  const otherMaps = calculateOtherMaps(iri);

  const collapseOnKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Esc' || e.key === 'Escape') {
      setExpand(false);
      e.stopPropagation();
      buttonRef.current?.focus();
    }
  };

  const filtersWithClassOfInterest = (mapIri: string|null): Filters => {
    const next = {...filters};
    const filter = mapIri && filterOfInterest[mapIri]?.(iri);

    if (filter) {
      const {iri: classIri, type} = filter;
      if (!filters[type]?.[classIri]) {
        next[type] = {...filters[type], [classIri]: true};
      }
      next.plottable = 'related';
    }
    return next;
  };
  /*
    Translations for the buttons that will be used for both the text content
    and a tooltip/title so that the button content can be truncated to fit within
    the available space but still be accessible on hover.
  */
  const buttonLabel = intl.translate({defaultMessage: 'Find on Map'});
  const buttonPlaceholderLabel = intl.translate({defaultMessage: 'Show Item on'});

  return <div className="vm-focus-control-select" onKeyDown={collapseOnKeyDown}>
    {(existOnView || !otherMaps.length) && <LinkForViewSelectionAndFilter
      className="vm-focus-button vm-toggle-button"
      select={iri}
      view={view}
      title={buttonLabel}
      filters={filtersWithClassOfInterest(view)}
      disabled={!existOnView}>
      <SVGCrosshair className="vm-svg vm-stroke-sm" />
      <span>{buttonLabel}</span>
    </LinkForViewSelectionAndFilter>}
    {otherMaps.length > 0 && <Fragment>
      {!existOnView && <div title={buttonPlaceholderLabel} className="vm-focus-button">
        <span>{buttonPlaceholderLabel}</span>
      </div>}
      <Button
        ref={buttonRef}
        className="vm-focus-button vm-toggle-button expand"
        tooltipMessage={title}
        aria-label={intl.translate({defaultMessage: 'Map Menu'})}
        aria-expanded={expand}
        onClick={() => setExpand(v => !v)}></Button>
      {expand && <div ref={selectRef} className="vm-focus-select">{otherMaps.map(map => <LinkForViewSelectionAndFilter
        key={map.iri}
        view={map.iri}
        select={iri}
        title={map.name}
        filters={filtersWithClassOfInterest(map.iri)}
        onClick={() => setExpand(false)}
        className="vm-focus-button vm-toggle-button"
      >
        <SVGPointer className="vm-svg vm-stroke-sm" />
        <span>{map.name}</span>
      </LinkForViewSelectionAndFilter>)}
      </div>}
    </Fragment>}
  </div>;
}
