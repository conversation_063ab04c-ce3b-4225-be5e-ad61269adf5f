// Search entry component
/** @jsx h */

import {h} from 'preact';

import {useMarkdown} from '../context/mark';
import {explainTypeDetails} from '../functions/explainItems';
import {Icon} from '../icons';
import SVGMagnifyingGlass from '../svg/SVGMagnifyingGlass';

import type {MarkedEnv} from 'markdown-it-marked';
import type {MapModel} from '../rdfmodels';
import type {RNode} from '../types';

type EntryExplainProps = Readonly<{
  mm: MapModel;
  iri: string;
}>;
function EntryExplain({mm, iri}: EntryExplainProps): RNode|null {
  return <span className="vm-explain">
    {explainTypeDetails({mm, iri})}
  </span>;
}

type SearchItemProps = {
  mm: MapModel;
  iri: string;
  markEnv?: MarkedEnv;
};
export function SearchItemContent(props: SearchItemProps): RNode {
  const {iri, mm, markEnv} = props;
  const kd = useMarkdown();

  return <div className="vm-search-item-content">
    <div className="vm-i-container">
      <Icon iri={iri} />
    </div>
    <div className="vm-label">
      <h4 {...kd.inline(mm.nameOf(iri), markEnv)} />
      <span className="vm-search-inline"><EntryExplain {...{mm, iri}} /></span>
    </div>
  </div>;
}

type SearchTermProps = {
  term: string;
  markEnv?: MarkedEnv;
};
export function SearchTermContent(props: SearchTermProps): RNode {
  const {term, markEnv} = props;
  const kd = useMarkdown();

  return <div className="vm-search-item-content vm-recent-content">
    <div className="vm-i-container">
      <div className="vm-i">
        <SVGMagnifyingGlass className="vm-svg vm-stroke-sm" />
      </div>
    </div>
    <div className="vm-label">
      <h4 {...kd.inline(term, markEnv)} />
    </div>
  </div>;
}
