// Top component for map terms display
/** @jsx h */

import {h} from 'preact';
import {useMemo} from 'preact/hooks';

import {TranslationProvider, useUserLanguage} from '../context/intl';
import TermsProvider from '../context/terms';
import {getMapLanguages} from '../context/triples';
import TriplesViewer from './TriplesViewer';

import type {MapData, RNode} from '../types';

type Props = Readonly<{mapReader: () => MapData}>;
/**
 * Top component for map terms display
 */
export default function MapTermsLive({mapReader} : Props): RNode {
  const mapData = mapReader();
  const languages = useMemo(() => getMapLanguages(mapData), [mapData]);
  const [lang, setLang] = useUserLanguage(languages);

  return <TranslationProvider lang={lang}>
    <TermsProvider mapReader={mapReader} lang={lang}>
      <TriplesViewer {...{mapData, lang, languages, setLang}} />
    </TermsProvider>
  </TranslationProvider>;
}
