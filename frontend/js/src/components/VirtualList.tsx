// A virtual list component that renders only visible rows with dynamic sizes
/** @jsx h */

import {Fragment, h} from 'preact';
import {useCallback, useEffect, useMemo, useState} from 'preact/hooks';
import {clamp} from '../utils';

import type {RefCallback} from 'preact';
import type {RNode} from '../types';

type HeightFn = (index: number) => number|null;

type VirtualItem = {
  initialHeight: number|null;
  measuredHeight: number|null;
  renderedElement: Element|null;
  isPersistent: () => boolean;
  getComputedHeight: () => number;
  setMeasuredHeight: (height: number) => void;
  setRef: (element: Element|null) => void;
};

type VirtualItems = ReadonlyArray<Readonly<VirtualItem>>;

function useVirtItems(itemCount: number, itemHeight: HeightFn): VirtualItems {
  const [, setTrigger] = useState(1);

  const buildItems = useCallback(() => {
    const triggerState = () => setTrigger(n => n + 1);
    return Array(itemCount).fill(null).map<VirtualItem>((_, i) => {
      return {
        initialHeight: itemHeight(i),
        measuredHeight: null,
        renderedElement: null,
        isPersistent(): boolean {
          return this.initialHeight === null;
        },
        getComputedHeight(): number {
          return this.initialHeight || this.measuredHeight || 0;
        },
        setMeasuredHeight(height: number): void {
          this.measuredHeight = height;
          triggerState();
        },
        setRef(element: Element|null): void {
          this.renderedElement = element;
        },
      };
    });
  }, [itemCount, itemHeight]);

  return useMemo(buildItems, [buildItems]);
}

function useVirtItemsMeasurer(virtItems: VirtualItems): void {
  useEffect(() => {
    if (window.ResizeObserver) {
      const observer = new ResizeObserver((entries: ResizeObserverEntry[]) => {
        for (const entry of entries) {
          const item = virtItems.find(x => x.renderedElement === entry.target);
          const height = (entry.target as HTMLElement|null)?.offsetHeight;
          if (item != null && height != null) {
            item.setMeasuredHeight(height);
          }
        }
      });

      virtItems.forEach(item => {
        if (item.renderedElement != null && item.isPersistent()) {
          observer.observe(item.renderedElement);
        }
      });

      return () => observer.disconnect();
    }

    return undefined;
  }, [virtItems]);
}

enum RenderItemType {
  Item = 'Item',
  Placeholder = 'Placeholder',
}

type RenderItem = {
  type: RenderItemType.Item;
  index: number;
  setItemRef: RefCallback<Element>;
} | {
  type: RenderItemType.Placeholder;
  index: number;
  count: number;
  height: number;
};

function useRenderItems(
  virtItems: VirtualItems,
  scrollElement: HTMLElement,
  offsetTop?: number): () => RenderItem[] {

  const [offset, setOffset] = useState(0);
  const [scrollHeight, setScrollHeight] = useState(0);

  useEffect(() => {
    if (typeof ResizeObserver !== 'undefined') {
      const observer = new ResizeObserver((entries: ResizeObserverEntry[]) => {
        for (const entry of entries) {
          setScrollHeight((entry.target as HTMLElement|null)?.offsetHeight || 0);
        }
      });
      observer.observe(scrollElement);

      return () => observer.disconnect();
    }

    return undefined;
  }, [scrollElement]);

  useEffect(() => {
    const handleScroll = () => {
      setOffset(clamp(scrollElement.scrollTop - (offsetTop || 0), 0, Infinity));
    };

    scrollElement.addEventListener('scroll', handleScroll);
    handleScroll();

    return () => {
      scrollElement.removeEventListener('scroll', handleScroll);
    };
  }, [scrollElement, offsetTop]);

  const getItems = useCallback(() => {
    const items: RenderItem[] = [];
    let totalHeight = 0;
    for (let index = 0; index < virtItems.length; index++) {
      const item = virtItems[index];
      const height = item.getComputedHeight();
      totalHeight += height;

      const isPersistent = item.isPersistent();
      // TODO: make this more generic
      // current fix is to render 3 more items before and after the visible list
      // to improve keyboard navigation when search item is expanded
      const itemsHeight = height * 3;
      const maxVisibleHeight = offset + scrollHeight + itemsHeight;
      const isVisible = offset - itemsHeight <= totalHeight && totalHeight <= maxVisibleHeight;

      if (isPersistent || height > 0 && isVisible) {
        items.push({
          type: RenderItemType.Item,
          index,
          setItemRef: (element) => item.setRef(element),
        });
      } else {
        const last = items[items.length - 1];
        if (last && last.type === RenderItemType.Placeholder) {
          last.height += height;
          last.count += 1;
        } else {
          items.push({
            type: RenderItemType.Placeholder,
            height,
            index,
            count: 1,
          });
        }
      }
    }

    return items;
  }, [virtItems, offset, scrollHeight]);

  return getItems;
}

type VirtualListProps = Readonly<{
  itemCount: number;
  scrollElement: HTMLElement;
  offsetIndex?: number;
  offsetTop?: number;
  itemHeight: HeightFn;
  itemRenderer: (index: number, setItemRef: RefCallback<Element>) => RNode;
  placeholderRenderer: (indexRange: [number, number], height: number) => RNode;
}>;
export default function VirtualList(props: VirtualListProps): RNode {
  const {
    itemCount,
    scrollElement,
    offsetTop,
    offsetIndex = 0,
    itemHeight,
    itemRenderer,
    placeholderRenderer,
  } = props;

  const virtItems = useVirtItems(itemCount, itemHeight);
  useVirtItemsMeasurer(virtItems);
  const getRenderItems = useRenderItems(virtItems, scrollElement, offsetTop);

  return (
    <Fragment>
      {getRenderItems().map((item: RenderItem) => item.type === RenderItemType.Item
        ? itemRenderer(item.index + offsetIndex, item.setItemRef)
        : placeholderRenderer([item.index, item.index + item.count - 1], item.height))}
    </Fragment>
  );
}
