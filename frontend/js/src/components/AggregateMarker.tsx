// Component for marker aggregation

/** @jsx h */

import {h} from 'preact';
import type {Coord} from '../context/ol';
import type {RNode} from '../types';
import type {MarkerFocus} from '../components/MapItems';
import {getOpacityStyles} from '../components/MapItems';

type AggregateMarkerProps = Readonly<{
  icon: string;
  count: number;
  position: Coord;
  opacity: number|null;
  focus: MarkerFocus;
}>;

export default function AggregateMarker(props: AggregateMarkerProps): RNode|null {
  const {icon, count, position, opacity, focus} = props;

  const backgroundImage = icon !== 'null' ? 'url("' + icon + '")' : 'var(--icon-unknown-marker)';
  const iconStyle = {
    backgroundImage,
  };
  const posStyle = {
    ['--markerX']: (position[1]) + 'px',
    ['--markerY']: (-position[0]) + 'px',
  };
  const opacityStyle = getOpacityStyles(opacity, focus);
  const style = Object.assign({}, posStyle, opacityStyle);

  return count > 0 && <div
    className="vm-marker-aggregation"
    style={style}>
    <span className="vm-marker-icon" style={iconStyle} />
    <p>{count}</p>
  </div> || null;
}
