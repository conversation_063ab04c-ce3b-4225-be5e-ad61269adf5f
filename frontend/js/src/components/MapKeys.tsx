// Global binding of key events for map changes
/** @jsx h */

import {useEffect} from 'preact/hooks';

import {useGeekery} from '../context/geek';
import {useNavigation, usePlace} from '../context/place';
import {useMapCursorPosition} from '../context/ol';


export default function MapKeys(): null {
  const {setPanelOpen} = useGeekery();
  const {filters, selection} = usePlace();
  const {setFilters, setView} = useNavigation();
  const getPosition = useMapCursorPosition();
  useEffect(() => {
    const onKey = (event: KeyboardEvent): void => {
      const metafied = event.altKey || event.ctrlKey || event.metaKey;
      const shiftied = event.shiftKey;
      switch (event.key) {
        case 'Escape':
        case 'Esc': {
          setView(null, {name: 'reset'});
          return;
        }
        case 'F':
        case 'f': {
          if (selection && !metafied) {
            const oldRelation = filters.relation;
            const relation = (shiftied && oldRelation)
              ? {...oldRelation, [selection]: !oldRelation[selection]}
              : {[selection]: true};
            setFilters({...filters, relation});
          }
          return;
        }
        case 'g': {
          setPanelOpen?.(prev => !prev);
          return;
        }
        case 'x': {
          if (!metafied && !shiftied) {
            const pos = getPosition();
            console.log('mouse at position', pos);
            navigator?.clipboard?.writeText(`[${pos[0].toFixed(2)}, ${pos[1].toFixed(2)}]`);
          }
          return;
        }
      }
    };
    document.addEventListener('keydown', onKey);
    return () => {
      document.removeEventListener('keydown', onKey);
    };
  }, [getPosition, setFilters, setPanelOpen, setView, filters, selection]);
  return null;
}
