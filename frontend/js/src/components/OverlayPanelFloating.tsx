// Overlay panel for mobile
/** @jsx h */

import {h} from 'preact';
import {useEffect, useRef} from 'preact/hooks';

import {PANEL_LABELS, usePanel, usePanelUpdaters} from '../context/panels';
import {useScreenProperties} from '../context/screen';
import {useTranslation} from '../intl';
import SVGMaps from '../svg/SVGMaps';
import PanelHeader from './shared/PanelHeader';

import type {ComponentChildren} from 'preact';
import type {Panel} from '../context/panels';
import type {RNode, RefObject} from '../types';

type Props = Readonly<{
  children: ComponentChildren;
  hidden: boolean;
  toggleElement?: RefObject<HTMLDivElement>;
  panel: Panel;
}>;
export default function OverlayPanelFloating(props: Props) : RNode {
  const {children, hidden, toggleElement, panel} = props;
  const {isLargeScreen} = useScreenProperties();
  const panelElement = useRef<HTMLDivElement>(null);
  const {open} = usePanel(panel);
  const {changeOpen} = usePanelUpdaters(panel);
  const intl = useTranslation();
  const showPanel = open && !hidden && !isLargeScreen;

  useEffect(() => {
    const handler = (e: MouseEvent) => {
      if (!(e.target instanceof Node) || !showPanel) {
        return;
      }
      if (toggleElement?.current && toggleElement.current.contains(e.target)) {
        return;
      }
      if (panelElement.current && !panelElement.current?.contains(e.target)) {
        changeOpen(false);
      }
    };

    document.addEventListener('pointerup', handler);
    return () => document.removeEventListener('pointerup', handler);
  }, [showPanel, toggleElement, changeOpen]);

  const classList = ['vm-float-block'];
  if (!showPanel) {
    classList.push('hide-bottom', 'delayed-hide');
  }

  // Overlay is always mounted. Close button is disabled to not be captured
  // by assistive tools.
  return <section ref={panelElement} aria-label={PANEL_LABELS[panel]} className={classList.join(' ')}>
    <PanelHeader
      panel={panel}
      title={intl.translate({defaultMessage: 'Maps'})}
      icon={<SVGMaps />}
      closeDisabled={!showPanel}
      deferredFocusMs={333} />
    <div className="vm-float-block-content">
      {showPanel && children}
    </div>
  </section>;
}
