// Outline of page design to show before content has loaded.
/** @jsx h */

import {h} from 'preact';

import {usePlatformStates} from '../context/platform';
import Button from './Button';

import type {RNode} from '../types';

type Props = {
  tilesSrc?: string;
};

export function Skeleton({tilesSrc}: Props): RNode {
  const showSIP = !!location.hash;

  return <div className="vm-page vm-skeleton">
    <div className="vm-map-placeholder vm-layer" >
      <img className="vm-shimmer" src={tilesSrc} alt="" />
    </div>
    <div className="vm-layer vm-float">
      <div className="vm-float-container base">
        <div className="vm-float-container-bottom">
          <div className="vm-shimmer-wrapper">
            <div className="vm-float-control-panel vm-float-block-skeleton vm-shimmer-normalized">
              <div className="vm-float-search-bar">
                <div className="vm-clearable-input-container">
                  <input type="search" disabled={true} />
                </div>
                <Button className="vm-filter-restore-button vm-toggle-button" disabled={true} />
                <Button className="vm-expand-filter-button vm-toggle-button" disabled={true} />
                <Button className="vm-toggle-button" disabled={true} />
              </div>
            </div>
          </div>
          {showSIP && <div className="vm-shimmer-wrapper">
            <div
              className="vm-float-selected-item vm-float-block-skeleton vm-shimmer-normalized"
              style={{minHeight: '7.5em'}} />
          </div>}
        </div>
        <div className="vm-float-container-top">
          <div className="vm-shimmer-wrapper" style={{alignSelf: 'flex-start'}}>
            <div className="vm-interaction vm-float-block-skeleton vm-shimmer-normalized">
              <Button className="vm-toggle-button vm-menu-button" disabled={true} />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div className="ol-zoom custom ol-unselectable ol-control vm-shimmer-wrapper">
      <div className="vm-float-block-skeleton vm-shimmer-normalized">
        <Button class="ol-zoom-in" disabled={true} />
        <Button class="ol-zoom-out" disabled={true} />
      </div>
    </div>
  </div>;
}

export default function MaybeSkeleton(props: Props): RNode|null {
  const {base: baseReady} = usePlatformStates();
  return baseReady ? null : <Skeleton {...props} />;
}
