// Page containing map and platform components

/** @jsx h */

import {h} from 'preact';
import {useEffect, useMemo} from 'preact/hooks';

import CachedHooksProvider from '../context/cache';
import CommentsPanelProvider from '../context/comments';
import {ControlPanelProvider} from '../context/controlpanel';
import {useGeekery} from '../context/geek';
import {useListContent} from '../context/page';
import PanelsProvider from '../context/panels';
import {usePlatformStates, usePlatformStatesUpdater} from '../context/platform';
import {SearchProvider} from '../context/search';
import {getMapLanguages} from '../context/triples';
import {useTutorial} from '../context/tutorial';
import {classes} from '../functions/html';
import IntroTutorial from './intro-tutorial';
import LoadFonts from './LoadFonts';
import {FullItemList} from './MapListModeFloating';
import {FloatingComponents} from './PageFloating';

import type {SupportedLocale} from '../context/intl';
import type {OlOptions} from '../hooks/useOlSettings';
import type {ComponentChildren, MapData, RNode} from '../types';

function PanelProviders(props: {children: ComponentChildren}): RNode {
  return <PanelsProvider>
    <ControlPanelProvider>
      <CommentsPanelProvider>
        {props.children}
      </CommentsPanelProvider>
    </ControlPanelProvider>
  </PanelsProvider>;
}

export type PageProps = {
  map: RNode;
  mapData: MapData;
  ol: OlOptions;
  lang: string;
  setLang: (lang: SupportedLocale) => void;
};

export default function Page(props: PageProps): RNode {
  const {mapData, lang} = props;
  const {panelDisplay} = useGeekery();
  const {open: isListMode} = useListContent();
  const {base} = usePlatformStates();
  const updateStates = usePlatformStatesUpdater();
  const {showTutorial} = useTutorial();
  const languages = useMemo(() => getMapLanguages(mapData), [mapData]);
  const title = mapData.name;

  // Remove the skeleton if list mode is set during map loading.
  // Note that the base state is not quite ready, and we are cancelling it early
  // when we switch list view. This shouldn't be a problem because the base
  // state is only used for loading map content.
  useEffect(() => {
    if (!base && isListMode) {
      updateStates({base: true});
    }
  }, [base, isListMode, updateStates]);

  return <div {...classes({['debug-' + panelDisplay]: panelDisplay != null}, 'vm-page')}>
    {/* hack to lead screen reader's initial load focus */}
    <span>&nbsp;</span>
    <SearchProvider lang={lang}>
      <CachedHooksProvider>
        <LoadFonts />
        <PanelProviders>
          {showTutorial && <IntroTutorial />}
          <div {...classes({'mode-list': isListMode}, 'vm-layer', 'vm-content')}>
            {isListMode && <FullItemList
              lang={props.lang}
            /> || props.map}
          </div>
          <FloatingComponents
            {...props}
            {...{languages, title}}
          />
        </PanelProviders>
      </CachedHooksProvider>
    </SearchProvider>
  </div>;
}
