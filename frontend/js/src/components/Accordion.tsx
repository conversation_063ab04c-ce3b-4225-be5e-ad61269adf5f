// Accordion with custom header

/** @jsx h */

import {h} from 'preact';
import {forwardRef} from 'preact/compat';
import {useEffect, useMemo, useRef, useState} from 'preact/hooks';

import Button from './Button';

import {useTranslation} from '../intl';

import SVGArrowWide from '../svg/SVGArrowWide';

import type {ComponentChildren, ForwardRef, RNode} from '../types';

export type AccordionProps = Readonly<{
  header: ComponentChildren;
  children: ComponentChildren;
  collapsed: boolean;
  disabled: boolean;
  isSelected?: boolean;
  className?: string;
  onToggle?: () => void;
}>;
/**
 * Accordion component that has it own collapsed state management or from parent.
 * Note: Cannot manage from both places at the same time.
 */
const Accordion = forwardRef((props: AccordionProps, ref: ForwardRef<HTMLDivElement>): RNode => {
  const {header, children, collapsed: collapsedInherited, disabled, isSelected, className, onToggle} = props;
  const [collapsed, setCollapsed] = useState(collapsedInherited);
  const contentRef = useRef<HTMLDivElement>(null);
  const {translate} = useTranslation();

  const onClick = useMemo(() => onToggle ? () => onToggle() : () => setCollapsed(v => !v), [onToggle]);
  const collapsedValue = onToggle ? collapsedInherited : collapsed;
  const [maxHeight, setMaxHeight] = useState(0);

  useEffect(() => {
    if (!window.ResizeObserver) {
      return undefined;
    }

    const {current} = contentRef;
    const observer = new ResizeObserver(() => {
      setMaxHeight(!collapsedValue && contentRef.current?.scrollHeight || 0);
    });

    if (current) {
      observer.observe(current);
    }

    return () => {
      observer.disconnect();
    };
  }, [children, collapsedValue]);

  const extendedClassName = [
    'vm-accordion',
    className,
  ].filter(Boolean).join(' ');
  return <div
    ref={ref}
    aria-current={isSelected}
    className={extendedClassName}>
    <div
      className={'vm-accordion-header' + (disabled ? ' disabled' : '')}
      onClick={onClick}>
      <span className="vm-t">{header}</span>
      <Button
        className={'vm-expand-button' + (collapsedValue ? '' : ' flip')}
        tooltipMessage={collapsedValue ? translate({defaultMessage: 'Expand'}) : translate({defaultMessage: 'Collapse'})}
        tabindex={0}
        aria-label="Expand"
        aria-pressed={!collapsedValue}>
        <SVGArrowWide />
      </Button>
    </div>
    <div className={'vm-accordion-content-container' + (collapsedValue ? ' delayed-hide' : '')} style={{maxHeight}}>
      <div ref={contentRef} className="vm-accordion-content">{children}</div>
    </div>
  </div>;
});

export default Accordion;
