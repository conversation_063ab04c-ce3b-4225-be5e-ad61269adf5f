// Voting info and identity choice
/** @jsx h */

import {h} from 'preact';

import {useAnnotationState} from '../context/annotation';
import {useMarkdown} from '../context/mark';
import {useNavigation, usePlace} from '../context/place';
import {useModel} from '../context/triples';
import {useUser} from '../context/user';
import {djangoCsrfTokenValue} from '../django';
import {Translate, useTranslation} from '../intl';

import Button from './Button';

import type {RNode} from '../types';

export type Props = Readonly<{
  show: boolean;
  setShow: (show: boolean) => void;
}>;

export default function VoterStart(props: Props): RNode|null {
  const {show, setShow} = props;
  const kd = useMarkdown();
  const mm = useModel();
  const {view} = usePlace();
  const {setView} = useNavigation();
  const loaded = useAnnotationState() === 'loaded';
  const user = useUser();
  const intl = useTranslation();

  const desc = mm.descriptionOf(view) || intl.translate({defaultMessage: 'Vote now!'});

  const cancel = (e: Event) => {
    e.preventDefault();
    setView(null);
  };
  const start = (e: Event) => {
    e.preventDefault();
    setShow(false);
  };

  const isUserEmail = user.includes('@');

  const signout_form = <form className="logout-form" method="post" action="/accounts/logout/?next=/signin">
    <input type="hidden" name="csrfmiddlewaretoken" value={djangoCsrfTokenValue()} />
    <button type="submit"><Translate defaultMessage="sign out" /></button>
  </form>;

  const authElem = isUserEmail
    ? <span>{signout_form} and sign in again</span>
    : <a href={'/signin'}><Translate defaultMessage="sign in" /></a>;

  return (!show ? null : <div className="vm-splash">
    <div onClick={cancel} />
    <section className="vm-voting">
      <div
        className="vm-c"
        key="desc"
        {...kd.render(desc)} />
      <p className="vm-d">{
        <Translate defaultMessage="Voting as {user} - {authLink} to change." values={{user, authLink: authElem}} />
      }</p>
      <form className="vm-form pure-form">
        <fieldset className="pure-group">
          <Button className="pure-button pure-button-primary" disabled={!loaded} onClick={start}><Translate defaultMessage="Start voting" /></Button>
          {' '}
          <Button className="pure-button" onClick={cancel}><Translate defaultMessage="Leave voting" /></Button>
        </fieldset>
      </form>
    </section>
  </div>);
}
