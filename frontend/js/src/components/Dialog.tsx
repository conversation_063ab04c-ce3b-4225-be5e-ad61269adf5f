// Basic Dialog component wrapper.
/** @jsx h */

import {h} from 'preact';
import {forwardRef} from 'preact/compat';
import {useRef} from 'preact/hooks';

import useCopyForwardRef from '../hooks/useCopyForwardRef';

import type {Attrs, ForwardRef, RNode} from '../types';


type DialogProps = Attrs['dialog'];
const Dialog = forwardRef((props: DialogProps, forwardRef: ForwardRef<HTMLDialogElement>): RNode => {
  const {children} = props;
  const ref = useRef<HTMLDialogElement>(null);
  const copyRef = useCopyForwardRef(forwardRef, ref);

  const onClick = (e: MouseEvent) => {
    const dialog = ref.current;
    if (!dialog) {
      return;
    }
    const rect = dialog.getBoundingClientRect();
    const isInDialog = (rect.top <= e.clientY && e.clientY <= rect.top + rect.height
      && rect.left <= e.clientX && e.clientX <= rect.left + rect.width);
    if (!isInDialog) {
      dialog.close();
    }
  };

  return <dialog {...props} onClick={onClick} ref={copyRef}>
    {children}
  </dialog>;
});

export default Dialog;
