// Component for editing a comment

/** @jsx h */

import {h} from 'preact';
import {forwardRef} from 'preact/compat';
import {useCallback, useEffect, useRef, useState} from 'preact/hooks';

import {ANNOTATION_CATEGORY_DEFAULT} from '../constants';
import {useCommentCategories} from '../context/annotation';
import {Translate} from '../intl';
import {_labelOf} from '../functions/labels';

import type {useCommenter} from '../context/annotation';
import type {Translator} from '../intl';
import type {ForwardRef, RNode, TargetedEvent} from '../types';
import Button from './Button';
import type {ExtendedComment} from './CommentsPanelFloating';

type CommentsMode = 'add'|'edit';

type CommentEditorProps = Readonly<{
  intl: Translator;
  disabled: boolean;
  iri: string;
  mode: CommentsMode;
  onClose: () => void;
  onSaveComment: ReturnType<typeof useCommenter>;
  hideCancel?: boolean;
  className?: string;
  headerText?: string;
  comment?: ExtendedComment;
}>;

// TODO: remove this and actually make the component simple
/* eslint-disable-next-line complexity */
const CommentEditor = forwardRef((props: CommentEditorProps, ref: ForwardRef<HTMLDivElement>) => {
  const {intl, disabled, iri, mode, onClose, onSaveComment, hideCancel, className, headerText, comment} = props;
  const textAreaRef = useRef<HTMLTextAreaElement>(null);
  const categories = useCommentCategories();
  const [body, setBody] = useState(comment?.body || '');
  const [category, setCategory] = useState(comment?.category?.iri
    || categories.find(cat => cat.iri === ANNOTATION_CATEGORY_DEFAULT)?.iri
    || categories[0].iri);

  const bodyTrimmed = body.trimEnd();

  const changeBody = useCallback(() => {
    if (textAreaRef.current) {
      setBody(textAreaRef.current.value);
    }
  }, []);

  const changeCategory = useCallback((e: TargetedEvent<HTMLSelectElement>) => {
    setCategory(e.currentTarget.value);
  }, []);

  const saveComment = useCallback(() => {
    onSaveComment(iri, bodyTrimmed, comment?.commentIri, comment?.created, category, true);
    onClose();
  }, [bodyTrimmed, category, comment, iri, onClose, onSaveComment]);

  const disableSubmit = !bodyTrimmed
    || bodyTrimmed === (comment?.body || '')
      && category === comment?.category?.iri;

  const showCommentType = categories.length > 1;

  useEffect(() => {
    if (textAreaRef.current) {
      textAreaRef.current.focus();
    }
  }, []);

  const getLabel = (mode: CommentsMode): RNode => {
    return mode === 'add' ? <Translate defaultMessage="Add comment:" /> : <Translate defaultMessage="Edit comment:" />;
  };

  return <div
    ref={ref}
    className={'comment-editor' + (className ? ` ${className}` : '') + (disabled ? ' disabled' : '')}>
    {headerText && <div className="header">
      <span>{headerText}</span>
    </div>}
    <label htmlFor={`comment-${_labelOf(iri)}`}>{getLabel(mode)}</label>
    <textarea
      id={`comment-${_labelOf(iri)}`}
      ref={textAreaRef}
      className="text-field"
      value={body}
      onKeyDown={(e) => e.stopPropagation()}
      placeholder={intl.translate({defaultMessage: 'Start typing...'})}
      onChange={changeBody}
    />
    {showCommentType && <div className="vm-input-row">
      <label><Translate defaultMessage="Comment Type" /></label>
      <select onChange={changeCategory}>
        {categories.map(cat => <option
          key={cat.iri}
          value={cat.iri}
          selected={cat.iri === category}>
          {cat.icon && <img src={cat.icon}></img>}
          {cat.name}
        </option>)}
      </select>
    </div>}
    <div className={`${mode}-mode-buttons`}>
      <Button
        className={'vm-confirm-button' + (disableSubmit ? ' disabled' : '')}
        onClick={saveComment}>
        {mode === 'add' && <Translate defaultMessage="Save comment" />}
        {mode === 'edit' && <Translate defaultMessage="Save" />}
      </Button>
      {!hideCancel && <Button
        className="vm-cancel-button"
        onClick={onClose}><Translate defaultMessage="Cancel" />
      </Button>}
    </div>
  </div>;
});

export default CommentEditor;
