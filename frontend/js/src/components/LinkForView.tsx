// Link to set the current view of the model
/** @jsx h */

import {h} from 'preact';
import {useCallback} from 'preact/hooks';

import {useNavigation} from '../context/place';
import Link from './Link';

import type {AnchorAttrs, Hint, RNode, TargetedEvent} from '../types';

export type Props = Readonly<Omit<AnchorAttrs, 'onClick'|'href'> & {
  iri: string;
  disabled?: boolean;
  hint?: Hint;
}>;

export default function LinkForView(props: Props): RNode {
  const {hint, iri, ...remainingProps} = props;
  const {hrefForView, setView} = useNavigation();

  const clickHandler = useCallback((e: TargetedEvent<HTMLAnchorElement>) => {
    const iri = e.currentTarget.getAttribute('data-iri');
    if (iri) {
      e.preventDefault();
      setView(iri, hint);
    }
  }, [hint, setView]);

  return <Link
    {...remainingProps}
    data-iri={iri}
    href={hrefForView(iri)}
    onClick={clickHandler}
  />;
}
