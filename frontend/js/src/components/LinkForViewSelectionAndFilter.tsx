// Link to set view, selection and filter
/** @jsx h */

import {h} from 'preact';
import {useCallback} from 'preact/hooks';

import {useFiltersToString, useNavigation} from '../context/place';

import Link from './Link';

import type {AnchorAttrs, Filters, Hint, RNode, TargetedEvent} from '../types';

export type Props = Readonly<Omit<AnchorAttrs, 'onClick'|'href'> & {
  select: string;
  view: string|null;
  filters: Filters;
  disabled?: boolean;
  hint?: Hint;
  onClick?: (e: TargetedEvent<HTMLAnchorElement, Event>) => void;
}>;

export default function LinkForViewSelectionAndFilter(props: Props): RNode {
  const {hint, select, view, filters, onClick, ...remainingProps} = props;
  const {hrefForSelection, hrefForView, hrefForFilters, setViewSelectionAndFilters} = useNavigation();
  const filtersString = useFiltersToString(filters, view);

  const clickHandler = useCallback((e: TargetedEvent<HTMLAnchorElement, Event>) => {
    e.preventDefault();
    setViewSelectionAndFilters(view, select, filters, hint);
    if (onClick) {
      onClick(e);
    }
  }, [select, view, filters, hint, onClick, setViewSelectionAndFilters]);

  return <Link
    {...remainingProps}
    href={hrefForView(view) + hrefForFilters(filtersString) + hrefForSelection(select)}
    onClick={clickHandler}
  />;
}
