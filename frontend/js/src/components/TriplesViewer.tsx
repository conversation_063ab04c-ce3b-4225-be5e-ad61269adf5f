// Triples viewer for a map
/** @jsx h */

import {Fragment, h} from 'preact';
import {useCallback, useEffect, useMemo, useRef, useState} from 'preact/hooks';

import {useTerms, useTermsSwitch} from '../context/terms';
import {useDebounce} from '../hooks/useDebounce';
import {Translate, useTranslation} from '../intl';
import {PREFIXES} from '../rdf';
import {defaultCompare} from '../sort';
import Button from './Button';
import FontMeasurer from './FontMeasurer';
import {LanguageSwitch} from './LanguageSwitch';
import VirtualList from './VirtualList';

import type {SupportedLocale} from '../context/intl';
import type {RawTerm} from '../context/terms';
import type {MapData, RNode, RefObject, TargetedEvent} from '../types';

type FieldKey = keyof RawTerm;
type SortDirection = 'asc'|'desc';
type Sort = {key: FieldKey; direction: SortDirection}[];
type Search = {[key in FieldKey]: string} & {global: string};

const FIELD_LIST: FieldKey[] = ['subj', 'pred', 'obj'];
const FIELD_NAMES: {[key in FieldKey]: string} = {subj: '', pred: '', obj: ''};
const FIELD_EXAMPLES: {[key in FieldKey]: string} = {
  subj: PREFIXES.vm + 'maps',
  pred: PREFIXES.vm + 'name',
  obj: 'about',
};

function useRealignWidth(
  elemRef: RefObject<HTMLTableSectionElement>,
  scrollRef: RefObject<HTMLTableSectionElement>,
): void {
  useEffect(() => {
    if (window.ResizeObserver && elemRef?.current && scrollRef?.current) {
      const observer = new ResizeObserver(() => {
        const scrollElement = scrollRef.current;
        const scrollbarWidth = (scrollElement?.offsetWidth || 0) - (scrollElement?.clientWidth || 0);
        if (scrollbarWidth > 0) {
          elemRef.current?.style.setProperty('width', `calc(100% - ${scrollbarWidth}px)`);
        } else {
          elemRef.current?.style.removeProperty('width');
        }
      });
      observer.observe(scrollRef.current);
      return () => observer.disconnect();
    }
    return undefined;
  }, [scrollRef, elemRef]);
}

function nextDirection(direction: SortDirection) {
  return direction === 'asc' ? 'desc' : 'asc';
}

function sortSymbol(direction: SortDirection, order: number, showOrder: boolean): RNode {
  const symbol = order === 1
    ? direction === 'asc' ? '\u25B2' : '\u25BC'
    : direction === 'asc' ? '\u25B3' : '\u25BD';

  return <span>{symbol}{showOrder ? <sup>{order}</sup> : null}</span>;
}

/**
 * Convert a value to a string.
 *
 * @param {unknown} value - The value to convert.
 * @returns {string} - The output of the value.
 */
const stringify = (value: unknown): string => (typeof value !== 'string' ? JSON.stringify(value) : value);

/**
 * Check if a given term exists within a content.
 *
 * @param {unknown} content - The given content to search within.
 * @param {string} term - The search term to look for. If an empty string is provided, the function returns `true`.
 * @returns {boolean} - Returns `true` if the term is found in the text, otherwise `false`.
 */
function simpleSearch(content: unknown, term: string): boolean {
  return !term || stringify(content).toLowerCase().includes(term.toLowerCase());
}

function FieldCell({value}: {value: string}): RNode {
  return <td title={value}><input value={value} readOnly={true} /></td>;
}

type TriplesViewerBodyProps = {
  scrollRef: RefObject<HTMLElement>;
  triples: RawTerm[];
};
function TripleRows({scrollRef, triples}: TriplesViewerBodyProps): RNode|null {
  // At the time of render, parent's ref still hasn't been assigned the HTMLElement.
  // Do it at the end, meaning additional render.
  const [fontSize, setFontSize] = useState(0);
  const [scrollElement, setScrollElement] = useState(scrollRef?.current);
  useEffect(() => setScrollElement(scrollRef?.current), [scrollRef]);

  const itemRenderer = useCallback((index: number) => {
    const t = triples[index];
    const rowindex = index + 2; // 1-based indexing and counting heading row
    return <tr key={index} aria-rowindex={rowindex} className={index % 2 ? 'o' : undefined}>
      <FieldCell value={t.subj} />
      <FieldCell value={t.pred} />
      <FieldCell value={t.obj} />
    </tr>;
  }, [triples]);

  const placeholderRenderer = useCallback((range: [number, number], height: number) => {
    return <tr key={range.map(x => `${triples[x].subj} ${triples[x].pred}`).join(' ')} style={{height}}></tr>;
  }, [triples]);

  const itemHeight = useCallback(() => fontSize * 1.5, [fontSize]);

  return <Fragment>
    {scrollElement && <VirtualList
      itemCount={triples.length}
      itemHeight={itemHeight}
      itemRenderer={itemRenderer}
      scrollElement={scrollElement}
      placeholderRenderer={placeholderRenderer}
    />}
    <FontMeasurer fontSize={'1em'} onResize={setFontSize} />
  </Fragment>;
}

type Props = {
  mapData: MapData;
  languages: string[];
  lang: string;
  setLang: (lang: SupportedLocale) => void;
};

/**
 * Component for sorting, filtering and displaying triples of a map model.
 * @param props Component properties
 * @param props.mapData Map data as data resource from map request.
 * @param props.languages Supported languages.
 * @param props.lang Selected language.
 * @param props.setLang Language setter.
 */
export default function TriplesViewer(props: Props): RNode {
  const terms = useTerms();
  const intl = useTranslation();
  const headRef = useRef<HTMLTableSectionElement>(null);
  const bodyRef = useRef<HTMLTableSectionElement>(null);
  const [search, setSearch] = useState<Search>({
    subj: '',
    pred: '',
    obj: '',
    global: '',
  });
  const [sort, setSort] = useState<Sort>([{key: 'subj', direction: 'asc'}]);
  const [expandHeader, setExpandHeader] = useState(false);
  const {activeReshape, switchReshape} = useTermsSwitch();

  useMemo(() => {
    FIELD_NAMES.subj = intl.translate({defaultMessage: 'Subject'});
    FIELD_NAMES.pred = intl.translate({defaultMessage: 'Predicate'});
    FIELD_NAMES.obj = intl.translate({defaultMessage: 'Object'});
  }, [intl]);

  const sorted = useMemo(() => {
    const result = terms.filter(t => (simpleSearch(t.subj, search.global)
      || simpleSearch(t.pred, search.global)
      || simpleSearch(t.obj, search.global))
        && simpleSearch(t.subj, search.subj)
        && simpleSearch(t.pred, search.pred)
        && simpleSearch(t.obj, search.obj));
    result.sort((a, b) => {
      for (const field of sort) {
        const cmp = defaultCompare(a[field.key], b[field.key]);
        if (cmp) {
          return field.direction === 'asc' ? cmp : -cmp;
        }
      }
      return 0;
    });
    return result;
  }, [terms, search, sort]);

  const {debouncedFunction: updateSearch} = useDebounce((search: string, prop: string) => {
    setSearch(prev => ({...prev, [prop]: search}));
  }, []);

  const changeSearch = useCallback((e: TargetedEvent<HTMLInputElement>, prop: keyof Search) => {
    updateSearch(e.currentTarget.value, prop);
  }, [updateSearch]);

  const toggleSort = useCallback((e: MouseEvent, key: FieldKey) => {
    setSort(prev => {
      if (!e.shiftKey) {
        return [{key, direction: prev[0].key === key ? nextDirection(prev[0].direction) : 'asc'}];
      }
      const index = prev.findIndex(o => o.key === key);
      switch (index) {
        case -1:
          return [...prev, {key, direction: 'asc'}];
        case 0:
          return prev;
        default:
          return [...prev.slice(0, index), {key: prev[index].key, direction: nextDirection(prev[index].direction)}];
      }
    });
  }, []);

  useRealignWidth(headRef, bodyRef);

  const fieldHeader = useCallback((key: FieldKey) => {
    const index = sort.findIndex(o => o.key === key);
    const symbol = index === -1 ? null : sortSymbol(sort[index].direction, index + 1, sort.length > 1);
    return <th key={key}>
      <Button onClick={(e) => toggleSort(e, key)}>
        <h4><span>{FIELD_NAMES[key]}</span>{symbol}</h4>
      </Button>
      <input
        hidden={!expandHeader}
        type="search"
        value={search[key]}
        placeholder={FIELD_EXAMPLES[key]}
        onChange={e => changeSearch(e, key)}
      />
    </th>;
  }, [changeSearch, expandHeader, search, sort, toggleSort]);

  const handleReshapeChange = (e: TargetedEvent<HTMLInputElement>) => {
    switchReshape(e.currentTarget.checked);
    if (bodyRef && bodyRef.current) {
      bodyRef.current.scrollTop = 0;
    }
  };

  const currentUrl = window.location.href;
  const modifiedUrl = currentUrl.replace('/mapterms/', '/maps/');

  return <div className="vm-triples">
    <div className="title"><a href={modifiedUrl}><Translate defaultMessage={'Map'} /> {'>'} {props.mapData.name}</a></div>
    <section className="triples-table">
      <div className="controls">
        <input
          type="search"
          className="search-global"
          placeholder={intl.translate({defaultMessage: 'Search'})}
          value={search.global}
          onChange={e => changeSearch(e, 'global')} />
        <label className="reshape">
          <span>{intl.translate({defaultMessage: 'Reshaped'})}:</span>
          <input
            type="checkbox"
            checked={activeReshape}
            onChange={handleReshapeChange}
          />
        </label>
        <LanguageSwitch
          setLang={props.setLang}
          lang={props.lang}
          languages={props.languages}
        />
      </div>
      <Button
        className="expand"
        aria-pressed={expandHeader}
        tooltipMessage={intl.translate({defaultMessage: 'Expand'})}
        onClick={() => setExpandHeader(v => !v)} />
      <table aria-rowcount={sorted.length}>
        <thead ref={headRef}>
          <tr aria-rowindex={1}>
            {FIELD_LIST.map(fieldHeader)}
          </tr>
        </thead>
        <tbody ref={bodyRef}>
          <TripleRows scrollRef={bodyRef} triples={sorted} />
        </tbody>
      </table>
      <span className="results">
        <Translate
          defaultMessage={'{count, plural, one {{count} result} other {{count} results}} total.'}
          values={{count: sorted.length}} />
      </span>
    </section>
  </div>;
}
