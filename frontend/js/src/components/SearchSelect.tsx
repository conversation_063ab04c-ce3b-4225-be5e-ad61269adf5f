// Provides a dropdown control with search
/** @jsx h */

import {h} from 'preact';

import {useCallback, useEffect, useRef, useState} from 'preact/hooks';

import {animateScroll} from '../functions/animateScroll';
import usePrevious from '../hooks/usePrevious';
import SVGArrowWide from '../svg/SVGArrowWide';
import Button from './Button';
import FontMeasurer from './FontMeasurer';
import ClearInputButton from './shared/ClearInputButton';
import VirtualList from './VirtualList';

import type {RNode, TargetedEvent} from '../types';

type SearchSelectItem = {
  ref?: HTMLLIElement | null;
  name: string;
  key: string;
};

type SearchSelectProps = Readonly<{
  onSelectChange: (key: string) => void;
  items: SearchSelectItem[];
  label: string;
  value?: string;
  onSelectOpen?: (e: HTMLElement) => void;
}>;

export default function SearchSelect(props: SearchSelectProps): RNode {
  const {label, items, onSelectChange, value, onSelectOpen} = props;

  const [expanded, setExpanded] = useState(false);
  const [search, setSearch] = useState('');
  const [filteredItems, setFilteredItems] = useState(items);
  const [focusedIndex, setFocusedIndex] = useState(items.findIndex(item => item.key === value));
  const [textStyle, setTextStyle] = useState('');
  const previousExpanded = usePrevious(expanded);

  const selectRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLUListElement>(null);

  const [fontSize, setFontSize] = useState(0);

  const MAX_ITEMS_SHOWN = 4;
  const itemHeight = fontSize * 1.5;

  useEffect(() => {
    const filtered = items.filter(item => search === '' || item.name.toLowerCase().includes(search.toLowerCase()));
    setFilteredItems(filtered);

    const newIndex = search ? 0 : items.findIndex(item => item.key === value);
    setFocusedIndex(newIndex);
  }, [items, search, value]);

  useEffect(() => {
    if (filteredItems.length > 0 && focusedIndex >= 0 && expanded) {
      filteredItems[focusedIndex].ref?.focus();
    }
    if (textStyle) {
      inputRef.current?.focus();
    }
  }, [focusedIndex, filteredItems, textStyle, expanded]);

  useEffect(() => {
    let handler: (e: MouseEvent) => void;
    if (expanded) {
      handler = (e: MouseEvent) => {
        if (e.target instanceof Node) {
          if (selectRef.current && !selectRef.current.contains(e.target) && e.target.textContent !== label + ':') {
            setExpanded(false);
            setSearch('');
          }
        }
      };
      document.addEventListener('pointerdown', handler);
    } else {
      setTextStyle('');
    }

    return () => handler && document.removeEventListener('pointerdown', handler);
  }, [expanded, label]);

  useEffect(() => {
    if (expanded) {
      if (onSelectOpen && selectRef.current) {
        onSelectOpen(selectRef.current);
      }
      if (!previousExpanded && listRef.current) {
        animateScroll({
          element: listRef.current,
          targetPosition: itemHeight * focusedIndex,
          duration: 500,
          stopAtApproximation: true,
        });
      }
    }
  }, [expanded, focusedIndex, itemHeight, onSelectOpen, previousExpanded]);

  const inputClickHandler = useCallback(() => {
    setExpanded(true);
    const newTextStyle = search ? 'hide' : 'light';
    setTextStyle(newTextStyle);
  }, [search]);

  const selectHandler = useCallback((e: TargetedEvent<HTMLLIElement, Event> | KeyboardEvent, item: SearchSelectItem) => {
    e.preventDefault();
    setExpanded(false);
    setSearch('');
    onSelectChange(item.key);
    setTextStyle('');
  }, [onSelectChange]);

  const inputKeyDownHandler = useCallback((e: KeyboardEvent) => {
    e.stopPropagation();
    if (e.key === 'ArrowUp') {
      e.preventDefault();
      setExpanded(true);
      if (focusedIndex > 0) {
        setFocusedIndex(focusedIndex - 1);
      }
      if (!search) {
        setTextStyle('light');
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      setExpanded(true);
      if (focusedIndex < filteredItems.length - 1) {
        setFocusedIndex(focusedIndex + 1);
      }
      if (!search) {
        setTextStyle('light');
      }
    } else if (e.key === 'Enter') {
      setExpanded(false);
      setTextStyle('');
      selectHandler(e, filteredItems[focusedIndex]);
    }
  }, [filteredItems, search, focusedIndex, selectHandler]);

  const searchHandler = useCallback((e:TargetedEvent<HTMLInputElement, Event>) => {
    const value = (e.target as HTMLInputElement).value;
    setSearch(value);
    setExpanded(true);
    const newTextStyle = value ? 'hide' : '';
    setTextStyle(newTextStyle);
  }, []);

  const buttonExpandHandler = useCallback(() => {
    setExpanded(v => !v);
    setSearch('');
  }, []);

  const renderRow = useCallback((index: number) => {
    const item = filteredItems[index];
    const isFocusedItem = focusedIndex === index;
    const keyDown = (e: KeyboardEvent) => {
      e.stopPropagation();
      if (e.target !== filteredItems[index].ref) {
        return;
      }
      if (e.key === 'Enter') {
        selectHandler(e, item);
      } else if (e.key === 'ArrowUp' && index > 0) {
        e.preventDefault();
        setFocusedIndex(index - 1);
      } else if (e.key === 'ArrowDown' && index < filteredItems.length - 1) {
        e.preventDefault();
        setFocusedIndex(index + 1);
      }
    };
    return <li
      key={item.key}
      id={item.name}
      ref={(elem) => {
        filteredItems[index].ref = elem;
      }}
      tabIndex={isFocusedItem ? 0 : -1}
      role="option"
      aria-current={isFocusedItem}
      aria-selected={isFocusedItem}
      onKeyDown={keyDown}
      onClick={(e) => selectHandler(e, item)}>
      {item.name}
    </li>;
  }, [filteredItems, focusedIndex, selectHandler]);

  const renderPlaceholder = useCallback((range: [number, number], height: number) => {
    return <li key={range.map(x => filteredItems[x].key).join(' ')} style={{height}}></li>;
  }, [filteredItems]);

  const heightFn = useCallback(() => itemHeight, [itemHeight]);

  const _toKebab = (str: string) => str.toLocaleLowerCase().split(' ').join('-');
  const selectedId = `select-${_toKebab(label)}`;
  const selectedValue = items.find(({key}) => key === value)?.name;
  const buttonLabel = `${label}: ${selectedValue} selected`;

  const clearSearchHandler = useCallback(() => {
    setSearch('');
  }, []);

  return <div className="vm-search-select">
    <label htmlFor={selectedId}>{label}:</label>
    <div
      className={'vm-search-select-area vm-clearable-input-container' + (expanded ? ' active' : '')}
      ref={selectRef}>
      <input
        ref={inputRef}
        id={selectedId}
        type="search"
        onClick={inputClickHandler}
        onKeyDown={(e) => inputKeyDownHandler(e)}
        onInput={searchHandler}
        value={search}
      />
      <div className={'text ' + textStyle} id={selectedId + '-value'}>
        {selectedValue}
      </div>
      <ClearInputButton
        onClearInput={clearSearchHandler}
        value={search}
      />
      <Button
        id={selectedId}
        aria-expanded={expanded}
        className={'vm-expand-button' + (expanded ? '' : ' flip')}
        onClick={buttonExpandHandler}
        role="combobox"
        aria-activedescendant={value}
        aria-label={buttonLabel}
      >
        <SVGArrowWide />
      </Button>
      <div className={'vm-search-select-dropdown' + (expanded ? ' active' : '')}>
        <ul
          ref={listRef}
          style={{height: expanded ? Math.min(filteredItems.length, MAX_ITEMS_SHOWN + 0.5) * itemHeight : 0}}
          className={'vm-dropdown-list'}
          tabIndex={-1}
          role="listbox"
        >
          {listRef.current && <VirtualList
            itemCount={filteredItems.length}
            itemHeight={heightFn}
            itemRenderer={renderRow}
            placeholderRenderer={renderPlaceholder}
            scrollElement={listRef.current}
          />}
          <FontMeasurer fontSize={'1em'} onResize={setFontSize} />
        </ul>
        <div className={'message' + (filteredItems.length ? ' hidden' : '')}>No results found.</div>
      </div>
    </div>
  </div>;
}
