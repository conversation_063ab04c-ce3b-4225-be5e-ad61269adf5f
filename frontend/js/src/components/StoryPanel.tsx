// Panel for story content
/** @jsx h */

import {Fragment, h} from 'preact';

import {useLayoutEffect, useRef, useState} from 'preact/hooks';
import Button from './Button';
import LinkForView from './LinkForView';
import {useMarkdown} from '../context/mark';
import {useModel} from '../context/triples';
import {usePlace} from '../context/place';
import {maybeMedia} from '../media';
import {useTranslation} from '../intl';
import SVGArrowWide from '../svg/SVGArrowWide';

import type {RNode} from '../types';

function count(move: ((v: string|null) => (string|null)), view: string|null): number {
  let c = 0;
  let v = view;
  while ((v = move(v))) {
    c++;
  }
  return c;
}

type StoryButtonProps = {
  iri: string|null;
  title: string;
  extraClassName?: string;
};
function LinkForStory(props: StoryButtonProps): RNode|null {
  const {iri, title, extraClassName} = props;
  const className = [
    'vm-story-link vm-toggle-button',
    extraClassName,
  ]
    .filter(Boolean)
    .join(' ');

  const disabled = !iri;

  return <LinkForView {...{iri: iri || '', title, disabled, className}}>
    <SVGArrowWide />
  </LinkForView>;
}

export default function StoryPanel(): RNode|null {
  const kd = useMarkdown();
  const mm = useModel();
  const {view} = usePlace();
  const {translate} = useTranslation();
  const [expand, setExpand] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    if (view && contentRef && contentRef.current) {
      contentRef.current.scrollTop = 0;
    }
  }, [view]);

  const isStorypoint = view != null && mm.ofStoryOf(view) != null;
  if (!isStorypoint) {
    return null;
  }

  const media = mm.contentItemOf(view);
  const next = mm.followedByOf(view);
  const prev = mm.storypointFollowedBy(view);
  const desc = mm.descriptionOf(view);

  const prevCount = count(v => mm.storypointFollowedBy(v), view);
  const nextCount = count(v => mm.followedByOf(v), view);
  const progressPercent = (prevCount + 1) / (prevCount + nextCount + 1) * 100;

  return (next || prev || desc) && (<Fragment>
    <div className="vm-story-controls">
      <LinkForStory
        iri={prev}
        title={translate({defaultMessage: 'previous'})}
      />
      <div className="vm-story-progress">
        <div style={{width: progressPercent + '%'}}></div>
      </div>
      <LinkForStory
        iri={next}
        title={translate({defaultMessage: 'next'})}
        extraClassName="flip"
      />
    </div>
    <div ref={contentRef} className={'vm-story-content vm-float-block-content' + (expand ? '' : ' collapse')} >
      <div className="content">
        <div
          className="vm-c"
          {...kd.render(desc || '')}
        />
        {media && maybeMedia(mm, media)}
      </div>
      <div className="vm-expand-controls">
        <Button
          className={'vm-expand-button' + (expand ? '' : ' flip')}
          tooltipMessage={expand ? translate({defaultMessage: 'Collapse'}) : translate({defaultMessage: 'Expand'})}
          onClick={() => setExpand(v => !v)}
        >
          <SVGArrowWide />
        </Button>
      </div>
    </div>
  </Fragment>);
}
