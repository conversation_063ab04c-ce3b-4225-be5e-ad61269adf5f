// Top level component for Shared Meaning map viewing application
//
// This is going to be a bit messy before it gets better.
/** @jsx h */

import {h} from 'preact';
import {Suspense, lazy} from 'preact/compat';
import {useEffect, useErrorBoundary, useState} from 'preact/hooks';

import {MarkdownProvider} from '../context/mark';
import {PlaceProvider} from '../context/placebase';
import PlatformProvider from '../context/platform';
import {UserProvider, resolveUser} from '../context/user';
import {getSettings} from '../functions/htmlData';
import {normalizePath, partitionURL} from '../functions/location';
import {logoutUser} from '../functions/logout';
import useTransitionlessResize from '../hooks/useTransitionlessResize';
import {useViewportHeight} from '../hooks/useViewportHeight';
import useWanna from '../hooks/useWanna';
import {loadMapData} from '../loadmap';
import ErrorReport from './ErrorReport';
import MaybeSkeleton from './Skeleton';

import 'ol/ol.css';
import 'purecss/build/pure-min.css';
import 'style/fonts.css';
import 'style/viewmap.css';

import type {RNode} from '../types';

const DEFAULT_API_USER = '/api/user';

const Live = lazy(() => import(/* webpackChunkName: "live" */'./Live'));

export default function App(): RNode {
  const [error] = useErrorBoundary();

  // Heroku does not normalise multiple slashes, causing Django to return a 404 before reaching this code.
  // Azure automatically normalises multiple slashes, allowing Django to route correctly.
  // In Azure's case, we manually enforce a redirect here to maintain consistency.
  normalizePath();

  useTransitionlessResize();

  const [baseUrl] = partitionURL('maps');
  const [settings] = useState(() => getSettings());

  useEffect(() => {
    if (!settings['expiry-date']) {
      return undefined;
    }

    const expiryDate = new Date(settings['expiry-date']);
    const now = new Date();
    const timeDiff = expiryDate.getTime() - now.getTime();

    if (timeDiff > 0) {
      const timeoutId = setTimeout(() => {
        logoutUser();
      }, timeDiff);

      return () => clearTimeout(timeoutId);
    }
    logoutUser();

    return undefined;
  }, [settings]);


  const userReader = useWanna(() => resolveUser(settings['me-url'] || DEFAULT_API_USER));
  const mapReader = useWanna(() => loadMapData(settings['api-root'], settings['map-iri']));

  useViewportHeight();

  if (error) {
    return <ErrorReport error={error} />;
  }

  return <MarkdownProvider>
    <PlaceProvider base={baseUrl}>
      <PlatformProvider>
        <MaybeSkeleton tilesSrc={settings['placeholder-src']} />
        <Suspense fallback={null}>
          <UserProvider userReader={userReader}>
            <Live {...{mapReader}} />
          </UserProvider>
        </Suspense>
      </PlatformProvider>
    </PlaceProvider>
  </MarkdownProvider>;
}
