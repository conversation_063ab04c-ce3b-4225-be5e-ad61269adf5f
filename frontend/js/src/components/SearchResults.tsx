// Flat search results
/** @jsx h */

import {h} from 'preact';
import {useCallback, useEffect, useMemo, useState} from 'preact/hooks';

import {envFromTerms, envFromUnicodeTerms} from 'markdown-it-marked';

import {PANEL_LABELS, Panel} from '../context/panels';
import {useFiltersPartialReset, useNavigation, usePlace} from '../context/place';
import {useSearchHistoryUpdater} from '../context/searchhistory';
import {useModel} from '../context/triples';
import {supportLookBehind} from '../functions/markdown';
import useExistOnView from '../hooks/useExistOnView';
import {useFilteredItemsByTerm} from '../hooks/useFilteredItems';
import {Translate, useTranslation} from '../intl';
import SVGCrosshair from '../svg/SVGCrosshair';
import SVGMagnifyingGlass from '../svg/SVGMagnifyingGlass';
import FontMeasurer from './FontMeasurer';
import LinkForSelect from './LinkForSelect';
import {SearchItemContent} from './SearchItemContent';
import VirtualList from './VirtualList';
import PanelHeader from './shared/PanelHeader';

import type {Keyed, RNode, RefCallback, RefObject} from '../types';

type Item = string;
type ItemGroup = {
  title?: RNode|string;
  items: Item[];
};
type RenderItem = {
  iri: string;
  ref: null|HTMLLIElement;
  groupSize: number;
  positionInGroup: number;
};
type RenderGroup = {
  title?: RNode|string;
  length: number;
  offsetIndex: number;
  offsetTop: number;
};

function toItem(iri: string): Item {
  return iri;
}

function toArray<T>(items: Keyed<T>): Item[] {
  return Object.keys(items).map(toItem);
}

export type Props = Readonly<{
  searchTerm: string;
  contentRef: RefObject<HTMLElement>;
  parentRef: RefObject<HTMLElement>;
  onClose: () => void;
  onItemSelected: () => void;
}>;
type SearchResultsComponentProps = Props & {
  itemGroups: ItemGroup[];
};
type SearchResultsListProps = SearchResultsComponentProps & {
  scroll: RefObject<HTMLElement>;
};

function SearchResultsList(props: SearchResultsListProps): RNode {
  const {itemGroups: itemGroupsInput, scroll, searchTerm, parentRef, onItemSelected} = props;
  const {isResettable: groupDisplay} = useFiltersPartialReset();

  const itemGroups = useMemo(() => groupDisplay
    ? itemGroupsInput
    : [{
        items: itemGroupsInput.reduce<string[]>((result, group) => {
          result.push(...group.items);
          return result;
        }, []),
      }],
  [itemGroupsInput, groupDisplay]);

  const mm = useModel();
  const {setSelection} = useNavigation();
  const {selection} = usePlace();
  const checkExistOnView = useExistOnView();
  const {push: pushHistory} = useSearchHistoryUpdater();

  const markEnv = useMemo(() => {
    const markdownEnv = supportLookBehind ? envFromUnicodeTerms : envFromTerms;
    return searchTerm ? markdownEnv(searchTerm.split(/\s+/g).filter(Boolean), {prefix: true}) : undefined;
  }, [searchTerm]);

  const [fontSize, setFontSize] = useState(0);

  const [focusedIndex, setFocusedIndex] = useState(0);

  const onItemClicked = useCallback((iri: string) => {
    pushHistory(
      {
        type: 'item',
        value: iri,
      }, {
        type: 'term',
        value: searchTerm,
      },
    );
    onItemSelected();
  }, [onItemSelected, pushHistory, searchTerm]);

  const itemHeight = fontSize * 2.75;
  const titleHeight = groupDisplay ? fontSize * 2.1875 : 0;
  const dividerHeight = groupDisplay ? fontSize : 0;
  const itemHeightFn = useCallback((_: number) => itemHeight, [itemHeight]);
  const renderGroups = useMemo<RenderGroup[]>(() => {
    const extended: RenderGroup[] = [];
    itemGroups.forEach((group, i) => {
      extended.push({
        title: group.title,
        length: group.items.length,
        offsetTop: i
          ? extended[i - 1].offsetTop + extended[i - 1].length * itemHeight + dividerHeight + titleHeight
          : titleHeight,
        offsetIndex: i ? extended[i - 1].offsetIndex + extended[i - 1].length : 0,
      });
    });
    return extended;
  }, [itemGroups, itemHeight, dividerHeight, titleHeight]);

  const renderItems = useMemo<RenderItem[]>(() => itemGroups.reduce<RenderItem[]>((result, group) => {
    group.items.forEach((item, itemIndex) => {
      result.push({
        iri: item,
        ref: null,
        groupSize: group.items.length,
        positionInGroup: itemIndex + 1,
      });
    });
    return result;
  }, []), [itemGroups]);

  const itemRenderer = useCallback((index: number, setItemRef: RefCallback<Element>) => {
    const {iri, positionInGroup, groupSize} = renderItems[index];
    // Add icon if item doesn't exist on current view but exists on other view
    const existOnView = checkExistOnView(iri);

    const keyDown = (e: KeyboardEvent) => {
      e.stopPropagation();
      if (e.target !== renderItems[index].ref) {
        return;
      }
      if (e.key === 'Enter') {
        e.preventDefault();
        setSelection(iri, {name: 'search'});
        onItemClicked(iri);
      } else if (e.key === 'Esc' || e.key === 'Escape') {
        e.preventDefault();
        parentRef.current?.focus();
      } else if (e.key === 'ArrowUp' && index > 0) {
        e.preventDefault();
        renderItems[index - 1].ref?.focus();
        setFocusedIndex(index - 1);
      } else if (e.key === 'ArrowDown' && index < renderItems.length - 1) {
        e.preventDefault();
        renderItems[index + 1].ref?.focus();
        setFocusedIndex(index + 1);
      }
    };
    return <li
      className="vm-search-item"
      ref={(elem) => {
        renderItems[index].ref = elem;
        setItemRef(elem);
      }}
      key={iri}
      tabIndex={focusedIndex === index ? 0 : -1}
      aria-setsize={groupSize}
      aria-posinset={positionInGroup}
      aria-current={selection === iri && 'location'}
      onKeyDown={keyDown}>
      <LinkForSelect hint={{name: 'search'}} iri={iri} tabIndex={-1} onClick={() => onItemClicked(iri)}>
        <SearchItemContent
          {...{mm, iri, markEnv}} />
        {existOnView && <div className="vm-focus-icon"><SVGCrosshair className="vm-svg vm-stroke-xsm" /></div>}
      </LinkForSelect>
    </li>;
  }, [renderItems, checkExistOnView, focusedIndex, selection, mm, markEnv, setSelection, onItemClicked, parentRef]);

  const placeholderRenderer = useCallback((range: [number, number], height: number) => {
    return <div key={range.map(x => renderItems[x].iri).join(' ')} style={{height}}></div>;
  }, [renderItems]);

  // ensures scroll element is already rendered, assuming it will not change
  const [scrollElement, setScrollElement] = useState(scroll?.current);
  useEffect(() => setScrollElement(scroll?.current), [scroll]);

  return renderItems.length
    && <div className="vm-search-result-list" tabIndex={-1}>
      {renderGroups.map((group, i) => <section
        aria-labelledby={`search-list-title-${i}`}
        className="vm-result-group"
        key={group.title}>
        {group.title && <header id={`search-list-title-${i}`} className="vm-line-clamp">
          {group.title}
        </header>}
        <ul className="vm-item-list" tabIndex={-1}>
          {scrollElement && <VirtualList
            itemCount={group.length}
            itemHeight={itemHeightFn}
            offsetTop={group.offsetTop}
            offsetIndex={group.offsetIndex}
            scrollElement={scrollElement}
            itemRenderer={itemRenderer}
            placeholderRenderer={placeholderRenderer}
          />}
        </ul>
      </section>)}
      <FontMeasurer fontSize={'1em'} onResize={setFontSize} />
    </div>
    || <p tabIndex={0}>
      <Translate defaultMessage="No results." />
    </p>;
}

function SearchResultsComponent(props: SearchResultsComponentProps): RNode {
  const {contentRef, onClose, itemGroups} = props;
  const intl = useTranslation();

  const total = itemGroups.reduce((sum, g) => sum + g.items.length, 0);

  return <section
    className="vm-float-block-section vm-search vm-search-results"
    aria-label={PANEL_LABELS[Panel.SearchResults]}>
    <section ref={contentRef} className="vm-float-block-content">
      <SearchResultsList {...props} scroll={contentRef} />
    </section>
    <PanelHeader
      icon={<SVGMagnifyingGlass />}
      title={intl.translate({defaultMessage: 'Search Results ({count})'}, {count: total})}
      panel={Panel.SearchResults}
      skipFocus={true}
      onClose={onClose} />
  </section>;
}

export default function SearchResults(props: Props): RNode {
  const {searchTerm} = props;
  const {items: filtered, remainingSearchedItems} = useFilteredItemsByTerm(searchTerm);

  const items = useMemo(() => {
    const filteredItems = toArray(filtered);
    const remainingItems = toArray(remainingSearchedItems);
    return [
      {
        title: <Translate
          defaultMessage= "{count, plural, one {Result} other {Results}} in current filters ({count})"
          values={{count: filteredItems.length}} />,
        items: filteredItems,
      },
      {
        title: <Translate defaultMessage= "{count, plural, one {Result} other {Results}} outside current filters ({count})" values={{count: remainingItems.length}} />,
        items: remainingItems,
      },
    ];
  }, [filtered, remainingSearchedItems]);

  return <SearchResultsComponent
    {...props}
    itemGroups={items}
  />;
}
