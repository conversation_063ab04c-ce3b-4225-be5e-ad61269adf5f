// Display of activity involvement
/** @jsx h */

import {h} from 'preact';
import {useMemo} from 'preact/hooks';

import ObjectLink from './ObjectLink';
import {useModel} from '../context/triples';
import {getInvolvementRelations} from '../labelledrelations';

import type {Hint, RNode} from '../types';
import type {MapModel} from '../rdfmodels';
import type {Involvement} from '../labelledrelations';

function describeInvolvement(mm: MapModel, {inv, label, other}: Involvement, hint?: Hint): RNode {
  return <p key={inv}>{label} {
    /* Here `other` should exist. With bad data involvement may dangle. */
    other
      ? <ObjectLink value={other} hint={hint} />
      : <em>{mm.className(inv)}</em>
  }</p>;
}

type Props = Readonly<{
  iri: string;
  hint?: Hint;
}>;

export function MaybeInvolvements({iri, hint}: Props): RNode|null {
  const mm = useModel();
  const involvements = useMemo(() => getInvolvementRelations(mm, iri), [mm, iri]);

  return involvements.length && <div className="vm-d">
    {involvements.map(inv => describeInvolvement(mm, inv, hint))}
  </div> || null;
}
