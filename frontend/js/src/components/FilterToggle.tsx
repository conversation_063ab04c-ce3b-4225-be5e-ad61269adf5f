// Toggles filters

/** @jsx h */
import {Fragment, h} from 'preact';

import {ClassIcon} from '../icons';
import {useTranslation} from '../intl';
import SVGCheckmark from '../svg/SVGCheckmark';
import SVGMinus from '../svg/SVGMinus';
import SVGPlus from '../svg/SVGPlus';
import Button from './Button';

import type {RNode, TargetedEvent} from '../types';
import TooltipWrapper from './shared/TooltipWrapper';

type ToggleIconProps = {
  binary?: boolean;
};
// Provides three icons to switch between.
function ToggleIcon({binary = false}: ToggleIconProps): RNode {
  // TODO: Might think of a better way to toggle between states, to not have all div in the DOM
  const iconClass = 'vm-toggle-icon' + (binary ? ' binary' : '');
  return (<Fragment>
    <div className={`${iconClass} vm-icon-add`}><SVGPlus /></div>
    <div className={`${iconClass} vm-icon-on`}><SVGCheckmark /></div>
    <div className={`${iconClass} vm-icon-remove`}><SVGMinus /></div>
  </Fragment>
  );
}

type FilterToggleProps = {
  iri: string;
  name: string;
  isSelected: boolean;
  onToggle: (iri: string) => void;
  onFocus?: (e: TargetedEvent<HTMLButtonElement>) => void;
  onPointerDown?: () => void;
  noTooltip?: boolean;
  compact?: boolean;
  showClass?: boolean;
  binaryIcon?: boolean;
};
export default function FilterToggle(props: FilterToggleProps): RNode {
  const {
    iri,
    name,
    isSelected,
    onToggle,
    onFocus,
    onPointerDown,
    noTooltip = false,
    compact = false,
    showClass = false,
    binaryIcon = false,
  } = props;
  const {translate} = useTranslation();
  const handleClick = (e: TargetedEvent<HTMLButtonElement, Event>) => {
    e.preventDefault();
    onToggle(iri);
  };

  const tooltip = noTooltip
    ? undefined
    : isSelected ? translate({defaultMessage: 'Remove filter'}) : translate({defaultMessage: 'Add filter'});

  // Switching states of icon is cared of in CSS.
  return <TooltipWrapper position="bottom" tooltipMessage={tooltip || 'Add Filter'}><Button
    className="vm-filter-toggle vm-toggle-button"
    aria-pressed={isSelected}
    tooltipMessage={tooltip}
    onFocus={onFocus}
    onPointerDown={onPointerDown}
    onClick={handleClick}>
    {showClass && <ClassIcon iri={iri} />}
    {!compact && <span className="label">{name}</span>}
    <ToggleIcon binary={binaryIcon} />
  </Button>
  </TooltipWrapper>;
}
