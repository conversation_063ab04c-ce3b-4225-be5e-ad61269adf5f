// Navigation for listing and setting current view
/** @jsx h */

import {h} from 'preact';
import {useCallback, useEffect, useMemo, useRef} from 'preact/hooks';

import LinkForView from './LinkForView';
import {useMarkdown} from '../context/mark';
import {useMatchingView, useNavigation, usePlace, useViewUrlMatcher} from '../context/place';
import {useModel} from '../context/triples';
import {Translate} from '../intl';

import type {MapModel} from '../rdfmodels';
import type {ComponentChildren, RNode} from '../types';

function shouldShowView(iri: string): boolean {
  return !iri.endsWith('/introduction');
}

function storyItem(mm: MapModel, iri: string): string|RNode {
  const name = mm.nameOf(iri);
  const storypoint = mm.firstPointOf(iri);
  if (storypoint == null) {
    return name;
  }
  return <LinkForView iri={storypoint}>
    {name}
  </LinkForView>;
}

function OldIntroView(): RNode {
  const [isIntro, introView] = useMatchingView('introduction');

  return <h4 aria-current={isIntro && 'page'}>
    <LinkForView iri={introView}><Translate defaultMessage="About this map" /></LinkForView>
  </h4>;
}

type Props = Readonly<{children: ComponentChildren; useAboutUsLink?: boolean; onMenuItemClick?: () => void}>;

function MarkdownViewList({content, children, useAboutUsLink, onMenuItemClick}: Props & {content: string}): RNode {
  const md = useMarkdown();
  const {setRelativeUrl} = useNavigation();
  const divRef = useRef<HTMLDivElement>(null);
  const viewMatcher = useViewUrlMatcher();

  useEffect(() => {
    // TODO: Pass context to md to avoid direct DOM operations
    const div = divRef.current;
    if (div) {
      div.querySelectorAll<HTMLAnchorElement>('.vm-h a').forEach(a => {
        if (viewMatcher(a.href)) {
          a.setAttribute('aria-current', 'page');
        } else {
          a.removeAttribute('aria-current');
        }
      });
    }
  }, [viewMatcher]);

  const navigateViewStory = useCallback((e: MouseEvent) => {
    const targetHref = (e.target as HTMLElement|undefined)?.closest<HTMLAnchorElement>('.vm-h a')?.getAttribute('href');
    if (targetHref?.startsWith('/')) {
      e.preventDefault();
      setRelativeUrl(targetHref);
      onMenuItemClick?.();
    }
  }, [onMenuItemClick, setRelativeUrl]);

  const mdProps = useMemo(() => md.render(content), [content, md]);

  return <div className="vm-learn" onClick={navigateViewStory} ref={divRef}>
    {useAboutUsLink && <OldIntroView />}
    <div
      className="vm-h"
      {...mdProps} />
    {children}
  </div>;
}

function DefaultViewList({children, useAboutUsLink, onMenuItemClick}: Props): RNode {
  const mm = useModel();
  const {view} = usePlace();
  const current = view ? mm.ofStoryOf(view) || view : null;

  return <div className="vm-learn">
    {useAboutUsLink && <OldIntroView />}
    {
      mm.stories().map((iri: string) => (
        <h4 key={iri} aria-current={iri === current && 'page'} onClick={onMenuItemClick}>
          {storyItem(mm, iri)}
        </h4>
      ))
    }{
      mm.views().filter(shouldShowView).map((iri: string) => (
        <h4 key={iri} aria-current={iri === current && 'page'} onClick={onMenuItemClick}>
          <LinkForView iri={iri}>
            {mm.nameOf(iri) || '?'}
          </LinkForView>
        </h4>
      ))
    }{
      children
    }
  </div>;
}

export default function ViewList({children, useAboutUsLink = false, onMenuItemClick}: Props): RNode {
  const mm = useModel();
  const customContent = mm.navContent();

  return customContent
    ? <MarkdownViewList content={customContent} useAboutUsLink={useAboutUsLink} onMenuItemClick={onMenuItemClick}>{children}</MarkdownViewList>
    : <DefaultViewList useAboutUsLink={useAboutUsLink} onMenuItemClick={onMenuItemClick}>{children}</DefaultViewList>;
}
