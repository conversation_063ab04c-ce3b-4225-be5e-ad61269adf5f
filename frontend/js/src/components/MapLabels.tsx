// Rendering of dynamic map labels
/** @jsx h */

import {h} from 'preact';
import {useCallback, useEffect, useMemo, useRef, useState} from 'preact/hooks';

import Layer from 'ol/layer/Layer';

import {MAP_SIZE, layerTransform, translateCoord, useMap} from '../context/ol';
import {hrefFromIri, usePlace} from '../context/place';
import {usePlatformStates} from '../context/platform';
import {useModel} from '../context/triples';
import flag from '../flags';
import {makeJudgementResolver, makeScoreResolver} from '../functions/colorResolver';
import {useCurrentLabels} from '../hooks/labels';
import useFilteredItems from '../hooks/useFilteredItems';
import {IRIS} from '../rdf';
import {partitionSubj} from '../triplejump';
import {resolveFont} from './LoadFonts';
import VirtualMapItems from './VirtualMapItems';

import type {Coord} from '../context/ol';
import type {LabelForViewInfo} from '../hooks/labels';
import type {MapModel} from '../rdfmodels';
import type {RNode} from '../types';
import type {FontProperties} from './LoadFonts';

// Multiplier for spacing of lines, should vary by font really but not by much.
const LINE_HEIGHT = 1.2;
// Any visible label should have some area, even with no text content.
const MIN_LABEL_WIDTH = 3;
// This is a fools-constant as will vary so much by font and letters used.
const ROUGH_FONT_CHAR_WIDTH = 0.6;

type LabelDisplay = {
  text: string;
  background: string|null;
  color: string|null;
  fontFamily: string;
  fontSize: number;
  box?: [number, number, number, number];
};

type LabelDisplayExtended = LabelDisplay & {
  width: number;
  height: number;
  font: FontProperties;
  faded: boolean;
};

function resolveColor(color: string|null): string|null {
  if (color == null || color === 'transparent') {
    return color;
  }
  const hexcolor = color.trim();
  if (!/^#[0-9A-Fa-f]{3,8}$/.test(hexcolor)) {
    return null;
  }
  const digitCount = hexcolor.length - 1;
  if (digitCount % 3 !== 0 && digitCount % 4 !== 0) {
    return null;
  }
  return hexcolor;
}

function parseLabelDisplay(mm: MapModel, label: LabelForViewInfo): LabelDisplay|null {
  const display = mm.parseDisplay(label.labelIRI);
  if (display != null) {
    return display;
  }
  const {
    [IRIS.VM.description]: text,
    [IRIS.VM.boxColor]: background,
    [IRIS.VM.fontColor]: color = '#555',
    [IRIS.VM.fontFamily]: fontFamily,
    [IRIS.VM.fontSize]: fontSize,
    [IRIS.VM.boxBounds]: rawBox,
  } = partitionSubj(mm, label.labelIRI, {})[0];
  if (!rawBox) {
    return null;
  }
  return {text, background, color, fontFamily, fontSize, box: JSON.parse(rawBox)};
}

function getLabelSize(scale: number, text: string, fontSize: number, box?: number[]) {
  if (box) {
    const height = Math.abs(box[2] - box[0]) * scale;
    const width = Math.abs(box[3] - box[1]) * scale;
    return {width, height};
  }
  // TODO: This is approximate but unvalidated logic
  const lines = text.split(/\n/);
  const height = scale * lines.length * fontSize * LINE_HEIGHT;
  const width = scale * lines.reduce((currentWidth, line) => Math.max(
    currentWidth,
    line.length * fontSize * ROUGH_FONT_CHAR_WIDTH,
  ), MIN_LABEL_WIDTH * fontSize);
  return {width, height};
}

function lineOffset(boxHeight: number, lineCount: number, i: number, lineHeight: number): number {
  const scaledHeight = lineHeight * boxHeight;
  const lineOffset = scaledHeight / (lineCount + 1);
  const centerOffset = (scaledHeight - boxHeight) / 2;
  return lineOffset * (i + 1) - centerOffset;
}

function disabledHandler(e: MouseEvent) {
  e.preventDefault();
}

type MapLabelProps = {
  scale: number;
  point: Coord;
  iri: string;
  isSelected: boolean;
  isAbsent: boolean;
  display: LabelDisplayExtended;
};

function MapLabel({scale, point, iri, isSelected, isAbsent, display}: MapLabelProps): RNode {
  const {text, background, color, font, fontSize, faded, width, height} = display;
  const translateX = point[1] * scale - width / 2;
  const translateY = -point[0] * scale - height / 2;

  const fontSizeScaled = fontSize * scale;
  const borderRadius = fontSizeScaled * 0.2;
  const transparent = background === 'transparent';

  const classList = ['vm-map-label'];
  if (transparent) {
    // Label authored without a background so can't have hover effects etc
    classList.push('inlaid');
  } else if (!iri) {
    // Label doesn't relate to anything in the model, an authoring error
    classList.push('invalid');
  }
  if (isSelected) {
    // Label relates to the currently selected thing in the model
    classList.push('selected');
  }
  if (faded) {
    // Label will have colour removed for the current view
    classList.push('faded');
  }
  if (isAbsent) {
    // Label relates to an item not in the current filters, so de-emphasise
    classList.push('absent');
  }

  const svgtext = (
    <text
      fill={color !== background ? (color || '#000') : '#A55'}
      fontSize={fontSizeScaled}
      textRendering="geometricPrecision"
      fontFamily={font.fontFamily}
      fontStyle={font.fontStyle}
      fontWeight={font.fontWeight}
    >
      {text.split(/\n/).map((line, i, lines) => {
        return <tspan
          key={i}
          x={width / 2}
          y={lineOffset(height, lines.length, i, LINE_HEIGHT)}
          textAnchor="middle"
          dy="0.35em"
        >{line}</tspan>;
      })}
    </text>
  );
  const content = transparent ? svgtext : (
    <g className="content" strokeWidth={0}>
      <rect
        width={width}
        height={height}
        rx={borderRadius}
        ry={borderRadius}
        fill={background || 'transparent'}
        strokeWidth={fontSizeScaled * 0.1}
      />
      {svgtext}
    </g>
  );

  if (!iri) {
    return <g
      className={classList.join(' ')}
      transform={`translate(${translateX} ${translateY})`}
    >
      {content}
    </g>;
  }
  return <g
    transform={`translate(${translateX} ${translateY})`}
  >
    <a
      className={classList.join(' ')}
      data-iri={iri}
      href={'#' + hrefFromIri(iri)}
      onClick={disabledHandler}
      onDragStart={disabledHandler}
      tabindex={-1}
    >
      {!transparent && <rect
        className="shadow"
        width={width}
        height={height}
        rx={borderRadius}
        ry={borderRadius}
        strokeWidth={0}
      />}
      {content}
    </a>
  </g>;
}

export type Props = {
  /*
   * Constant factor by which dimensions are multiplied when rendering labels.
   * Seems to matter for two reasons:
   *   - Font scaling is clamped to limited precision
   *   - Safari (incorrectly) uses this for intermediate buffer sizes
   * Note that making this too large blows out graphics card memory, so there
   * is a tradeoff between fidelity and the browser not crashing hardware.
   * Previously this was a compile time constant, but big maps currently need
   * bigger values, and we may want to be clever later with detail levels.
   */
  scaleFactor: number;
};

export default function MapLabels({scaleFactor}: Props): RNode {
  const {map} = useMap();
  const mm = useModel();
  const {items} = useFilteredItems();
  const {selection, view} = usePlace();
  const labels = useCurrentLabels();

  const isRAGView = mm.hasRAGData(view);
  const isGrayscale = mm.isGrayscale(view);

  const {base: baseReady} = usePlatformStates();

  const fillResolver = useMemo(() => {
    const scoreResolver = makeScoreResolver(mm);
    const ragResolver = isRAGView
      ? makeJudgementResolver(mm, 'default')
      : () => null;

    return (iri: string) => ragResolver(iri) ?? scoreResolver(iri);
  }, [mm, isRAGView]);

  const getLabelDisplay = useCallback((label: LabelForViewInfo): null|LabelDisplayExtended => {
    const display = parseLabelDisplay(mm, label);
    if (display == null) {
      return null;
    }

    let color = resolveColor(display.color);
    if (display.color == null || display.color !== color) {
      console.warn('Label: invalid color', `'${display.color}'`, label.targetIRI);
    }
    let background = resolveColor(display.background);
    if (display.background == null || display.background !== background) {
      console.warn('Label: invalid background', `'${display.background}'`, label.targetIRI);
    }

    const fillOverride = fillResolver(label.targetIRI);
    if (fillOverride) {
      background = fillOverride;
      color = '#fff';
    }

    const faded = isGrayscale && !fillOverride;
    const font = resolveFont(display.fontFamily);
    if (!font.resolved) {
      console.warn('Label: invalid font', `'${display.fontFamily}'`, label.targetIRI);
    }
    const {width, height} = getLabelSize(scaleFactor, display.text, display.fontSize, display.box);

    return {...display, width, height, font, color, background, faded};
  }, [mm, scaleFactor, isGrayscale, fillResolver]);

  const container = useRef<HTMLDivElement>(null);
  const layerElement = useRef<SVGSVGElement>(null);
  const blurElement = useRef<SVGFEGaussianBlurElement>(null);

  const sizeAttr = MAP_SIZE * scaleFactor;

  const absentEnabled = flag(mm, 'absentLabels');
  const isAbsent = useCallback((iri: string) => {
    return absentEnabled && !!iri && !(iri in items);
  }, [absentEnabled, items]);

  // stringified ViewportTarget for less unnecessary updates
  const [target, setTarget] = useState<string|null>(null);

  useEffect(() => {
    if (!baseReady) {
      return () => {};
    }
    const layer = new Layer({
      render(frameState) {
        const {x, y, scale} = layerTransform(frameState, scaleFactor);

        if (frameState.extent) {
          setTarget(JSON.stringify({extent: frameState.extent, scale}));
        }

        if (layerElement.current) {
          layerElement.current.style.transformOrigin = 'top left';
          layerElement.current.style.transform = `translate(${x}px, ${y}px) scale(${scale})`;
          layerElement.current.style.setProperty('--label-hover-offset', (7 / scale) + 'px');
        }

        if (blurElement.current) {
          blurElement.current.setAttribute('stdDeviation', (1.5 / scale).toString());
        }

        return container.current ?? document.createElement('div');
      },
      zIndex: 5,
    });
    map.addLayer(layer);

    return () => {
      map.removeLayer(layer);
    };
  }, [baseReady, map, scaleFactor]);

  const labelNodes = useMemo(() => {
    return labels.map(label => {
      const display = getLabelDisplay(label);
      const [x, y] = translateCoord(label.point);
      if (!display) {
        return {
          node: null,
          coords: [x, y],
        };
      }
      const dx = display.width / 2 / scaleFactor;
      const dy = display.height / 2 / scaleFactor;
      const coords = [x - dx, y - dy, x + dx, y + dy];
      return {
        node: <MapLabel
          key={label.labelIRI}
          scale={scaleFactor}
          point={label.point}
          iri={label.targetIRI}
          isSelected={selection === label.targetIRI}
          isAbsent={isAbsent(label.targetIRI)}
          display={display}
        />,
        coords,
      };
    });
  }, [labels, selection, scaleFactor, getLabelDisplay, isAbsent]);

  return <div style={{display: 'none'}}>
    <div ref={container} style={{position: 'absolute'}}>
      <svg
        ref={layerElement}
        className="svg-layer vm-labels"
        viewBox={`0 0 ${sizeAttr} ${sizeAttr}`}
        width={sizeAttr}
        height={sizeAttr}
      >
        <filter
          id="label-shadow-blur"
          x="-50%"
          y="-50%"
          width="200%"
          height="200%"
        >
          <feGaussianBlur ref={blurElement} />
        </filter>
        <filter id="label-grayscale">
          <feColorMatrix type="saturate" values="0" />
        </filter>
        <g>
          <VirtualMapItems items={labelNodes} targetString={target} />
        </g>
      </svg>
    </div>
  </div>;
}
