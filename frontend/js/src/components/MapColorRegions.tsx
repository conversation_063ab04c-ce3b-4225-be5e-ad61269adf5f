// Rendering of map colored regions
/** @jsx h */

import {h} from 'preact';
import {useEffect, useMemo, useRef} from 'preact/hooks';

import Layer from 'ol/layer/Layer';

import {MAP_SIZE, layerTransform, useMap} from '../context/ol';
import {usePlace} from '../context/place';
import {useModel} from '../context/triples';
import {makeCategoryResolver, makeJudgementResolver} from '../functions/colorResolver';
import {buildArea} from '../functions/svgpaths';
import {useAreasByLens} from '../hooks/geolocations';
import useFilteredItems from '../hooks/useFilteredItems';
import {lensFromLocation} from '../triplejump';

import type {RNode} from '../types';

export default function MapColorRegions(): RNode {
  const {map} = useMap();
  const mm = useModel();
  const {view, lensLocation} = usePlace();
  const areasByLens = useAreasByLens();
  const {items} = useFilteredItems();

  const container = useRef<HTMLDivElement>(null);
  const layerElement = useRef<SVGSVGElement>(null);

  const hasRAGData = mm.hasRAGData(view);
  const colorCategoryTypes = mm.forColorCategoriesOf(view);
  const hasColorCategories = !!colorCategoryTypes && Object.keys(colorCategoryTypes).length > 0;
  const hasColorData = hasRAGData || hasColorCategories;

  useEffect(() => {
    if (!hasColorData) {
      return undefined;
    }

    const layer = new Layer({
      render(frameState) {
        const {x, y, scale} = layerTransform(frameState);

        if (layerElement.current) {
          layerElement.current.style.transformOrigin = 'top left';
          layerElement.current.style.transform = `translate(${x}px, ${y}px) scale(${scale})`;
        }

        return container.current ?? document.createElement('div');
      },
      zIndex: 4,
    });
    map.addLayer(layer);
    return () => {
      map.removeLayer(layer);
    };
  }, [map, hasColorData]);

  const fillResolver = useMemo(() => {
    const categoryColorResolver = makeCategoryResolver(mm, colorCategoryTypes);
    const categoryResolver = hasColorCategories
      ? (iri: string) => items[iri] && categoryColorResolver(iri) || null
      : () => null;
    const judgementResolver = hasRAGData ? makeJudgementResolver(mm, 'overlay') : () => null;

    return (iri: string) => categoryResolver(iri) ?? judgementResolver(iri);
  }, [colorCategoryTypes, hasColorCategories, hasRAGData, items, mm]);

  const lens = lensFromLocation(mm, lensLocation);
  const areas = areasByLens[lens];
  const polyNodes = useMemo(() => {
    return Object.entries(areas || {}).map(([iri, poly]) => {
      const fillColor = fillResolver(iri);
      if (fillColor) {
        return <path key={iri} transform="matrix(0 -1 1 0 0 0)" d={buildArea(poly)} fill={fillColor} />;
      }
      return null;
    });
  }, [areas, fillResolver]);

  return <div style={{display: 'none'}}>
    <div ref={container} style={{position: 'absolute'}}>
      <svg
        ref={layerElement}
        className="svg-layer"
        viewBox={`0 0 ${MAP_SIZE} ${MAP_SIZE}`}
        width={MAP_SIZE}
        height={MAP_SIZE}
      >
        {polyNodes}
      </svg>
    </div>
  </div>;
}
