// Debug rendering of map content
/** @jsx h */

import {h} from 'preact';
import {useEffect, useRef} from 'preact/hooks';

import Layer from 'ol/layer/Layer';

import {useGeekery} from '../context/geek';
import {MAP_SIZE, layerTransform, useMap} from '../context/ol';
import {usePlace} from '../context/place';
import {useModel} from '../context/triples';
import {classes} from '../functions/html';
import {buildAreaOrRect, buildLines} from '../functions/svgpaths';
import {useAreasByLens, useDataPathsByLens} from '../hooks/geolocations';
import {useLabelsAtCurrentStack} from '../hooks/labels';
import {buildLabelPaths} from '../hooks/useMapItems';
import {lensFromLocation} from '../triplejump';

import type {Coord} from '../context/ol';
import type {RNode} from '../types';

function isProperArea(area: string|Coord[]): area is string {
  // Old point array paths and simple rectangles are improper
  return typeof area === 'string' && area.split(',').length > 6;
}

export default function MapGeek(): RNode {
  const {map} = useMap();
  const {selection, view, lensLocation} = usePlace();
  const mm = useModel();
  const {showBounds, showPink, showAllAreas, showSelectionArea} = useGeekery();
  const labels = useLabelsAtCurrentStack();
  const dataPaths = useDataPathsByLens();
  const areasByLens = useAreasByLens();

  const container = useRef<HTMLDivElement>(null);
  const layerElement = useRef<SVGSVGElement>(null);

  const elems: RNode[] = [];

  // Draw in view bounding boxes (for eg. story points)
  if (showBounds && view) {
    const minPoint: Coord|null = mm.minGeoPointOf(view);
    const maxPoint: Coord|null = mm.maxGeoPointOf(view);
    if (minPoint && maxPoint) {
      elems.push(<path fill="#aada" transform="matrix(0 -1 1 0 0 0)" d={buildAreaOrRect([minPoint, maxPoint])} />);
    }
  }

  // Display of all areas simultaniously as per future categorical visualisations
  if (showAllAreas && labels) {
    const lens = lensFromLocation(mm, lensLocation);
    const areas = Object.values(areasByLens[lens] || []).filter(isProperArea);
    const pathElems: RNode[] = [];
    const fac = Math.max(10, 360 / areas.length);
    let hue = 170;
    for (const d of areas) {
      const fill = `oklch(75% 0.12 ${hue = (hue + fac) % 360} / 0.4)`;
      pathElems.push(<path fill={fill} d={d} />);
    }
    elems.push(<g className="vm-debug-rainbow-areas" transform="matrix(0 -1 1 0 0 0)">{pathElems}</g>);
  }

  // Preview of area selection visualisation
  if (showSelectionArea && selection) {
    const lens = lensFromLocation(mm, lensLocation);
    const area = areasByLens[lens]?.[selection];
    if (area) {
      const d = `M0,0 H${-1 * MAP_SIZE} V${MAP_SIZE} H0 Z ${buildAreaOrRect(area)}`;
      elems.push(<path fill="#0002" fillRule="evenodd" transform="matrix(0 -1 1 0 0 0)" d={d} />);
    }
  }

  // Draw in 'data collection lines' or pink lines, in pink!
  if (showPink) {
    const pathMap = buildLabelPaths(mm, labels, dataPaths);
    const pathElems: RNode[] = [];
    for (const iri in pathMap) {
      pathMap[iri].forEach(({path, infer: inferred}) => {
        const d = buildLines(path);
        pathElems.push(<path key={iri} d={d} {...classes({inferred, selected: selection === iri})} />);
      });
    }
    elems.push(<g className="vm-debug-pink" fill="none" transform="matrix(0 -1 1 0 0 0)">{pathElems}</g>);
  }

  // TODO: This is mostly copy/paste from MapColorRegions, let's make a better way.
  useEffect(() => {
    const layer = new Layer({
      render(frameState) {
        const {x, y, scale} = layerTransform(frameState);

        if (layerElement.current) {
          layerElement.current.style.transformOrigin = 'top left';
          layerElement.current.style.transform = `translate(${x}px, ${y}px) scale(${scale})`;
          layerElement.current.style.setProperty('--unscaled-width', (1 / scale) + 'px');
        }

        return container.current ?? document.createElement('div');
      },
      zIndex: 4,
    });
    map.addLayer(layer);
    return () => {
      map.removeLayer(layer);
    };
  }, [map]);

  return <div style={{display: 'none'}}>
    <div ref={container} style={{position: 'absolute'}}>
      <svg
        className="svg-layer"
        ref={layerElement}
        viewBox={`0 0 ${MAP_SIZE} ${MAP_SIZE}`}
        width={MAP_SIZE}
        height={MAP_SIZE}
      >
        {elems}
      </svg>
    </div>
  </div>;
}
