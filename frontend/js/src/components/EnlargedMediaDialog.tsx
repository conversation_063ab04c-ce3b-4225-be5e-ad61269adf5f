// Enlarged dialog component for displaying media.

/** @jsx h */
import {Fragment, h} from 'preact';

import {useCallback, useLayoutEffect, useMemo, useRef, useState} from 'preact/hooks';

import {PANEL_LABELS, Panel, usePanel, usePanelUpdaters} from '../context/panels';
import {classes} from '../functions/html';
import {Translate} from '../intl';
import SVGPopup from '../svg/SVGPopup';
import SVGResize from '../svg/SVGResize';
import Button from './Button';
import Dialog from './Dialog';
import PanelHeader from './shared/PanelHeader';

import type {ComponentChildren, RNode} from '../types';

/*
  Sometimes the google doc viewer does not load, so iframe also does not load anything.
  We need to trigger reloads by reloading the iframe until it is.
  Should be handled when src changes or switching between SIP and modal.
*/
export function refreshIframe(container: Element|null|undefined): number|undefined {
  if (!container) {
    return undefined;
  }

  container.querySelector<HTMLIFrameElement>('iframe')?.classList?.toggle('loaded', false);

  const iframeRefreshInterval = setInterval(() => {
    const iframe = container?.querySelector<HTMLIFrameElement>('iframe:not(.loaded)');
    if (!iframe) {
      clearInterval(iframeRefreshInterval);
      return;
    }
    iframe.src += '';
  }, 3000);

  return iframeRefreshInterval;
}

function isDialogSupported(): boolean {
  return typeof HTMLDialogElement === 'function';
}

/*
  A media dialog served as container for WithEnlargedDialog to mount media content
  for enlarged isolated view.
*/
export function MediaDialog(): RNode | null {
  const {changeOpen, setElementRef} = usePanelUpdaters(Panel.Media);
  const {open, elementRef: dialogRef} = usePanel<HTMLDialogElement>(Panel.Media);
  const hasDialog = isDialogSupported();

  useMemo(() => {
    if (open) {
      dialogRef.current?.showModal();
    }
  }, [open, dialogRef]);

  const onClose = useCallback(() => {
    changeOpen(false);
  }, [changeOpen]);

  return hasDialog && <Dialog
    ref={setElementRef}
    aria-label={PANEL_LABELS[Panel.Media]}
    className="vm-media-dialog vm-float-block"
    onClose={onClose}>
    <PanelHeader panel={Panel.Media} onClose={() => dialogRef.current?.close()} />
  </Dialog> || null;
}

type EnlargeControlsProps = {
  onEnlargeClick: () => void;
  mediaType?: string;
};
function EnlargeControls({onEnlargeClick, mediaType}: EnlargeControlsProps): RNode {
  return <div className="vm-enlarge-controls">
    <Button
      onClick={onEnlargeClick}
    >
      {mediaType === 'image'
        && <Fragment><span><Translate defaultMessage="Enlarge" /></span><SVGResize /></Fragment>
        || <Fragment><span><Translate defaultMessage="Enlarge Document" /></span><SVGPopup /></Fragment>}
    </Button>
  </div>;
}

type WithEnlargeProps = {
  children: ComponentChildren;
  mediaType: string;
};

/**
 * A wrapper for detaching media and moving to an isolated dialog container.
 *
 * @param {string} props.mediaType Media type
 */
export function WithEnlargedDialog({children, mediaType}: WithEnlargeProps): RNode {
  const containerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const [enlarged, setEnlarged] = useState<boolean>(false);
  const {changeOpen} = usePanelUpdaters(Panel.Media);
  const {elementRef: dialogRef} = usePanel(Panel.Media);

  const onEnlargeClick = useCallback(() => {
    changeOpen(true, {onClose: () => setEnlarged(false)});
    setEnlarged(true);
  }, [changeOpen]);

  const hasDialog = isDialogSupported();

  useLayoutEffect(() => {
    if (!contentRef.current) {
      return () => {};
    }

    if (enlarged) {
      dialogRef.current?.append(contentRef.current);
    } else {
      containerRef.current?.prepend(contentRef.current);
    }

    const timeout = refreshIframe(contentRef.current);
    return () => clearInterval(timeout);
  }, [enlarged, dialogRef]);

  return <div className="vm-enlarge">
    <div ref={containerRef} className="vm-enlarge-content-container">
      <div ref={contentRef} {...classes({}, 'vm-enlarge-content', mediaType)} >
        {children}
      </div>
    </div>
    {hasDialog && <Fragment>
      <EnlargeControls onEnlargeClick={onEnlargeClick} mediaType={mediaType} />
    </Fragment>}
  </div>;
}
