// Placement of active filtered items in map
/** @jsx h */

import {h} from 'preact';
import {useCallback, useContext, useEffect, useMemo, useRef, useState} from 'preact/hooks';

import Layer from 'ol/layer/Layer';

import AggregateMarker from '../components/AggregateMarker';
import RAGColorizeFilter, {useRAGFilter} from '../components/RAGColorizeFilter';
import VirtualMapItems from '../components/VirtualMapItems';
import {useAnnotations} from '../context/annotation';
import {AssetsContext} from '../context/assets';
import {useHighlightedItems} from '../context/focus';
import {layerTransform, translateCoord, useMap} from '../context/ol';
import {usePlace} from '../context/place';
import {usePlatformStates} from '../context/platform';
import {useModel} from '../context/triples';
import useMapItems from '../hooks/useMapItems';
import {iconFor, iconForRegion} from '../icons';
import {IRIS} from '../rdf';

import type {Coord} from '../context/ol';
import type {FallbackIcon} from '../icons';
import type {RNode} from '../types';

const OPACITY_UNFOCUSED = 0.2;

export type MarkerFocus = 'default'|'focused'|'unfocused';

type MarkerProps = Readonly<{
  iri: string;
  icon: string|null;
  iconFilter: string|null;
  opacity: number|null;
  layout: 'none'|'left'|'right'|null;
  name: string;
  nameIcon: FallbackIcon;
  position: Coord;
  votes: number;
  faded: boolean;
  focus: MarkerFocus;
}>;

const EXTENT_PADDING = [-150, -35, -35, -150];

export function getOpacityStyles(opacity: number|null, focus: MarkerFocus): {visibility?: string; opacity?: number} {
  if (opacity === 0) {
    return {visibility: 'hidden'};
  }
  if (opacity === null || opacity < 0 || opacity > 100) {
    return focus === 'unfocused' ? {opacity: OPACITY_UNFOCUSED} : {};
  }
  const usedOpacity = focus === 'unfocused' ? OPACITY_UNFOCUSED * opacity : opacity;
  return {opacity: usedOpacity / 100};
}

function Marker(props: MarkerProps): RNode|null {
  const {iri, icon, iconFilter, opacity, layout, faded, focus, name, nameIcon, position, votes} = props;

  const posStyle = {
    ['--markerX']: (position[1]) + 'px',
    ['--markerY']: (-position[0]) + 'px',
  };

  const opacityStyle = getOpacityStyles(opacity, focus);

  const style = Object.assign({}, posStyle, opacityStyle);

  const brightness = opacity != null ? (1 + 0.4 * (-1 * (opacity / 100) + 1)) : null;

  const backgroundImage = icon != null ? 'url("' + icon + '")' : 'var(--icon-unknown-marker)';
  const filter = (iconFilter ?? '') + (brightness != null ? ` brightness(${brightness})` : '');
  const iconStyle = {
    backgroundImage,
    filter,
  };

  const classList = ['vm-marker'];
  if (layout != null) {
    classList.push(layout);
  }
  if (icon == null) {
    classList.push('unknown');
  }
  if (faded) {
    classList.push('faded');
  }
  if (focus === 'focused') {
    classList.push('focused');
  }

  return <div
    className={classList.join(' ')}
    data-iri={iri} data-votes={votes || undefined}
    style={style}
  >
    <span className="vm-marker-icon-container">
      <span className="vm-marker-icon" style={iconStyle}></span>
    </span>
    {name && <p>
      {nameIcon && (typeof nameIcon === 'string'
        ? <span>{nameIcon}</span>
        : <span
            className={nameIcon.className}
            style={{backgroundImage: nameIcon.backgroundImage}}
          ></span>
      )}
      {nameIcon ? ' ' : ''}
      {name}
    </p>}
  </div>;
}

export type Props = Readonly<{
  showVotes: boolean;
}>;

export default function MapItems(props: Props): RNode {
  const {showVotes} = props;
  const {byItem} = useAnnotations();
  const assets = useContext(AssetsContext);
  const {view, selection} = usePlace();
  const {map} = useMap();
  const mm = useModel();
  const mapItems = useMapItems();
  const {mapIris} = useHighlightedItems();

  const {base: baseReady} = usePlatformStates();

  const getNameIcon = useCallback((iri: string): FallbackIcon => {
    const region = mm._getField(iri, IRIS.VM.inRegion);
    if (region) {
      return iconForRegion(mm, region);
    }
    return '';
  }, [mm]);

  const {getIconFilter} = useRAGFilter();

  const getOpacity = useCallback((iri: string) => {
    const impact = mm.hasImpact(iri);
    if (!impact) {
      return null;
    }

    const opacity = Number(mm.opacityOf(impact));
    if (isNaN(opacity)) {
      return null;
    }

    return opacity;
  }, [mm]);

  // stringified ViewportTarget for less unnecessary updates
  const [target, setTarget] = useState<string|null>(null);

  const container = useRef<HTMLDivElement>(null);
  const layerElement = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!baseReady) {
      return () => {};
    }
    const layer = new Layer({
      render(frameState) {
        const {x, y, scale} = layerTransform(frameState);

        if (frameState.extent) {
          setTarget(JSON.stringify({extent: frameState.extent, scale}));
        }

        if (layerElement.current) {
          layerElement.current.style.setProperty('--layerX', x + 'px');
          layerElement.current.style.setProperty('--layerY', y + 'px');
          layerElement.current.style.setProperty('--layerScale', scale.toString());
        }

        return container.current ?? document.createElement('div');
      },
      zIndex: 6,
    });
    map.addLayer(layer);

    return () => {
      map.removeLayer(layer);
    };
  }, [baseReady, map]);

  const isRAGView = mm.hasRAGData(view);
  const isGrayscale = mm.isGrayscale(view);

  const itemNodes = useMemo(() => {
    const evaluateFocus = (iri: string): MarkerProps['focus'] => {
      if (mapItems.focused[iri]) {
        return 'focused';
      }
      if (mapIris.length) {
        if (mapIris.includes(iri)) {
          return 'focused';
        }
        return 'unfocused';
      }
      return 'default';
    };
    return mapItems.all.map(({info, point}) => {
      if (info.kind === 'item') {
        const {iri, name} = info;
        const iconFilter = isRAGView ? getIconFilter(iri) : null;
        return {
          node: <Marker
            key={`${iri} ${point[0]} ${point[1]}`}
            iri={iri}
            name={name}
            nameIcon={getNameIcon(iri)}
            icon={iconFor(mm, assets, iri, mm.classOf(iri))}
            iconFilter={iconFilter}
            opacity={getOpacity(iri)}
            layout={mm.aspectOf(iri)}
            position={point}
            votes={showVotes ? byItem[iri]?.votes : 0}
            faded={isGrayscale && !iconFilter}
            focus={evaluateFocus(iri)}
          />,
          coords: translateCoord(point),
        };
      }
      return {
        node: <AggregateMarker
          key={`${info.icon} ${point[0]} ${point[1]}`}
          {...info}
          position={point}
          opacity={null}
          focus={selection !== null && mapIris.includes(selection) ? 'unfocused' : 'default'}
        />,
        coords: translateCoord(point),
      };
    });
  }, [mapItems, mapIris, selection, isRAGView, getIconFilter, getNameIcon, mm, assets, getOpacity, showVotes, byItem, isGrayscale]);

  return <div style={{display: 'none'}}>
    <div ref={container} style={{position: 'absolute'}}>
      <RAGColorizeFilter />
      <div ref={layerElement}>
        <VirtualMapItems items={itemNodes} targetString={target} padding={EXTENT_PADDING} />
      </div>
    </div>
  </div>;
}
