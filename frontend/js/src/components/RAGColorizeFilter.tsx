// SVG filter to colorize icon in RAG colors
/** @jsx h */

import {h} from 'preact';
import {useCallback} from 'preact/hooks';

import {useModel} from '../context/triples';
import {getRGB} from '../functions/colorResolver';
import {IRIS} from '../rdf';

import type {RNode} from '../types';

type FilterFunctions = {
  getIconFilter: (iri: string) => string|null;
};

enum RAGFilterId {
  RED = 'rag-colorize-r',
  AMBER = 'rag-colorize-a',
  GREEN = 'rag-colorize-g',
}

const judgementToFilter = {
  [IRIS.VM.judgedNegative]: `url(#${RAGFilterId.RED})`,
  [IRIS.VM.judgedNeutral]: `url(#${RAGFilterId.AMBER})`,
  [IRIS.VM.judgedPositive]: `url(#${RAGFilterId.GREEN})`,
};

const idToCSSVar = {
  [RAGFilterId.RED]: '--color-rag-negative-rgb',
  [RAGFilterId.AMBER]: '--color-rag-neutral-rgb',
  [RAGFilterId.GREEN]: '--color-rag-positive-rgb',
};

export default function RAGColorizeFilter(): RNode {
  const idToColor = Object.entries(idToCSSVar)
    .map(([id, cssVar]) => ({id, color: getRGB(cssVar)}));

  // Allow some shades of grey for antialiasing
  const feFuncValues = '0 0 0.1 0.2 0.4 1 1';

  return <svg width="0" height="0">
    {idToColor.map(({id, color}) => <filter key={id} id={id}>
      <feComponentTransfer result="f1">
        <feFuncR type="table" tableValues={feFuncValues} />
        <feFuncG type="table" tableValues={feFuncValues} />
        <feFuncB type="table" tableValues={feFuncValues} />
      </feComponentTransfer>
      <feColorMatrix
        in="f1"
        type="matrix"
        // eslint-disable-next-line react/no-unknown-property
        color-interpolation-filters="sRGB"
        values={`
          1 0 0 0 ${color.r / 255}
          0 1 0 0 ${color.g / 255}
          0 0 1 0 ${color.b / 255}
          0 0 0 1 0
        `}
      />
    </filter>)}
  </svg>;
}

export function useRAGFilter(): FilterFunctions {
  const mm = useModel();

  const getIconFilter = useCallback((iri: string) => {
    const categories = mm.categoriesOf(iri);
    if (categories) {
      for (const category in judgementToFilter) {
        if (categories[category]) {
          return judgementToFilter[category];
        }
      }
    }

    return null;
  }, [mm]);

  return {getIconFilter};
}
