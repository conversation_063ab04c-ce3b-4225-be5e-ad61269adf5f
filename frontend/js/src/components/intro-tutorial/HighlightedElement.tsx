/** @jsx h */
import {h} from 'preact';
import type {RNode} from '../../types';

interface HighlightedElementProps {
  targetRect: DOMRect;
  stepId: string;
}

export default function HighlightedElement({targetRect, stepId}: HighlightedElementProps): RNode {
  return (
    <div
      className="vm-intro-highlight"
      style={{
        top: targetRect.top + window.scrollY,
        left: targetRect.left + window.scrollX,
        width: targetRect.width,
        height: targetRect.height,
      }}
      onClick={(e) => e.preventDefault()}
      data-tutorial-step={stepId}
    />
  );
}
