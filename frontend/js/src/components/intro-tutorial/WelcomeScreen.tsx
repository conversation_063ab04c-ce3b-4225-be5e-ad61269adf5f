/** @jsx h */
import {h} from 'preact';
import {useEffect, useRef, useState} from 'preact/hooks';

import Button from '../Button';
import {Translate, useTranslation} from '../../intl';

import type {RNode} from '../../types';

interface WelcomeScreenProps {
  isVisible: boolean;
  isLargeScreen: boolean;
  onExitLater: () => void;
  onExit: () => void;
  onStart: () => void;
}

export default function WelcomeScreen({
  isVisible,
  isLargeScreen,
  onExitLater,
  onExit,
  onStart,
}: WelcomeScreenProps): RNode {
  const intl = useTranslation();
  const [showA11yMessage, setShowA11yMessage] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  // Use effect to delay showing the accessibility message and focus the welcome dialog
  useEffect(() => {
    if (isVisible) {
      setShowA11yMessage(false);

      // Set a timeout for showing the message after the component is fully rendered
      const messageTimer = setTimeout(() => {
        setShowA11yMessage(true);
      }, 100);

      // Focus the welcome dialog after it becomes visible
      const focusTimer = setTimeout(() => {
        if (contentRef.current) {
          contentRef.current.focus();
        }
      }, 50);

      return () => {
        clearTimeout(messageTimer);
        clearTimeout(focusTimer);
      };
    }
    return undefined;
  }, [isVisible]);

  return (
    <div className="vm-intro-welcome" style={{opacity: isVisible ? 1 : 0, padding: isLargeScreen ? '20px' : '0px'}}>
      <div
        ref={contentRef}
        className="vm-intro-welcome-content"
        tabIndex={-1}
        aria-label={intl.translate({defaultMessage: 'Tutorial Welcome Screen'})}
        style={{
          transform: isVisible ? 'translateY(0)' : 'translateY(20px)',
          opacity: isVisible ? 1 : 0,
        }}
      >
        <h2 className="vm-intro-welcome-title">
          <Translate defaultMessage="Welcome! 👋" />
        </h2>
        <p className="vm-intro-welcome-text">
          <Translate defaultMessage="This is a quick tutorial to get you familiar with all the awesome features." />
        </p>
        {/* Screen reader only accessibility instructions */}
        <div className="vm-sr-only" aria-live="polite">
          {showA11yMessage
            ? <Translate
                defaultMessage="Accessibility Note: Screen reader users can navigate through the tutorial using left and right arrow keys. You may need to switch your screen reader to Forms or Focus mode to enable keyboard navigation."
              /> : ''}
        </div>
        <div className="vm-intro-welcome-buttons">
          <Button
            className="vm-primary-button vm-toggle-button"
            onClick={onExitLater}
            data-testid="intro-tutorial-remind-later-button"
          >
            <Translate defaultMessage="Remind me later" />
          </Button>
          <Button
            className="vm-primary-button vm-toggle-button"
            onClick={onExit}
            data-testid="intro-tutorial-skip-button"
          >
            <Translate defaultMessage="Skip Tutorial" />
          </Button>
          <Button
            className="vm-primary-button vm-toggle-button"
            onClick={onStart}
            data-testid="intro-tutorial-start-button"
          >
            <Translate defaultMessage="Start tutorial" />
          </Button>
        </div>
      </div>
    </div>
  );
}
