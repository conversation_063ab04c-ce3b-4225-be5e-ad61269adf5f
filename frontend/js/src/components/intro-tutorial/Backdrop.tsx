/** @jsx h */
import {Fragment, h} from 'preact';
import type {RNode} from '../../types';

interface BackdropProps {
  targetRect: DOMRect | null;
  isCongratulationsScreen: boolean;
}

/**
 * Creates a semi-transparent backdrop that highlights a specific element on the screen.
 *
 * This component has two modes:
 * 1. Congratulations screen mode: A single full-screen backdrop
 * 2. Element highlighting mode: Four backdrop elements that create a "cutout" effect
 *
 * In element highlighting mode, the component creates four separate backdrop divs that
 * cover the entire screen except for the target element. These four divs represent:
 * - Top section: Covers everything above the target element
 * - Left section: Covers the area to the left of the target element
 * - Bottom section: Covers everything below the target element
 * - Right section: Covers the area to the right of the target element
 *
 * This approach creates a "spotlight" effect where only the target element is visible
 * through the semi-transparent backdrop, drawing the user's attention to it.
 *
 * @param {Object} props - The component props
 * @param {DOMRect | null} props.targetRect - The bounding rectangle of the element being highlighted
 * @param {boolean} props.isCongratulationsScreen - Whether to show the congratulations screen backdrop
 * @returns {RNode} The rendered backdrop elements
 */
export default function Backdrop({
  targetRect,
  isCongratulationsScreen,
}: BackdropProps): RNode {
  if (isCongratulationsScreen) {
    // Single semi-transparent backdrop for congratulations screen
    return (
      <div
        className="vm-intro-backdrop vm-intro-backdrop-congrats"
        style={{
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
        }}
      />
    );
  }

  // Create four backdrop elements that together cover the entire screen except for the target element
  // This creates a "cutout" effect where only the target element is visible
  return (
    <Fragment>
      {/* Top backdrop - covers everything above the target element */}
      <div
        className="vm-intro-backdrop"
        style={{
          top: 0,
          left: 0,
          width: '100%',
          height: targetRect ? `${targetRect.top + window.scrollY}px` : '100%',
        }}
      />
      {/* Left backdrop - covers the area to the left of the target element */}
      <div
        className="vm-intro-backdrop"
        style={{
          top: targetRect ? `${targetRect.top + window.scrollY}px` : '0',
          left: 0,
          width: `${targetRect?.left ?? 0}px`,
          height: `${targetRect?.height ?? 0}px`,
        }}
      />
      {/* Bottom backdrop - covers everything below the target element */}
      <div
        className="vm-intro-backdrop"
        style={{
          top: targetRect ? `${targetRect.bottom + window.scrollY}px` : '0',
          left: 0,
          width: '100%',
          height: targetRect ? `calc(100vh - ${targetRect.bottom + window.scrollY}px)` : '100%',
        }}
      />
      {/* Right backdrop - covers the area to the right of the target element */}
      <div
        className="vm-intro-backdrop"
        style={{
          top: targetRect ? `${targetRect.top + window.scrollY}px` : '0',
          left: `${(targetRect?.right ?? 0)}px`,
          width: targetRect ? `calc(100vw - ${targetRect.right}px)` : '100%',
          height: `${targetRect?.height ?? 0}px`,
        }}
      />
    </Fragment>
  );
}
