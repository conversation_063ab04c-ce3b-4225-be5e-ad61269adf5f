/** @jsx h */
import {h} from 'preact';
import {useEffect, useRef} from 'preact/hooks';

import Button from '../Button';
import {Translate} from '../../intl';

import type {RNode} from '../../types';

interface CongratulationsDialogProps {
  onComplete: () => void;
}

/**
 * Congratulations dialog shown at the end of the tutorial.
 *
 * @param {CongratulationsDialogProps} props - Component props
 * @returns {RNode} The rendered dialog
 */
export default function CongratulationsDialog({
  onComplete,
}: CongratulationsDialogProps): RNode {
  const contentRef = useRef<HTMLDivElement>(null);

  // Focus the button directly when the dialog appears
  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.focus();
    }
  }, [contentRef]);

  return (
    <div
      className="vm-intro-congrats"
      ref={contentRef}
      tabIndex={-1}
    >
      <h2 className="vm-intro-congrats-title">
        <Translate defaultMessage="🎉 Congratulations! 🎉" />
      </h2>
      <p className="vm-intro-congrats-text">
        <Translate defaultMessage="You have successfully completed the tutorial. Enjoy exploring this mapping project!" />
      </p>
      <div className="vm-intro-congrats-button-container">
        <Button
          className="vm-primary-button vm-toggle-button"
          onClick={onComplete}
          data-testid="intro-tutorial-complete-button"
        >
          <Translate defaultMessage="Let's go!" />
        </Button>
      </div>
    </div>
  );
}
