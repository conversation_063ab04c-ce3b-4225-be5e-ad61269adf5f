/** @jsx h */
import {h} from 'preact';

import {useScreenProperties} from '../../context/screen';

import type {RNode} from '../../types';
import type {CSSProperties} from './types';


export default function ConfettiEffect(): RNode {
  const {isLargeScreen} = useScreenProperties();

  return (
    <div className="vm-intro-confetti-container">
      {Array.from({length: isLargeScreen ? 500 : 200}).map((_, i) => {
        // Randomize shape, size, and rotation
        const shape = Math.random() > 0.66 ? 'circle' : Math.random() > 0.5 ? 'square' : 'triangle';
        const size = Math.random() * 15 + 10; // Random size between 10px and 25px
        const rotation = Math.random() * 360; // Random initial rotation
        const opacity = Math.random() * 0.5 + 0.5; // Random opacity between 0.5 and 1

        const shapeStyles: CSSProperties = {
          position: 'absolute',
          left: `${Math.random() * 100}%`,
          top: '-20px',
          width: `${size}px`,
          height: `${size}px`,
          background: `hsl(${Math.random() * 360}, 100%, ${50 + Math.random() * 20}%)`,
          opacity,
          animation: `confetti ${1 + Math.random() * 1}s linear forwards`,
          animationDelay: `${Math.random() * 0.5}s`,
          transform: `rotate(${rotation}deg)`,
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        };

        if (shape === 'circle') {
          shapeStyles.borderRadius = '50%';
        } else if (shape === 'triangle') {
          shapeStyles.width = 0;
          shapeStyles.height = 0;
          shapeStyles.borderLeft = `${size / 2}px solid transparent`;
          shapeStyles.borderRight = `${size / 2}px solid transparent`;
          shapeStyles.borderBottom = `${size}px solid hsl(${Math.random() * 360}, 100%, ${50 + Math.random() * 20}%)`;
          shapeStyles.background = 'none';
        }

        return <div key={i} style={shapeStyles} />;
      })}
    </div>
  );
}
