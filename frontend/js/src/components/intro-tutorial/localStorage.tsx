/** @jsx h */

/**
 * Current tutorial version - increment this when making significant changes to the tutorial
 * that would require users to see it again. This version is compared with the stored version
 * to determine if the tutorial should be shown again after updates.
 *
 * @constant {string}
 */
export const TUTORIAL_VERSION = '1.0.0';
export const PLATFORM_STATE_KEY = 'platformState';

/**
 * Interface representing the platform state stored in localStorage.
 * Contains tutorial completion status and version information, as well as
 * allowing for additional properties to be stored for future use.
 *
 * @interface
 * @property {boolean} [tutorialDone] - Whether the user has completed the tutorial
 * @property {string} [tutorialVersion] - The version of the tutorial the user has completed
 * @property {any} [key: string] - Additional properties for future platform state needs
 */
export interface PlatformState {
  tutorialDone?: boolean;
  tutorialVersion?: string;
  [key: string]: boolean | string | number | null | undefined;
}

/**
 * Retrieves the current platform state from localStorage.
 *
 * This function attempts to read and parse the platform state from localStorage.
 * If the state doesn't exist or can't be parsed (e.g., due to invalid JSON),
 * it returns an empty object as the default state.
 *
 * @returns {PlatformState} The current platform state object, or an empty object if not found or on error
 */
export const getPlatformState = (): PlatformState => {
  try {
    const stateStr = localStorage.getItem(PLATFORM_STATE_KEY);
    return stateStr ? JSON.parse(stateStr) : {};
  } catch (e) {
    console.error('Error parsing platform state from localStorage:', e);
    return {};
  }
};

/**
 * Saves the provided platform state to localStorage.
 *
 * This function serializes the state object to JSON and stores it in localStorage
 * under the PLATFORM_STATE_KEY. It handles potential errors that might occur during
 * the save operation (e.g., localStorage being full or disabled).
 *
 * @param {PlatformState} state - The platform state object to save
 * @returns {void}
 */
export const savePlatformState = (state: PlatformState): void => {
  try {
    localStorage.setItem(PLATFORM_STATE_KEY, JSON.stringify(state));
  } catch (e) {
    console.error('Error saving platform state to localStorage:', e);
  }
};

/**
 * Determines whether the tutorial should be shown to the user based on localStorage state.
 *
 * This function checks two conditions:
 * 1. If the tutorial has never been completed (tutorialDone is falsy)
 * 2. If the tutorial version has changed since the user last completed it
 *
 * The tutorial will be shown if either condition is true, ensuring users see
 * the tutorial on their first visit and when significant updates are made.
 *
 * @returns {boolean} True if the tutorial should be shown, false otherwise
 */
export const shouldShowTutorial = (): boolean => {
  const state = getPlatformState();
  // Show tutorial if it's never been completed or if the version has changed
  return !state.tutorialDone || state.tutorialVersion !== TUTORIAL_VERSION;
};

/**
 * Marks the tutorial as completed in localStorage.
 *
 * This function updates the platform state to indicate that the user has completed
 * the current version of the tutorial. It preserves all other platform state values
 * while updating the tutorial completion status and version.
 *
 * @returns {void}
 */
export const localStorageTutorialCompleted = (): void => {
  const state = getPlatformState();
  savePlatformState({
    ...state,
    tutorialDone: true,
    tutorialVersion: TUTORIAL_VERSION,
  });
};

/**
 * Marks the tutorial as not completed in localStorage.
 *
 * This function updates the platform state to indicate that the user has not completed
 * the tutorial, allowing it to be shown again in the future. It preserves all other
 * platform state values while updating the tutorial completion status.
 *
 * @returns {void}
 */
export const localStorageTutorialNotCompleted = (): void => {
  const state = getPlatformState();
  savePlatformState({
    ...state,
    tutorialDone: false,
  });
};
