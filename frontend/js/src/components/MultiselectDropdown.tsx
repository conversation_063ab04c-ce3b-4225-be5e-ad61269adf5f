// Provides a dropdown control with search and multiple item selection
/** @jsx h */
import {h} from 'preact';
import {useCallback, useEffect, useRef, useState} from 'preact/hooks';

import {useDebounce} from '../hooks/useDebounce';
import {useTranslation} from '../intl';
import Button from './Button';
import VirtualList from './VirtualList';
import ClearInputButton from './shared/ClearInputButton';

import type {RNode, TargetedEvent} from '../types';

export type MultiselectDropdownItem = {name: string; key: string};

export type MultiselectDropdownProps = Readonly<{
  itemAdd: (key: string) => void;
  items: MultiselectDropdownItem[];
  id: string;
}>;
export function MultiselectDropdown(props: MultiselectDropdownProps): RNode {
  const {itemAdd, items, id} = props;

  const [search, setSearch] = useState('');
  const [filteredItems, setFilteredItems] = useState(items);
  useEffect(() => setFilteredItems(items), [items]);

  const [expanded, setExpanded] = useState(false);
  const [timer, setTimer] = useState(0);
  const buttonRefs = useRef(new Set<HTMLButtonElement>());
  const inputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLUListElement>(null);
  const intl = useTranslation();

  useEffect(() => {
    return () => clearTimeout(timer);
  }, [timer]);

  const blurHandler = useCallback((event: TargetedEvent<HTMLElement, FocusEvent>) => {
    if (buttonRefs.current && buttonRefs.current.has(event.relatedTarget as HTMLButtonElement)
      || inputRef.current && event.relatedTarget === inputRef.current) {
      if (event.relatedTarget !== listRef.current) {
        const related = event.relatedTarget as HTMLElement;
        related?.focus();
      }
    } else if (event.relatedTarget === listRef.current) {
      inputRef.current?.focus();
    } else {
      setTimer(window.setTimeout(() => setExpanded(false), 200));
    }
  }, []);

  const addHandler = useCallback((e: TargetedEvent<HTMLButtonElement, Event>, key: string) => {
    e.preventDefault();
    clearTimeout(timer);
    inputRef.current?.focus();
    itemAdd(key);
  }, [itemAdd, timer]);

  const {debouncedFunction: searchHandler, clearDebounce} = useDebounce((e:TargetedEvent<HTMLInputElement, Event>) => {
    const value = (e.target as HTMLInputElement).value;
    setSearch(value);
  }, [setSearch]);

  const onClearSearchInput = useCallback(() => {
    setSearch('');
    clearDebounce();
  }, [clearDebounce]);

  const expandedClass = expanded ? 'expand' : 'collapse';

  useEffect(() => {
    const filtered = items.filter(item => search === '' || item.name.toLowerCase().includes(search.toLowerCase()));
    setFilteredItems(filtered);
    buttonRefs.current.clear();
  }, [items, search, timer]);

  const ITEM_HEIGHT = 30;

  const renderRow = useCallback((index: number) => {
    const item = filteredItems[index];
    return <li style={{height: ITEM_HEIGHT}} key={item.key}>
      <Button
        refCallback={(element) => element && buttonRefs.current?.add(element)}
        onBlur={blurHandler}
        onClick={(e) => addHandler(e, item.key)}>
        {item.name}
      </Button>
    </li>;
  }, [filteredItems, addHandler, blurHandler]);

  const renderPlaceholder = useCallback((range: [number, number], height: number) => {
    return <li key={range.map(x => filteredItems[x].key).join(' ')} style={{height}}></li>;
  }, [filteredItems]);

  const heightFn = useCallback(() => ITEM_HEIGHT, []);

  return <div className="vm-dropdown-container">
    <div className="vm-clearable-input-container">
      <input
        ref={inputRef}
        type="search"
        id={id}
        onFocus={() => setExpanded(true)}
        onBlur={blurHandler}
        className="pure-u-1"
        onKeyDown={(e) => e.stopPropagation()}
        onInput={searchHandler}
        value={search}
        placeholder={intl.translate({defaultMessage: 'Add...'})}
        aria-expanded={expanded} />
      <ClearInputButton value={search} onClearInput={onClearSearchInput} />
    </div>
    <ul
      ref={listRef}
      className={`vm-dropdown-list ${expandedClass}`}
      tabIndex={-1}
    >
      {expanded && listRef.current && <VirtualList
        itemCount={filteredItems.length}
        itemHeight={heightFn}
        itemRenderer={renderRow}
        placeholderRenderer={renderPlaceholder}
        scrollElement={listRef.current}
      />}
    </ul>
  </div>;
}
