// Panel content component for menu items

/** @jsx h */

import {h} from 'preact';
import {useRef} from 'preact/hooks';

import {animateScroll} from '../functions/animateScroll';

import DomainSelect from './DomainSelect';
import {LanguageSwitch} from './LanguageSwitch';
import ViewList from './ViewList';

import type {RNode} from '../types';
import type {PageProps} from './PageFloating';

// TODO: remove all 'float' in classnames.
type Props = PageProps & {
  onMenuItemClick: () => void;
};
export default function MenuPanel(props: Props): RNode {
  const contentRef = useRef<HTMLDivElement>(null);

  const scrollTo = (e: HTMLElement) => {
    if (contentRef.current) {
      animateScroll({
        element: contentRef.current,
        targetPosition: e.offsetTop - contentRef.current.offsetTop,
        duration: 1000,
        stopAtApproximation: true,
      });
    }
  };

  return <div ref={contentRef} className="vm-menu-content">
    <ViewList onMenuItemClick={props.onMenuItemClick}>
      <LanguageSwitch
        setLang={props.setLang}
        lang={props.lang}
        languages={props.languages}
      />
      <DomainSelect onFocus={scrollTo} />
    </ViewList>
  </div>;
}
