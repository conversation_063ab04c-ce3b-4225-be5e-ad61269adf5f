// Embedded Media Component

/** @jsx h */
import {Fragment, h} from 'preact';
import {useRef} from 'preact/hooks';

import {useEffectAfterMount} from '../hooks/useEffectAfterMount';
import {Translate} from '../intl';
import {maybeMedia} from '../media';
import {IRIS} from '../rdf';
import {WithEnlargedDialog, refreshIframe} from './EnlargedMediaDialog';
import ObjectLink from './ObjectLink';

import type {MapModel} from '../rdfmodels';
import type {Hint, RNode} from '../types';


function maybeLinkedMedia(mm: MapModel, iri: string, hint?: Hint): RNode | null {
  const src = mm.classOf(iri) === IRIS.VM.Document && mm._getField(iri, IRIS.VM.link);

  return src ? <LinkedMedia mm={mm} iri={iri} src={src} hint={hint} /> : null;
}

function viewerSrc(src: string) {
  return encodeURI(`https://docs.google.com/viewer?embedded=true&url=${src}`);
}

// TODO: media type Type?
// TODO: loaded state?
export function externalMedia(mm: MapModel, iri: string, src: string): {elem: RNode|null; type: string} {
  const type = mm.documentTypeOf(iri);
  let elem: RNode|null = null;

  switch (type) {
    case IRIS.VM.DOCUMENTTYPE.audio:
      elem = <audio src={src} />;
      break;
    case IRIS.VM.DOCUMENTTYPE.video:
      elem = <video src={src} controls autoPlay />;
      break;
    case IRIS.VM.DOCUMENTTYPE.image:
      elem = <img src={src} />;
      break;
    case IRIS.VM.DOCUMENTTYPE.embed:
    case IRIS.VM.DOCUMENTTYPE.application:
      elem = <Fragment>
        <iframe
          src={type === IRIS.VM.DOCUMENTTYPE.embed ? src : viewerSrc(src)}
          onLoad={(e) => {
            e.currentTarget.classList.toggle('loaded', true);
          }}
          allowFullScreen
          frameBorder={0} />
        <span className="loading"><Translate defaultMessage="Loading..." /></span>
      </Fragment>;
      break;
    default:
  }

  return {elem: elem ? <div className="vm-media">{elem}</div> : null, type};
}

type LinkedMediaProps = EmbedProps & {
  iri: string;
  src: string;
};
function LinkedMedia({mm, iri, src, hint}: LinkedMediaProps): RNode | null {
  const canEmbed: boolean = !!mm.canEmbedOf(iri);
  const ref = useRef<HTMLDivElement>(null);

  let media: RNode|null = null;
  let mediaType: string = '';

  useEffectAfterMount(() => {
    const timeout = refreshIframe(ref.current);
    return () => clearInterval(timeout);
  }, [src]);

  if (canEmbed) {
    const mediaParse = externalMedia(mm, iri, src);
    if (mediaParse.elem) {
      media = mediaParse.elem;
      mediaType = mediaParse.type;
    }
  }

  const simpleType = /.*\/([^/]+)$/.exec(mediaType)?.[1].toLowerCase() || '';
  const className = 'vm-media-container' + (simpleType ? ` ${simpleType}` : '');
  const enlargeMedia = [
    IRIS.VM.DOCUMENTTYPE.application,
    IRIS.VM.DOCUMENTTYPE.image,
  ].includes(mediaType);

  // TODO: dialog should not be nested.
  return <section ref={ref} className={className} aria-label="Media">
    {canEmbed && media && (enlargeMedia
      && <WithEnlargedDialog mediaType={simpleType}>{media}</WithEnlargedDialog>
      || media)}
    <div className="relation-group">
      <div className="relation-label"><span>{mm.className(IRIS.VM.link)}</span></div>
      <div className="relation-other"><ObjectLink value={src} hint={hint} /></div>
    </div>
  </section>;
}

export type EmbedProps = Readonly<{
  mm: MapModel;
  iri: string | null;
  hint?: Hint;
}>;
export default function MaybeEmbeddedMedia({mm, iri, hint}: EmbedProps): RNode|null {
  if (!iri) {
    return null;
  }

  return maybeMedia(mm, iri) || maybeLinkedMedia(mm, iri, hint);
}

