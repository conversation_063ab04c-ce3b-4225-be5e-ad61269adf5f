// Tests for model object value linking component
/** @jsx h */

import {h} from 'preact';

import {MarkdownProvider} from '../context/mark';
import {IRIS} from '../rdf';
import {renderWithModel} from '../testing/model';
import ObjectLink from './ObjectLink';

const triples = [
  {subj: '_example', pred: IRIS.VM.name, obj: 'Example'},
];

test.each([
  ['Literal string', 'Literal string'],
  ['https://url.test/path', 'url.test/path'],
  ['mailto:<EMAIL>', '<EMAIL>'],
  // TODO: Need a nav context to test the richer object links.
  //['_example', 'Example'],
])('ObjectLink %#', (v, expected) => {
  const {container} = renderWithModel(<MarkdownProvider><ObjectLink value={v} /></MarkdownProvider>, triples);
  expect(container.textContent).toEqual(expected);
});
