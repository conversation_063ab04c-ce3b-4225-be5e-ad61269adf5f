// Setting current region filter
/** @jsx h */

import {h} from 'preact';

import {useNavigation, usePlace} from '../context/place';
import {useModel} from '../context/triples';
import {toggleGroupedInclusion} from '../functions/keyed';
import {_labelOf} from '../functions/labels';
import {iconForRegion} from '../icons';
import {Translate} from '../intl';
import {IRIS} from '../rdf';
import Button from './Button';

import type {RNode} from '../types';

function pressedValue(val: boolean|undefined): 'true'|'false'|'mixed'|undefined {
  if (val === true) {
    return 'true';
  }
  if (val === false) {
    return 'false';
  }
  return 'mixed';
}

type RegionSelectProps = {
  values: string[];
};
export default function RegionSelect({values: regions}: RegionSelectProps): RNode|null {
  const mm = useModel();
  const {setFilters} = useNavigation();
  const {filters} = usePlace();
  const groupIri = IRIS.VM.Region;

  return regions.length ? (
    <div id={`select-${_labelOf(groupIri)}`}>
      <label><Translate defaultMessage="Region:" />&nbsp;</label>
      {regions.map(iri => {
        const name = mm.nameOf(iri);
        const icon = iconForRegion(mm, iri);
        return <Button
          key={iri}
          aria-label={name}
          aria-pressed={pressedValue(filters.domain?.[groupIri]?.[iri])}
          className="pure-button"
          tooltipMessage={name}
          onClick={() => setFilters({...filters, domain: toggleGroupedInclusion(filters.domain, groupIri, iri)})}
        >
          {typeof icon === 'string'
            ? icon
            : <span
                className={icon.className}
                style={{backgroundImage: icon.backgroundImage}}></span>}
        </Button>;
      })}
    </div>
  ) : null;
}
