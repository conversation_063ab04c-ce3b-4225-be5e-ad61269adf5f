/** @jsx h */
import {h} from 'preact';

import Button from './Button';

import type {RNode} from '../types';

/**
 * Example usage of the refactored Button component with built-in tooltip functionality.
 * This demonstrates how the Button component now eliminates the need for manual TooltipWrapper usage.
 */
export default function ButtonExample(): RNode {
  return (
    <div style={{padding: '2rem', display: 'flex', flexDirection: 'column', gap: '2rem'}}>
      <h2>Button Component with Built-in Tooltips</h2>
      
      {/* Basic buttons without tooltips */}
      <div>
        <h3>Basic Buttons (No Tooltips)</h3>
        <div style={{display: 'flex', gap: '1rem', flexWrap: 'wrap'}}>
          <Button>Simple Button</Button>
          <Button className="vm-toggle-button">Styled Button</Button>
          <Button disabled>Disabled Button</Button>
        </div>
      </div>

      {/* Buttons with tooltips using default positioning */}
      <div>
        <h3>Buttons with Auto-Positioned Tooltips</h3>
        <div style={{display: 'flex', gap: '1rem', flexWrap: 'wrap'}}>
          <Button tooltipMessage="This tooltip auto-positions itself">
            Auto Tooltip
          </Button>
          
          <Button 
            tooltipMessage="Main menu"
            className="vm-toggle-button"
            aria-pressed={false}
          >
            ☰ Menu
          </Button>
          
          <Button 
            tooltipMessage="Search items"
            className="vm-toggle-button"
          >
            🔍 Search
          </Button>
        </div>
      </div>

      {/* Buttons with specific tooltip positions */}
      <div>
        <h3>Buttons with Specific Tooltip Positions</h3>
        <div style={{display: 'flex', gap: '1rem', flexWrap: 'wrap'}}>
          <Button 
            tooltipMessage="Tooltip on top" 
            tooltipPosition="top"
          >
            Top Tooltip
          </Button>
          
          <Button 
            tooltipMessage="Tooltip on bottom" 
            tooltipPosition="bottom"
          >
            Bottom Tooltip
          </Button>
          
          <Button 
            tooltipMessage="Tooltip on left" 
            tooltipPosition="left"
          >
            Left Tooltip
          </Button>
          
          <Button 
            tooltipMessage="Tooltip on right" 
            tooltipPosition="right"
          >
            Right Tooltip
          </Button>
        </div>
      </div>

      {/* Real-world usage examples */}
      <div>
        <h3>Real-World Usage Examples</h3>
        <div style={{display: 'flex', gap: '1rem', flexWrap: 'wrap'}}>
          {/* Example from InteractionPanelFloating - BEFORE */}
          <div>
            <h4>Before (Manual TooltipWrapper):</h4>
            <pre style={{fontSize: '0.8rem', backgroundColor: '#f5f5f5', padding: '0.5rem'}}>
{`<TooltipWrapper 
  position="auto" 
  tooltipMessage="Main Menu"
>
  <Button
    id="main-menu-button"
    className="vm-interaction-button"
    aria-pressed={false}
    onClick={toggleBurger}
  >
    <SVGBurger />
  </Button>
</TooltipWrapper>`}
            </pre>
          </div>
          
          <div>
            <h4>After (Built-in Tooltip):</h4>
            <pre style={{fontSize: '0.8rem', backgroundColor: '#e8f5e8', padding: '0.5rem'}}>
{`<Button
  id="main-menu-button"
  className="vm-interaction-button"
  aria-pressed={false}
  onClick={toggleBurger}
  tooltipMessage="Main Menu"
  tooltipPosition="auto"
>
  <SVGBurger />
</Button>`}
            </pre>
          </div>
        </div>
      </div>

      {/* Disabled buttons with tooltips */}
      <div>
        <h3>Disabled Buttons (Tooltips Hidden)</h3>
        <div style={{display: 'flex', gap: '1rem', flexWrap: 'wrap'}}>
          <Button 
            disabled 
            tooltipMessage="This tooltip won't show because button is disabled"
          >
            Disabled with Tooltip
          </Button>
          
          <Button 
            aria-disabled="true" 
            tooltipMessage="This tooltip won't show because of aria-disabled"
          >
            Aria Disabled
          </Button>
        </div>
      </div>

      {/* Edge detection demonstration */}
      <div>
        <h3>Edge Detection & Stretching</h3>
        <p>Try these buttons near viewport edges to see tooltip stretching:</p>
        <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start'}}>
          <Button 
            tooltipMessage="This tooltip will stretch to fit when near the left edge of the viewport and has a very long message"
            tooltipPosition="bottom"
          >
            Left Edge Test
          </Button>
          
          <Button 
            tooltipMessage="This tooltip will stretch to fit when near the right edge of the viewport and has a very long message"
            tooltipPosition="bottom"
          >
            Right Edge Test
          </Button>
        </div>
      </div>

      <div style={{marginTop: '2rem', padding: '1rem', backgroundColor: '#f5f5f5', borderRadius: '4px'}}>
        <h4>✅ Benefits of Built-in Tooltip Functionality:</h4>
        <ul>
          <li><strong>Reduced Code Duplication:</strong> No need to manually wrap Button with TooltipWrapper</li>
          <li><strong>Improved Developer Experience:</strong> Simpler, cleaner component usage</li>
          <li><strong>Backward Compatibility:</strong> Existing Button usage without tooltips works unchanged</li>
          <li><strong>All Tooltip Features:</strong> Edge detection, smart positioning, accessibility, mobile handling</li>
          <li><strong>Type Safety:</strong> Full TypeScript support for tooltip props</li>
          <li><strong>Consistent API:</strong> Same tooltip behavior across all Button instances</li>
        </ul>
        
        <h4>🔧 Migration Guide:</h4>
        <ol>
          <li>Replace <code>&lt;TooltipWrapper&gt;&lt;Button&gt;</code> patterns with <code>&lt;Button tooltipMessage=""&gt;</code></li>
          <li>Move <code>tooltipMessage</code> and <code>position</code> props from TooltipWrapper to Button</li>
          <li>Remove the TooltipWrapper import and usage</li>
          <li>All existing Button functionality remains unchanged</li>
        </ol>
      </div>
    </div>
  );
}
