// Dropdown selector between map geographies

/** @jsx h */

import {h} from 'preact';
import {forwardRef} from 'preact/compat';
import {useCallback, useMemo} from 'preact/hooks';

import {Panel} from '../context/panels';
import {useNavigation, usePlace} from '../context/place';
import {useModel} from '../context/triples';
import flag from '../flags';
import {useScreenProperties} from '../context/screen';
import {useTranslation} from '../intl';
import SVGArrowWide from '../svg/SVGArrowWide';
import SVGMaps from '../svg/SVGMaps';
import {parseTilesSrc} from '../tiles';

import Button from './Button';
import OverlayPanelFloating from './OverlayPanelFloating';

import type {ForwardRef, RNode, RefObject} from '../types';

function MapIcon({mapIri}: {mapIri: string}): RNode|null {
  const mm = useModel();

  const lens = mm.onLensOf(mapIri);
  if (lens == null) {
    return null;
  }
  const detail = mm.detailsOf(lens)[0];
  const tiles: string|null = mm.usesMapTilesOf(detail);
  if (!tiles) {
    console.error('Broken lens for map: ' + mapIri);
    return null;
  }
  const {src} = parseTilesSrc(tiles);

  const baseTileUrl = src.replace('{z}-{x}-{y}', '0-0-0');

  return <div className="vm-float-map-select-icon-wrapper">
    <img className="vm-float-map-select-icon" src={baseTileUrl} />
  </div>;
}

export type MapItemProps = {
  iri: string;
  isSelected?: boolean;
  selection: string | null;
};
function MapItem({iri, isSelected, selection}: MapItemProps): RNode {
  const mm = useModel();
  const {setViewAndSelection} = useNavigation();
  const onClick = useCallback(() => setViewAndSelection(iri, selection), [setViewAndSelection, iri, selection]);

  return <Button
    role="link"
    aria-pressed={isSelected}
    className="vm-float-map-select-item vm-toggle-button"
    onClick={onClick}
    disabled={isSelected}
  >
    <MapIcon mapIri={iri} />
    <span className="vm-line-clamp">{mm.nameOf(iri)}</span>
  </Button>;
}

export type MapSelectorFloatingOverlayProps = {
  mapSwitcherToggleRef: RefObject<HTMLDivElement>;
};
export function MapSelectorFloatingOverlay({mapSwitcherToggleRef}: MapSelectorFloatingOverlayProps): RNode|null {
  const mm = useModel();
  const {view: currentMap, selection} = usePlace();

  const maps: string[] = useMemo(() => {
    return mm.maps();
  }, [mm]);

  return <OverlayPanelFloating
    panel={Panel.MapSelector}
    hidden={!maps.length}
    toggleElement={mapSwitcherToggleRef}
  >
    {maps.map(iri => {
      return <MapItem
        key={iri}
        iri={iri}
        isSelected={currentMap === iri}
        selection={selection}
      />;
    })}
  </OverlayPanelFloating>;
}

export type MapSelectorFloatingProps = {
  onToggle?: () => void;
  expand: boolean;
};
const MapSelectorFloating = forwardRef(({onToggle, expand}: MapSelectorFloatingProps, ref: ForwardRef<HTMLDivElement>): RNode|null => {
  const mm = useModel();
  const {isLargeScreen} = useScreenProperties();
  const {view: currentMap, selection} = usePlace();
  const intl = useTranslation();
  const hideMapSwitcher = flag(mm, 'hideMapSwitcher');

  const maps: string[] = useMemo(() => mm.maps(), [mm]);

  const togglePanelState = useCallback(() => onToggle && onToggle(), [onToggle]);

  if (hideMapSwitcher || !currentMap || !maps.length) {
    return null;
  }

  return <div ref={ref} className="vm-float-map-select vm-float-block">
    <Button
      className="vm-float-map-select-button"
      id="map-selector-button"
      aria-pressed={expand}
      onClick={togglePanelState}
    >
      {isLargeScreen && <SVGMaps />}
      <span>{intl.translate({defaultMessage: 'Maps'})}</span>
      {isLargeScreen && <SVGArrowWide />}
    </Button>
    {isLargeScreen && <div className="vm-float-map-select-items">
      {maps.map(iri => {
        return <MapItem
          key={iri}
          iri={iri}
          isSelected={currentMap === iri}
          selection={selection}
        />;
      })}
    </div>}
  </div>;
});

export default MapSelectorFloating;
