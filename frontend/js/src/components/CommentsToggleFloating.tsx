// Toggle with message bubble icon.

/** @jsx h */
import {h} from 'preact';
import {useMemo} from 'preact/hooks';

import Button from './Button';

import {useComments} from '../context/annotation';
import {Panel, usePanelUpdaters} from '../context/panels';
import {useTranslation} from '../intl';
import SVGBubbleText from '../svg/SVGBubbleText';

import type {RNode} from '../types';

type ToggleProps = {
  toggleEnabled: boolean;
};
export default function CommentsToggleFloating({toggleEnabled}: ToggleProps): RNode|null {
  const intl = useTranslation();
  const comments = useComments();
  const {toggleOpen} = usePanelUpdaters(Panel.Comments);
  const commentsCount = useMemo(() => Object.values(comments).reduce<number>((acc, c) => acc + c.length, 0), [comments]);
  const isLargeCount = commentsCount > 99;

  return <div className="vm-float-block vm-comments-toggle">
    <Button
      className="vm-toggle-button"
      aria-label={intl.translate({defaultMessage: 'Comments'})}
      id="comments-toggle-button"
      // TODO: The button should not disappear when the comment panel is opened.
      aria-pressed={toggleEnabled}
      tooltipMessage={intl.translate({defaultMessage: 'Comments'})}
      onClick={toggleOpen}>
      <SVGBubbleText />
      {commentsCount > 0 ? (
        <span data-testid="comments-count">
          {isLargeCount ? '99+' : commentsCount}
        </span>
      ) : (<span style={{fontSize: '1.1rem'}}>{'+'}</span>)}
    </Button>
  </div>;
}
