// Trust perception sidebar component
/** @jsx h */

import {h} from 'preact';
import {useCallback, useMemo, useState} from 'preact/hooks';

import {useModel} from '../context/triples';
import {getTrustAttitude} from '../functions/trustperception';
import {Translate} from '../intl';
import {IRIS} from '../rdf';
import {defaultCompare, localeInsensitive} from '../sort';
import {partitionSubj} from '../triplejump';

import type {RNode} from '../types';

function roundScore(s: unknown): null|number {
  if (typeof s === 'number' || typeof s === 'string') {
    return Math.round(+s * 100) / 100;
  }
  return null;
}

type Props = Readonly<{
  iri: string;
  expandable: boolean;
}>;
type PropsExtended = Props & {
  perceptions: string[];
};
function TrustPerception(props: PropsExtended): RNode {
  const {expandable} = props;
  const mm = useModel();
  const [openPerception, setOpenPerception] = useState<string|null>(null);

  const perceptions = useMemo(() => props.perceptions
    .map((iri: string) => {
      const [obj] = partitionSubj(mm, iri, {}, true);
      const trust = {
        trustor: obj[IRIS.VM.trustor],
        trustee: obj[IRIS.VM.trustee],
        comment: obj[IRIS.VM.comment],
        trustScore: roundScore(obj[IRIS.VM.trustScore]),
        trustworthinessScore: roundScore(obj[IRIS.VM.trustworthinessScore]),
        benevolenceScore: roundScore(obj[IRIS.VM.benevolenceScore]),
        competenceScore: roundScore(obj[IRIS.VM.competenceScore]),
        integrityScore: roundScore(obj[IRIS.VM.integrityScore]),
      };

      type Other = {other: string; direction: 'from'|'towards'};
      const other: Other = props.iri === trust.trustor
        ? {other: trust.trustee, direction: 'towards'}
        : {other: trust.trustor, direction: 'from'};

      return {
        iri,
        attitude: getTrustAttitude(trust.trustScore),
        otherName: mm.nameOf(other.other),
        ...other,
        ...trust,
      };
    })
    .sort((a, b) => (
      localeInsensitive(a.otherName, b.otherName)
      || defaultCompare(a.other, b.other)
    )), [mm, props.iri, props.perceptions]);

  const onPerceptionClick = useCallback((iri: string) => (
    () => setOpenPerception(v => v === iri ? null : iri)
  ), []);

  return <ul className="vm-d vm-trust-list">
    {perceptions.map(t => <li key={t.iri}>
      <span
        onClick={expandable ? onPerceptionClick(t.iri) : undefined}
        className={expandable ? 'interactable' : undefined}
      >
        <span className={`vm-i offset vm-trust-attitude ${t.attitude}`}>&#x25B6;&#xFE0E;</span>
        {t.direction === 'from'
          ? <Translate defaultMessage="Trust from {other}" values={{other: t.otherName}} />
          : <Translate defaultMessage="Trust towards {other}" values={{other: t.otherName}} />}
      </span>
      {(!expandable || openPerception === t.iri) && <div className="vm-trust-expanded">
        <div><Translate defaultMessage="Trust Score:" /> {t.trustScore ?? '?'}</div>
        <div>
          <Translate defaultMessage="Trustworthiness Score:" /> {t.trustworthinessScore ?? '?'}
          <div><Translate defaultMessage="Benevolence Score:" /> {t.benevolenceScore ?? '?'}</div>
          <div><Translate defaultMessage="Competence Score:" /> {t.competenceScore ?? '?'}</div>
          <div><Translate defaultMessage="Integrity Score:" /> {t.integrityScore ?? '?'}</div>
        </div>
        <div>{t.comment}</div>
      </div>}
    </li>)}
  </ul>;
}

export default function MaybeTrustPerception(props: Props): RNode|null {
  const mm = useModel();

  const perceptionIRIs = useMemo(() => mm.trustPerceptionsRelatedTo(props.iri), [mm, props.iri]);
  if (!perceptionIRIs.length) {
    return null;
  }

  return TrustPerception({...props, perceptions: perceptionIRIs});
}
