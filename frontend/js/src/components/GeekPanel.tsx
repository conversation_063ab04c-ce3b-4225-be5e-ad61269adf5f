// Controlling what debug options are switched on
/** @jsx h */

import {h} from 'preact';

import {useGeekery} from '../context/geek';
import Button from './Button';

import type {Geekishness} from '../context/geek';
import type {RNode, TargetedEvent} from '../types';

export default function GeekPanel(): RNode|null {
  const {
    panelDisplay,
    panelOpen,
    setPanelOpen,
    showBounds,
    showPink,
    showAllAreas,
    showSelectionArea,
    tilesOverride,
    showLenses,
    showMapTermsButton,
    updateShown,
  } = useGeekery() as Geekishness;

  if (!panelOpen) {
    return null;
  }

  const close = (e: Event) => {
    e.preventDefault();
    setPanelOpen(false);
  };

  const change = (e: TargetedEvent<HTMLInputElement>) => {
    const input = e.currentTarget;
    updateShown(prev => ({...prev, [input.name]: input.checked}));
  };

  const selectChange = (e: TargetedEvent<HTMLSelectElement>) => {
    const select = e.currentTarget;
    updateShown(prev => ({...prev, [select.name]: select.value}));
  };


  return <div className="vm-geek-panel">
    <form className="vm-form pure-form">
      <label>
        Set user interface panels display:
        <select name="panelDisplay" value={panelDisplay} onChange={selectChange}>
          <option value="norm">Display panels as normal</option>
          <option value="fade">Display panels in outline only</option>
          <option value="hide">Hide panels</option>
        </select>
      </label>
      <label>
        Override maps tiles display:
        <select name="tilesOverride" value={tilesOverride} onChange={selectChange}>
          <option value="none">Display tiles as normal</option>
          <option value="base">Display base lens only</option>
          <option value="leaf">Display leaf lens only</option>
          <option value="hide">Hide tiles</option>
        </select>
      </label>
      <fieldset>
        <label>
          Show zoom and lens:
          <input type="checkbox" checked={showLenses} name="showLenses" onChange={change} />
        </label>
        <label>
          Show all areas rainbowy:
          <input type="checkbox" checked={showAllAreas} name="showAllAreas" onChange={change} />
        </label>
        <label>
          Show area of a current selection:
          <input type="checkbox" checked={showSelectionArea} name="showSelectionArea" onChange={change} />
        </label>
        <label>
          Show view bounds:
          <input type="checkbox" checked={showBounds} name="showBounds" onChange={change} />
        </label>
        <label>
          Show pink lines:
          <input type="checkbox" checked={showPink} name="showPink" onChange={change} />
        </label>
        <label>
          Show map terms button:
          <input type="checkbox" checked={showMapTermsButton} name="showMapTermsButton" onChange={change} />
        </label>
      </fieldset>
      <Button className="pure-button pure-button-primary" onClick={close}>Close</Button>
    </form>
  </div>;
}

