// Groups of relations
/** @jsx h */

import {h} from 'preact';
import {useMemo} from 'preact/hooks';

import {useModel} from '../context/triples';
import {getInvolvementRelations, getLabelledRelations, sortLabelledRelations} from '../labelledrelations';
import {IRIS} from '../rdf';
import ObjectLink from './ObjectLink';

import type {LabelledRelation} from '../labelledrelations';
import type {Existence, Hint, RNode} from '../types';

type GroupedLabelledRelations = {
  label: string;
  rels: Omit<LabelledRelation, 'label'>[];
};

const HANDLED_PREDS: Existence = {
  [IRIS.VMHE.hasInvolvement]: true,
};

export type Props = {
  iri: string;
  preds: Existence;
  hint?: Hint;
};
export default function GroupedRelations({iri, preds, hint}: Props): RNode|null {
  const mm = useModel();

  const groups = useMemo(() => {
    const relations = [
      ...getInvolvementRelations(mm, iri),
      ...getLabelledRelations(mm, iri, {...preds, ...HANDLED_PREDS}),
    ];
    let currentGroup: GroupedLabelledRelations;
    return sortLabelledRelations(relations, mm)
      .reduce<GroupedLabelledRelations[]>((acc, rel) => {
        if (!currentGroup || currentGroup.label !== rel.label) {
          currentGroup = {label: rel.label, rels: []};
          acc.push(currentGroup);
        }
        currentGroup.rels.push(rel);
        return acc;
      }, []);
  }, [mm, iri, preds]);

  return (groups.length && <div className="vm-relation-group-container vm-d">
    {groups.map(({label, rels}) => <div
      key={label}
      className="relation-group">
      <div className="relation-label"><span>{label}</span></div>
      <div className="relation-other">{rels.map((rel, i) => (
        rel.other
          ? <ObjectLink
              relIri={rel.iri}
              key={label + '-' + rel.other}
              value={rel.other}
              iconFlag="offset"
              hint={hint} />
          : <span key={i}>{rel.defaultOtherLabel()}</span>))}</div>
    </div>)}
  </div> || null);
}
