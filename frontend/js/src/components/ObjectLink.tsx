// Fancy link component for obj values from model
/** @jsx h */

import {Fragment, h} from 'preact';

import {useMarkdown} from '../context/mark';
import {useModel} from '../context/triples';
import {Icon} from '../icons';
import {IRIS} from '../rdf';
import SVGExternalLink from '../svg/SVGExternalLink';
import SVGMail from '../svg/SVGMail';
import SVGPhone from '../svg/SVGPhone';
import LinkForSelect from './LinkForSelect';

import type {Renderer} from '../context/mark';
import type {MapModel} from '../rdfmodels';
import type {Hint, RNode} from '../types';

export type Props = {
  value: string;
  namer?: (iri: string) => string;
  iconFlag?: 'default'|'noIcon'|'offset';
  hint?: Hint;
  relIri?: string;
};

function buildObjectLink(mm: MapModel, kd:Renderer, {value: iri, iconFlag = 'default', hint, namer, relIri}: Props): RNode {
  const name = namer ? namer(iri) : mm.nameOf(iri);
  if (!name) {
    // Not an iri of named individual, might be url or just a string.
    const match = /^(?:https?|tel|mailto):\/*(.+)?/.exec(iri);
    if (match && (!iri.startsWith('http:') || relIri === IRIS.VM.link)) {
      // TODO: Handle relative urls sensibly.
      return (
        <a
          href={iri} target="_blank" rel="noopener noreferrer"
        >
          <span>
            {match[1]}
          </span>
          {renderIcon(iri)}
        </a>
      );
    }
    // nesting span elements causes style conflicts and rendering issues
    return <div {...kd.render(iri)}></div>;
  }
  return <Fragment>
    {(iconFlag !== 'noIcon') && <Icon iri={iri} offset={iconFlag === 'offset'} tooltip={true} />}
    <LinkForSelect iri={iri} hint={hint}>{name}</LinkForSelect>
  </Fragment>;
}

export default function ObjectLink(props: Props): RNode {
  const kd = useMarkdown();
  const {iconFlag = 'default'} = props;
  const mm = useModel();
  return <span className={'vm-object-link' + (iconFlag === 'offset' ? ' offset' : '')}>
    {buildObjectLink(mm, kd, props)}
  </span>;
}

const renderIcon = (iri:string) => {
  if (iri.startsWith('mailto:')) {
    return <SVGMail />;
  } else if (iri.startsWith('tel:')) {
    return <SVGPhone />;
  }
  return <SVGExternalLink />;

};
