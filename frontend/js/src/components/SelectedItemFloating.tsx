// Selected Item

/** @jsx h */
import {h} from 'preact';
import {useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState} from 'preact/hooks';

import {useControlPanelActiveControl, useControlPanelUpdater, useSearchControl} from '../context/controlpanel';
import {useMarkdown} from '../context/mark';
import {PANEL_LABELS, Panel} from '../context/panels';
import {useNavigation, usePlace} from '../context/place';
import {useSearchHistory} from '../context/searchhistory';
import {useModel} from '../context/triples';
import {classNamer, exampleFor, explainTypeDetails, objectNamer} from '../functions/explainItems';
import useFilterToggles from '../hooks/useFilterToggles';
import usePrevious from '../hooks/usePrevious';
import {Icon} from '../icons';
import {useTranslation} from '../intl';
import {IRIS} from '../rdf';
import SVGArrowWide from '../svg/SVGArrowWide';
import Button from './Button';
import EmbeddedMedia from './EmbeddedMedia';
import FilterToggle from './FilterToggle';
import FocusControlSelect from './FocusControlSelect';
import GroupedRelations from './GroupedRelations';
import HistoryNavigationButton from './HistoryNavigationButton';
import ObjectLink from './ObjectLink';
import PanelHeader from './shared/PanelHeader';
import MaybeTrustPerception from './TrustPerception';

import type {MapModel} from '../rdfmodels';
import type {Existence, Hint, RNode, StateUpdater} from '../types';

const PANEL_DEFAULT_CLASS_NAME = 'vm-float-selected-item vm-float-block';
const HANDLED_PREDS: Existence = {
  [IRIS.SKOS.altLabel]: true,
  [IRIS.SKOS.comment]: true,
  [IRIS.SKOS.definition]: true,
  [IRIS.SKOS.example]: true,
  [IRIS.RDFS.subClassOf]: true,
  [IRIS.VM.aidsCapitalType]: true,
  [IRIS.VM.broader]: true,
  [IRIS.VM.description]: true,
  [IRIS.VM.harmsCapitalType]: true,
  [IRIS.VM.hasContentType]: true,
  [IRIS.VM.hasTrustPerception]: true,
  [IRIS.VM.hasDocumentType]: true,
  [IRIS.VM.canEmbed]: true,
};

function broaderLink(mm: MapModel, iri: string|null, hint?: Hint) {
  return iri && <ObjectLink value={iri} namer={objectNamer(mm)} iconFlag="offset" hint={hint} />;
}

function classLink(mm: MapModel, cls: string|null, hint?: Hint) {
  if (!cls) {
    return null;
  }

  const namer = classNamer(mm);
  const asLink = cls !== IRIS.OWL.Class;
  return asLink
    ? <ObjectLink value={cls} namer={namer} iconFlag="noIcon" hint={hint} />
    : namer(cls);
}

function noIconObjectLink(mm: MapModel, iri: string|null, hint?: Hint) {
  return iri && <ObjectLink value={iri} namer={objectNamer(mm)} iconFlag="noIcon" hint={hint} />;
}

type ItemExplainProps = Readonly<{
  mm: MapModel;
  iri: string;
  hint?: Hint;
}>;
export function ItemExplain({mm, iri, hint}: ItemExplainProps): RNode|null {
  const classBuilder = (iri: string|null) => classLink(mm, iri, hint);
  const superClassBuilder = (iri: string|null) => noIconObjectLink(mm, iri, hint);
  const categoryBuilder = (iri: string|null) => noIconObjectLink(mm, iri, hint);
  const broaderBuilder = (iri: string|null) => broaderLink(mm, iri, hint);

  return (<span className="vm-explain">
    {explainTypeDetails({mm, iri, classBuilder, superClassBuilder, broaderBuilder, categoryBuilder})}
  </span>);
}

type CategoryFilterToggleProps = {
  iri: string;
};
export function CategoryFilterToggle({iri}: CategoryFilterToggleProps): RNode|null {
  const {translate} = useTranslation();
  const {checkFilter, toggleFilter} = useFilterToggles();

  const isEnabled = checkFilter('itemKinds', iri);

  return <FilterToggle
    iri={iri}
    name={isEnabled
      ? translate({defaultMessage: 'Remove from filters'})
      : translate({defaultMessage: 'Add to filters'})}
    isSelected={isEnabled}
    onToggle={iri => toggleFilter('itemKinds', iri)}
    noTooltip={true}
    binaryIcon={true}
  />;
}

export function MaybeCategoryFilterToggle(props: CategoryFilterToggleProps): RNode|null {
  const mm = useModel();

  const show = mm.isCategoryInstance(props.iri);
  return show && <CategoryFilterToggle {...props}></CategoryFilterToggle> || null;
}

function getMaxHeight(element: HTMLElement) {
  const style = getComputedStyle(element);
  const lines = parseFloat(style.getPropertyValue('--collapsed-lines'));
  const maxHeight = lines * parseFloat(style.lineHeight);
  const padding = parseFloat(style.paddingTop)
    + parseFloat(style.paddingBottom);
  return maxHeight + padding;
}

type ExpandWrapProps = {
  expand: boolean;
  setExpand: StateUpdater<boolean>;
};
type SelectedItemProps = ExpandWrapProps & {
  selection: string;
};
function SelectedItemPanel(props: SelectedItemProps): RNode|null {
  const {selection, expand, setExpand} = props;
  const prevSelection = usePrevious(selection);
  const {view, hint} = usePlace();
  const {setSelection} = useNavigation();
  const mm = useModel();
  const kd = useMarkdown();
  const intl = useTranslation();
  const {setControl} = useControlPanelUpdater();
  const contentRef = useRef<HTMLDivElement>(null);
  const panelRef = useRef<HTMLDivElement>(null);
  const panelClassName = useRef(PANEL_DEFAULT_CLASS_NAME);
  const calcOverflow = useRef(true);
  const contentMaxHeight = useRef<number|null>(null);
  const headerRef = useRef<HTMLDivElement>(null);

  const prevHintName = usePrevious(hint.name);

  const sipHint: Hint = {name: 'sip'};

  /**
   * To decide whether the toogle button/bottom fade should appear or not
   * requires the content to be rendered once. To avoid the twitchy effect of
   * popping up the button then disappear in some cases, we set the classes
   * directly after render in useEffect.
   *
   * Mind you this check should be done once for a selection.
   *
   * The ref for panelClassName is used for backing up the element classes to
   * make sure added/removed non-expandable class is not lost on change other
   * than calcOverflow.
   */

  // checking overflow only if selection has changed.
  if (prevSelection !== selection) {
    calcOverflow.current = true;
  }

  if (calcOverflow.current) {
    panelClassName.current = PANEL_DEFAULT_CLASS_NAME + ' non-expandable';
    if (panelRef.current) {
      panelRef.current.classList.toggle('non-expandable', true);
    }
  }

  useLayoutEffect(() => {
    if (contentRef && contentRef.current) {
      contentRef.current.scrollTop = 0;
    }
  }, [selection]);

  useLayoutEffect(() => {
    if (calcOverflow.current) {
      calcOverflow.current = false;
      if (contentRef.current && panelRef.current) {
        // cheating with the fact that the app SIP starts in collapsed state which has max-height
        contentMaxHeight.current ??= getMaxHeight(contentRef.current);
        if (contentRef.current.scrollHeight > contentMaxHeight.current) {
          panelRef.current.classList.toggle('non-expandable', false);
          panelClassName.current = PANEL_DEFAULT_CLASS_NAME;
        }
      }
    }
  });

  useEffect(() => {
    if (hint.name === 'sip' || (prevHintName == null && hint.name === 'search')) {
      panelRef.current?.focus();
    }
  }, [hint.name, prevHintName, selection]);

  const onClose = useCallback(() => {
    setSelection(null);
    setExpand(true);
    if (hint.name === 'search') {
      setControl('search', true);
    }
  }, [setSelection, setExpand, setControl, hint]);

  const description = selection && mm.descriptionOf(selection);

  const handledPreds = useMemo<Existence>(() => {
    const preds = {...HANDLED_PREDS};
    if (!selection) {
      return preds;
    }
    const primaryRel = mm.primaryCategoryRelationOf(selection)?.predicate;
    if (primaryRel) {
      preds[primaryRel] = true;
    }
    const isDocument = mm.classOf(selection) === IRIS.VM.Document;
    if (isDocument) {
      preds[IRIS.VM.link] = true;
    }
    return preds;
  }, [mm, selection]);

  return <section ref={panelRef} id="selected-item-panel" aria-label={PANEL_LABELS[Panel.SelectedItem]} className={panelClassName.current} tabIndex={-1}>
    <PanelHeader
      ref={headerRef}
      icon={<Icon iri={selection} />}
      title={mm.nameOfOrFallback(selection)}
      subTitle={<ItemExplain mm={mm} iri={selection} hint={sipHint} />}
      panel={Panel.SelectedItem}
      onClose={onClose} />
    <section ref={contentRef} className={'selected-item-content vm-float-block-content' + (expand ? '' : ' collapse')}>
      <EmbeddedMedia mm={mm} iri={selection} hint={sipHint} />
      {description && <div
        className="vm-c"
        key="desc"
        {...kd.render(description)}
      />}
      <MaybeCategoryFilterToggle iri={selection} />
      {exampleFor(mm, selection)}
      <MaybeTrustPerception iri={selection} expandable={true} />
      <GroupedRelations iri={selection} preds={handledPreds} hint={sipHint}></GroupedRelations>
    </section>
    <footer className="footer">
      <div className="buttons-trail">
        <div className="navigation">
          <HistoryNavigationButton
            title={intl.translate({defaultMessage: 'previous'})}
            direction="backwards"
            hint={sipHint} />
          <HistoryNavigationButton
            title={intl.translate({defaultMessage: 'next'})}
            direction="forwards"
            hint={sipHint} />
        </div>
        <div className="context">
          <FocusControlSelect
            iri={selection}
            view={view}
            title={intl.translate({defaultMessage: 'Show Item on...'})} />
          <Button
            aria-label="Expand Panel"
            aria-pressed={expand}
            aria-expanded={expand}
            tooltipMessage={expand ? 'Collapse' : 'Expand'}
            className={'vm-toggle-button vm-expand-button' + (expand ? ' flip' : '')}
            onClick={() => setExpand(v => !v)}
          >
            <SVGArrowWide />
          </Button>
        </div>
      </div>
    </footer>
  </section>;
}

function MaybeSelectedItemPanel({expand, setExpand}: ExpandWrapProps): RNode | null {
  const mm = useModel();
  const {selection} = usePlace();
  const activeControl = useControlPanelActiveControl();
  const {search: searchTerm} = useSearchControl();
  const entries = useSearchHistory();

  // Show SIP when search is focused but no search input and history
  const emptySearchAndHistory = activeControl === 'search' && searchTerm === '' && !entries.length;
  const hasActiveControl = activeControl !== 'none' && !emptySearchAndHistory;

  const selectionExists = selection != null && mm.subjectExists(selection);
  const showPanel = selectionExists && !hasActiveControl;

  return showPanel && <SelectedItemPanel {...{selection, expand, setExpand}} /> || null;
}

type Props = {
  enabled: boolean;
};
/**
 * Selected Item Component
 */
export default function SelectedItemFloating({enabled}: Props): RNode | null {
  // Persistent states
  const [expand, setExpand] = useState(true);

  return enabled && <MaybeSelectedItemPanel {...{expand, setExpand}} /> || null;
}
