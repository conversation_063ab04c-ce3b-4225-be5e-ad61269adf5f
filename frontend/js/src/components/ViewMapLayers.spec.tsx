import {IRIS} from '../rdf';
import {buildModel} from '../testing/model';
import {evaluateStack} from './ViewMapLayers';


describe('evaluateStack', () => {
  const FALLBACK_SRC = 'http://test.com/fallback/z-x-y.png#fff';

  it('legacy map - view', () => {
    const model = buildModel([
      {subj: 'vm:viewA', pred: IRIS.VM.usesMapTiles, obj: 'http://test.com/view/z-x-y.png#background:#fff'},
    ]);
    const view = 'vm:viewA';
    const lensDetails: string[] = [];
    const expected = [
      '#fff',
      'http://test.com/view/z-x-y.png',
    ];
    expect(evaluateStack(model, view, lensDetails, FALLBACK_SRC)).toEqual(expected);
  });
  it('legacy map - storypoint1', () => {
    const model = buildModel([
      {subj: 'vm:storypoint1', pred: IRIS.VM.ofStory, obj: 'vm:storyA'},
      {subj: 'vm:storypoint1', pred: IRIS.VM.usesMapTiles, obj: 'http://test.com/storypoint/1/z-x-y.png#background:#fff'},
    ]);
    const view = 'vm:storypoint1';
    const lensDetails: string[] = [];
    const expected = [
      '#fff',
      'http://test.com/storypoint/1/z-x-y.png',
    ];
    expect(evaluateStack(model, view, lensDetails, FALLBACK_SRC)).toEqual(expected);
  });
  it('legacy map - uses parent story tiles', () => {
    const model = buildModel([
      {subj: 'vm:storypoint1', pred: IRIS.VM.ofStory, obj: 'vm:storyA'},
      {subj: 'vm:storyA', pred: IRIS.VM.usesMapTiles, obj: 'http://test.com/story/z-x-y.png#background:#fff'},
    ]);
    const view = 'vm:storypoint1';
    const lensDetails: string[] = [];
    const expected = [
      '#fff',
      'http://test.com/story/z-x-y.png',
    ];
    expect(evaluateStack(model, view, lensDetails, FALLBACK_SRC)).toEqual(expected);
  });
  it('legacy map - stack storypoint, story', () => {
    const model = buildModel([
      {subj: 'vm:storypoint1', pred: IRIS.VM.ofStory, obj: 'vm:storyA'},
      {subj: 'vm:storypoint1', pred: IRIS.VM.usesMapTiles, obj: 'http://test.com/storypoint/1/z-x-y.png#background:transparent'},
      {subj: 'vm:storyA', pred: IRIS.VM.usesMapTiles, obj: 'http://test.com/story/z-x-y.png#background:#fff'},
    ]);
    const view = 'vm:storypoint1';
    const lensDetails: string[] = [];
    const expected = [
      '#fff',
      'http://test.com/story/z-x-y.png\nhttp://test.com/storypoint/1/z-x-y.png',
    ];
    expect(evaluateStack(model, view, lensDetails, FALLBACK_SRC)).toEqual(expected);
  });
  it('legacy map - no tiles, does fallback', () => {
    const model = buildModel([]);
    const view = 'vm:storypoint1';
    const lensDetails: string[] = [];
    const expected = [
      '#fff',
      'http://test.com/fallback/z-x-y.png',
    ];
    expect(evaluateStack(model, view, lensDetails, FALLBACK_SRC)).toEqual(expected);
  });
  it('legacy map - stack transparent, does fallback', () => {
    const model = buildModel([
      {subj: 'vm:view', pred: IRIS.VM.usesMapTiles, obj: 'http://test.com/view/z-x-y.png#background:transparent'},
    ]);
    const view = 'vm:view';
    const lensDetails: string[] = [];
    const expected = [
      '#fff',
      'http://test.com/fallback/z-x-y.png\nhttp://test.com/view/z-x-y.png',
    ];
    expect(evaluateStack(model, view, lensDetails, FALLBACK_SRC)).toEqual(expected);
  });
  it('lens map - view on single lens', () => {
    const model = buildModel([
      {subj: 'vm:lensA', pred: IRIS.VM.usesMapTiles, obj: 'http://test.com/view/z-x-y.png#background:#eee'},
    ]);
    const view = 'vm:storypoint1';
    const lensDetails: string[] = ['vm:lensA'];
    const expected = [
      '#eee',
      'http://test.com/view/z-x-y.png',
    ];
    expect(evaluateStack(model, view, lensDetails, FALLBACK_SRC)).toEqual(expected);
  });
  it('lens map - view on lens stack', () => {
    const model = buildModel([
      {subj: 'vm:lensA', pred: IRIS.VM.usesMapTiles, obj: 'http://test.com/lens/A/z-x-y.png#background:transparent'},
      {subj: 'vm:lensB', pred: IRIS.VM.usesMapTiles, obj: 'http://test.com/lens/B/z-x-y.png#background:#fff'},
    ]);
    const view = 'vm:storypoint1';
    const lensDetails: string[] = ['vm:lensA', 'vm:lensB'];
    const expected = [
      '#fff',
      'http://test.com/lens/B/z-x-y.png\nhttp://test.com/lens/A/z-x-y.png',
    ];
    expect(evaluateStack(model, view, lensDetails, FALLBACK_SRC)).toEqual(expected);
  });
  it('lens map - no view does fallback', () => {
    const model = buildModel([
      {subj: 'vm:lensA', pred: IRIS.VM.usesMapTiles, obj: 'http://test.com/lens/A/z-x-y.png#background:transparent'},
      {subj: 'vm:lensB', pred: IRIS.VM.usesMapTiles, obj: 'http://test.com/lens/B/z-x-y.png#background:#fff'},
    ]);
    const view = null;
    const lensDetails: string[] = ['vm:lensA', 'vm:lensB'];
    const expected = [
      '#fff',
      'http://test.com/fallback/z-x-y.png',
    ];
    expect(evaluateStack(model, view, lensDetails, FALLBACK_SRC)).toEqual(expected);
  });
  it('lens map - lens tiles', () => {
    const model = buildModel([
      {subj: 'vm:lensA', pred: IRIS.VM.usesMapTiles, obj: 'http://test.com/lens/A/z-x-y.png#background:transparent'},
      {subj: 'vm:lensB', pred: IRIS.VM.usesMapTiles, obj: 'http://test.com/lens/B/z-x-y.png#background:#fff'},
    ]);
    const view = 'vm:storypoint1';
    const lensDetails: string[] = ['vm:lensA', 'vm:lensB'];
    const expected = [
      '#fff',
      'http://test.com/lens/B/z-x-y.png\nhttp://test.com/lens/A/z-x-y.png',
    ];
    expect(evaluateStack(model, view, lensDetails, FALLBACK_SRC)).toEqual(expected);
  });
  it('lens map - lens transparent tiles, does fallback', () => {
    const model = buildModel([
      {subj: 'vm:lensA', pred: IRIS.VM.usesMapTiles, obj: 'http://test.com/lens/A/z-x-y.png#background:transparent'},
      {subj: 'vm:lensB', pred: IRIS.VM.usesMapTiles, obj: 'http://test.com/lens/B/z-x-y.png#background:transparent'},
      {subj: 'vm:storypoint1', pred: IRIS.VM.ofStory, obj: 'vm:story'},
    ]);
    const view = 'vm:storypoint1';
    const lensDetails: string[] = ['vm:lensA', 'vm:lensB'];
    const expected = [
      '#fff',
      'http://test.com/fallback/z-x-y.png\nhttp://test.com/lens/B/z-x-y.png\nhttp://test.com/lens/A/z-x-y.png',
    ];
    expect(evaluateStack(model, view, lensDetails, FALLBACK_SRC)).toEqual(expected);
  });
  it('lens map - view with tiles, lens without tiles and falling back', () => {
    const model = buildModel([
      {subj: 'vm:storypoint1', pred: IRIS.VM.ofStory, obj: 'vm:story'},
      {subj: 'vm:storypoint1', pred: IRIS.VM.usesMapTiles, obj: 'http://test.com/storypoint/1/z-x-y.png#background:transparent'},
    ]);
    const view = 'vm:storypoint1';
    const lensDetails: string[] = ['vm:lensA'];
    const expected = [
      '#fff',
      'http://test.com/fallback/z-x-y.png\nhttp://test.com/storypoint/1/z-x-y.png',
    ];
    expect(evaluateStack(model, view, lensDetails, FALLBACK_SRC)).toEqual(expected);
  });
});
