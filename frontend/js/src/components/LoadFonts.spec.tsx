// Tests for dynamic font loading logic
/** @jsx h */

import {h} from 'preact';

import {IRIS} from '../rdf';
import {renderWithModel} from '../testing/model';
import LoadFonts, {resolveFont} from './LoadFonts';

function buildLabel(subj: string, pred: string, obj: unknown) {
  return [
    {subj, pred: IRIS.RDF.type, obj: IRIS.VM.Label},
    {subj, pred, obj},
  ];
}

const roboto = 'Roboto-Regular';
const displayR = {fontFamily: 'Roboto-Regular'};
const displayB = {fontFamily: 'Roboto-Bold'};
const displayK = {fontFamily: 'Roboto-Black'};
const displayCG = {fontFamily: 'CenturyGothicPro-Bold'};
const displayFP = {fontFamily: 'FuturaPT-Bold'};
const displayUPR = {fontFamily: 'UniviaProRegular'};
const displayUPL = {fontFamily: 'UniviaPro-Light'};
const css = '@import url(\'https://fonts.googleapis.com/css2?family=Roboto&display=swap\');';
const csspt = '@import url(\'https://fonts.googleapis.com/css2?family=PT+Sans&display=swap\');';
const csspti = '@import url(\'https://fonts.googleapis.com/css2?family=PT+Sans:ital@1&display=swap\');';
const cssmsh = '@import url(\'https://fonts.googleapis.com/css2?family=Mulish:wght@700&display=swap\');';
const css700 = '@import url(\'https://fonts.googleapis.com/css2?family=Roboto:wght@700&display=swap\');';
const css900 = '@import url(\'https://fonts.googleapis.com/css2?family=Roboto:wght@900&display=swap\');';
const css400700 = '@import url(\'https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap\');';
const cssc100400 = '@import url(\'https://fonts.googleapis.com/css2?family=Carlito:wght@100;400&display=swap\');';
const csscc = '@import url(\'https://fonts.googleapis.com/css2?family=Carlito&family=Mulish:wght@400;700&display=swap\');';
const cssfp = '@import url(\'https://fonts.googleapis.com/css2?family=Hind:wght@700&display=swap\');';
const cssupr = '@import url(\'https://fonts.googleapis.com/css2?family=Exo&display=swap\');';
const cssupl = '@import url(\'https://fonts.googleapis.com/css2?family=Exo:wght@300&display=swap\');';

const chainC = 'Calibri, Carlito, "Noto Sans", sans-serif';
const chainNS = '"Noto Sans", sans-serif';
const chainR = '"Roboto", sans-serif';
const chainRl = '"Raleway", sans-serif';
const chainP = '"PT Sans", sans-serif';
const chainCG = '"Century Gothic", "Century Gothic Pro", "URW Gothic", Mulish, sans-serif';
const chainA = '"Liberation Sans", Arial, sans-serif';
const chainU = '"univia-pro", "Exo", sans-serif';
const chainF = '"futura-pt", "Hind", sans-serif';

test.each([
  [[], undefined],
  [[...buildLabel('h', IRIS.VM.fontFamily, roboto)], css],
  [[...buildLabel('h', IRIS.VM.fontFamily, 'Arial')], undefined],
  [[...buildLabel('h', IRIS.VM.fontFamily, 'ArialMT')], undefined],
  [[...buildLabel('h', IRIS.VM.fontFamily, 'Arial-BoldMT')], undefined],
  [[...buildLabel('h', IRIS.VM.fontFamily, 'PTSans-Regular')], csspt],
  [[...buildLabel('h', IRIS.VM.fontFamily, roboto), ...buildLabel('h', IRIS.VM.fontFamily, 'Arial')], css],
  [[...buildLabel('h', IRIS.VM.display, displayR)], css],
  [[...buildLabel('h', IRIS.VM.display, displayB)], css700],
  [[...buildLabel('h', IRIS.VM.display, displayK)], css900],
  [[...buildLabel('h', IRIS.VM.display, displayB), ...buildLabel('h', IRIS.VM.display, displayR)], css400700],
  [[...buildLabel('h', IRIS.VM.display, displayCG)], cssmsh],
  [[...buildLabel('h', IRIS.VM.display, displayFP)], cssfp],
  [[...buildLabel('h', IRIS.VM.display, displayUPR)], cssupr],
  [[...buildLabel('h', IRIS.VM.display, displayUPL)], cssupl],
  [[...buildLabel('h', IRIS.VM.fontFamily, 'PTSans-Italic')], csspti],
  // Will ask for a (non-existent) 100-weight Carlito variant for Calibri-Light but okay?
  [[...buildLabel('h', IRIS.VM.fontFamily, 'Calibri'), ...buildLabel('h', IRIS.VM.fontFamily, 'Calibri-Light')], cssc100400],
  [[...buildLabel('h', IRIS.VM.fontFamily, 'Calibri'), ...buildLabel('h', IRIS.VM.fontFamily, 'CenturyGothicPro-Bold'), ...buildLabel('h', IRIS.VM.fontFamily, 'CenturyGothicPro')], csscc],
])('LoadFont %#', (triples, expected) => {
  const {container} = renderWithModel(<LoadFonts />, triples);
  expect(container.querySelector('style')?.textContent).toEqual(expected);
});

test.each([
  ['Non-Existent-Font', {fontFamily: chainA, resolved: false}],
  ['NotoSans-Italic', {fontFamily: chainNS, fontStyle: 'italic', fontWeight: 400, resolved: true}],
  ['NotoSans-Medium', {fontFamily: chainNS, fontStyle: 'normal', fontWeight: 500, resolved: true}],
  ['-Regular', {fontFamily: chainA, resolved: false}],
  ['Roboto-ItalicItalic', {fontFamily: chainA, resolved: false}],
  ['Something-Regular', {fontFamily: chainA, resolved: false}],
  ['Roboto-Black', {fontFamily: chainR, fontStyle: 'normal', fontWeight: 900, resolved: true}],
  ['Roboto-Bold', {fontFamily: chainR, fontStyle: 'normal', fontWeight: 700, resolved: true}],
  ['Roboto-BoldItalic', {fontFamily: chainR, fontStyle: 'italic', fontWeight: 700, resolved: true}],
  ['Roboto-BlackItalic', {fontFamily: chainR, fontStyle: 'italic', fontWeight: 900, resolved: true}],
  ['Roboto-Light', {fontFamily: chainR, fontStyle: 'normal', fontWeight: 300, resolved: true}],
  ['Raleway-Medium', {fontFamily: chainRl, fontStyle: 'normal', fontWeight: 500, resolved: true}],
  ['PTSans-Bold', {fontFamily: chainP, fontStyle: 'normal', fontWeight: 700, resolved: true}],
  ['PTSans-Italic', {fontFamily: chainP, fontStyle: 'italic', fontWeight: 400, resolved: true}],
  ['CenturyGothicPro-Bold', {fontFamily: chainCG, fontStyle: 'normal', fontWeight: 700, resolved: true}],
  ['ArialMT', {fontFamily: chainA, fontStyle: 'normal', fontWeight: 400, resolved: true}],
  ['Arial-RegularMT', {fontFamily: chainA, fontStyle: 'normal', fontWeight: 400, resolved: true}],
  ['Arial-ItalicMT', {fontFamily: chainA, fontStyle: 'italic', fontWeight: 400, resolved: true}],
  ['Calibri-Light', {fontFamily: chainC, fontStyle: 'normal', fontWeight: 100, resolved: true}],
  ['Calibri-Regular', {fontFamily: chainC, fontStyle: 'normal', fontWeight: 400, resolved: true}],
  ['Calibri', {fontFamily: chainC, fontStyle: 'normal', fontWeight: 400, resolved: true}],
  ['UniviaProRegular', {fontFamily: chainU, fontStyle: 'normal', fontWeight: 400, resolved: true}],
  ['UniviaPro-Light', {fontFamily: chainU, fontStyle: 'normal', fontWeight: 300, resolved: true}],
  ['UniviaPro-Bold', {fontFamily: chainU, fontStyle: 'normal', fontWeight: 700, resolved: true}],
  ['FuturaPT-Bold', {fontFamily: chainF, fontStyle: 'normal', fontWeight: 700, resolved: true}],
])('resolveFont "%s"', (faceName, expected) => {
  expect(resolveFont(faceName)).toEqual(expected);
});
