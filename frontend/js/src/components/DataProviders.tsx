// Providers for data on and related to the map
/** @jsx h */

import {h} from 'preact';
import {useMemo} from 'preact/hooks';

import {AnnotationProvider} from '../context/annotation';
import {AssetsContext, assetsFromModelData} from '../context/assets';
import {DomainsProvider} from '../context/domains';
import {ModelContext, modelFromData} from '../context/triples';

import type {ComponentChildren, MapData, RNode} from '../types';

export type Props = Readonly<{
  children: ComponentChildren;
  mapReader: () => MapData;
  lang: string;
}>;

export default function DataProviders(props: Props): RNode {
  const mapData = props.mapReader();
  const {lang} = props;
  const {
    assets,
    model,
  } = useMemo(() => ({
    assets: assetsFromModelData(mapData),
    model: modelFromData(mapData, lang),
  }), [mapData, lang]);
  return (
    <AssetsContext.Provider value={assets}>
      <ModelContext.Provider value={model}>
        <AnnotationProvider mapIRI={mapData['iri']} lang={lang}>
          <DomainsProvider>
            {props.children}
          </DomainsProvider>
        </AnnotationProvider>
      </ModelContext.Provider>
    </AssetsContext.Provider>
  );
}
