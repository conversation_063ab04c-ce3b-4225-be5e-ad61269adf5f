// Navigation for listing and inspecting items
/** @jsx h */

import {envFromTerms, envFromUnicodeTerms} from 'markdown-it-marked';
import {Fragment, h} from 'preact';
import {useCallback, useEffect, useLayoutEffect, useMemo, useRef} from 'preact/hooks';

import {useMarkdown} from '../context/mark';
import {useMatchingView, useNavigation, usePlace} from '../context/place';
import {supportLookBehind} from '../functions/markdown';
import {focusables} from '../hooks/useFocusedFirstRef';
import usePrevious from '../hooks/usePrevious';
import {Translate} from '../intl';
import ItemListEntry from './ItemListEntry';

import type {MutableRef} from 'preact/hooks';
import type {Item, RNode} from '../types';
import type {ExpandType} from './ItemListEntry';

export type ListHintName = 'initial' | 'kbNav' | 'none';
/**
 * Hinting allows control over behaviours.
 */
export type ListHint = {name: ListHintName};

export interface ListViewItem extends Item {
  groupIri?: string;
  firstInGroup?: boolean;
}

type ItemRow = ListViewItem & {
  index: number;
  ref: HTMLElement | null;
};

type ListViewItemGroup = {
  iri?: string;
  items: ItemRow[];
};

const FOCUS_HINTS: Partial<{[hint in ListHintName]: true}> = {
  initial: true,
  kbNav: true,
};

/**
 * Sets the focusability of an element and its focusable children by modifying the `tabindex` attribute.
 *
 * Skips enabling tabindex for the first link element (which is the item link) and linking action
 * is moved to the container.
 *
 * @param {HTMLElement|null} element - The target element whose focusability and children's should be modified.
 * @param {boolean} enable - If `true`, sets `tabindex` to `0, but skip the first child.
 *     If `false`, sets `tabindex` to `-1`.
 */
function setFocusability(
  element: HTMLElement|null,
  enable: boolean,
): void {
  const focusableChildren = element?.querySelectorAll(focusables);
  const value = enable ? '0' : '-1';
  element?.setAttribute('tabindex', value);
  focusableChildren?.forEach((el, i) => {
    if (i || !enable) {
      el.setAttribute('tabindex', value);
    }
  });
}

/**
 * If the scroll is time consuming it would wait for item to be in view
 * before focus. Otherwise it is instantaneous focus.
 *
 * @param {ScrollIntoViewOptions} options scroll options.
 */
function scrollAndFocus(element: HTMLElement, options: ScrollIntoViewOptions, skipFocus: boolean = false) {
  element.scrollIntoView(options);

  if (skipFocus) {
    return;
  }

  if (window.IntersectionObserver) {
    const observer = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting) {
        element.focus();
        observer.disconnect(); // Stop observing after focus
      }
    }, {threshold: 1});

    observer.observe(element);
  } else {
    setTimeout(() => {
      element.focus();
    }, 300);
  }
}

function resetItemsFocusability(itemRows: ItemRow[]) {
  itemRows.forEach(({ref}) => {
    setFocusability(ref, false);
  });
}

function updateItemsFocusability(itemRows: ItemRow[], prevIndex: number|null, nextIndex: number) {
  const prevRef = prevIndex != null && itemRows[prevIndex]?.ref || null;
  const nextRef = itemRows[nextIndex]?.ref || null;
  setFocusability(prevRef, false);
  setFocusability(nextRef, true);
}

function useUpdateFocusability(focusedIndex: MutableRef<number>, itemRows: ItemRow[], selection: string|null, expand: ExpandType): void {
  // recalculate focused index when content changes
  useMemo(() => {
    focusedIndex.current = Math.max(
      itemRows.findIndex(item => selection === item.iri),
      0,
    );
  }, [focusedIndex, itemRows, selection]);

  const prevFocusedIndex = usePrevious(focusedIndex.current);
  const currFocusedIndex = focusedIndex.current;

  useLayoutEffect(() => {
    resetItemsFocusability(itemRows);
  }, [itemRows]);

  useLayoutEffect(() => {
    updateItemsFocusability(itemRows, prevFocusedIndex, currFocusedIndex);
  }, [currFocusedIndex, itemRows, prevFocusedIndex, expand]);
}

export type Props = Readonly<{
  expand: ExpandType;
  lang: string;
  items: ListViewItem[];
  hasGroups: boolean;
  listHint: ListHint;
  allowKeyDownPropagate?: boolean;
}>;

/**
 * Renders a list of items with optional grouping and keyboard navigation.
 *
 * @param {Object} props - The properties for the component.
 * @param {'all' | 'selection' | 'none'} props.expand - Determines how the list should be expanded.
 * @param {Item[]} props.items - The list of items to display.
 * @param {boolean} props.hasGroups - Whether the items are grouped.
 * @param {boolean} [props.allowKeyDownPropagate=false] - Whether to allow keyboard events to propagate.
 * @returns {RNode} The rendered item list.
 */
export default function ItemListContent(props: Props): RNode {
  const {expand, items, hasGroups, listHint, allowKeyDownPropagate = false} = props;
  const kd = useMarkdown();
  const {hint, filters, selection} = usePlace();
  const {setSelection} = useNavigation();
  const [isVoting, _] = useMatchingView('voting');
  const selectionVisible = useRef(false);
  /**
   * Used to recalculate focusable elements
   *
   * Defined as a ref, rather than state to not trigger state updaters in
   * useEffect causing re-rendering.
   */
  const focusedIndex = useRef(-1);

  const search = filters.search;
  const markEnv = useMemo(() => {
    const markdownEnv = supportLookBehind ? envFromUnicodeTerms : envFromTerms;
    return search ? markdownEnv(search.split(/\s/g).filter(Boolean), {prefix: true}) : undefined;
  }, [search]);

  const itemRows: ItemRow[] = useMemo(() => items.map((item, index) => ({...item, index, ref: null})), [items]);

  useUpdateFocusability(focusedIndex, itemRows, selection, expand);

  // Auto focus on mount or content refresh
  useEffect(() => {
    const focusedRef = itemRows[focusedIndex.current]?.ref;
    if (focusedRef) {
      scrollAndFocus(focusedRef, {block: hint.name === 'itemList' ? 'nearest' : 'start', behavior: 'smooth'}, !FOCUS_HINTS[listHint.name]);
    }

    if (FOCUS_HINTS[listHint.name]) {
      listHint.name = 'none';
    }
  }, [hint, itemRows, listHint]);

  const [groupedItems] = itemRows.reduce<[ListViewItemGroup[], string|undefined|null]>(([groups, currentIri], item) => {
    if (currentIri !== item.groupIri) {
      groups.push({iri: item.groupIri, items: []});
      currentIri = item.groupIri;
    }
    groups[groups.length - 1].items.push(item);
    return [groups, currentIri];
  }, [[], null]);

  const moveTo = useCallback((index: number, fromIndex: number|null = null) => {
    const nextIndex
      = index === itemRows.length ? 0
        : index === -1 ? itemRows.length - 1
          : index;
    const nextElement = itemRows[nextIndex].ref;
    if (nextElement) {
      focusedIndex.current = nextIndex;
      updateItemsFocusability(itemRows, fromIndex, nextIndex);
      nextElement.focus();
    }
  }, [itemRows]);

  const onItemKeyDown = useCallback((e: KeyboardEvent, index: number, iri: string) => {
    if (!allowKeyDownPropagate) {
      e.stopPropagation();
    }
    if (e.key === 'ArrowUp') {
      e.preventDefault();
      moveTo(index - 1, index);
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      moveTo(index + 1, index);
    } else if (e.key === 'Enter') {
      e.preventDefault();
      setSelection(iri, {name: 'search'});
    }
  }, [allowKeyDownPropagate, moveTo, setSelection]);

  const buildItem = ({iri, index, name, prominent = false}: Item & {index: number; prominent?: boolean}) => <ItemListEntry
    expand={expand}
    expandableContent={false}
    ref={(e: HTMLLIElement|null) => {
      if (iri === selection) {
        selectionVisible.current = !!e;
      }
      itemRows[index].ref = e;
    }}
    iri={iri}
    isSelected={selection === iri}
    key={iri}
    name={kd.inline(name, markEnv)}
    prominent={prominent}
    showVotes={isVoting}
    markEnv={expand === 'all' || expand === 'selection' && selection === iri ? markEnv : undefined}
    onKeyDown={e => onItemKeyDown(e, index, iri)}
    hint={{name: 'itemList'}}
  />;

  /**
   * NVDA has ArrowDown, ArrowUp mapping to traverse through elements.
   * This is an issue when we would like to assign custom key press control.
   * tabIndex={-1} on a container would supress the built-in NVDA navigation
   * and allow custom key mapping.
   */
  return <Fragment>
    {groupedItems.map((group) => {
      return <ul className="vm-group" key={group.iri || '#'} data-groupiri={group.iri} tabIndex={-1}>
        {group.items.map(item => {
          if (group.iri) {
            return buildItem({...item, prominent: item.firstInGroup});
          }
          return <Fragment key={item.iri}>
            {item.firstInGroup && hasGroups && <h4 className="vm-prominent"><Translate defaultMessage="Others" /></h4>}
            {buildItem(item)}
          </Fragment>;
        })}
      </ul>;
    })}
  </Fragment>;
}
