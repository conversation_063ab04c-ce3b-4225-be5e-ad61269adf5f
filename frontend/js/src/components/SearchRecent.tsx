// Flat search results
/** @jsx h */

import {Fragment, h} from 'preact';
import {useMemo, useState} from 'preact/hooks';

import {PANEL_LABELS, Panel} from '../context/panels';
import {useNavigation} from '../context/place';
import {useSearchHistory, useSearchHistoryUpdater} from '../context/searchhistory';
import {useModel} from '../context/triples';
import {useEffectAfterMount} from '../hooks/useEffectAfterMount';
import {Translate, useTranslation} from '../intl';
import SVGBin from '../svg/SVGBin';
import SVGMagnifyingGlass from '../svg/SVGMagnifyingGlass';
import Button from './Button';
import LinkForSelect from './LinkForSelect';
import {SearchItemContent, SearchTermContent} from './SearchItemContent';
import PanelHeader from './shared/PanelHeader';

import type {RNode, RefObject, SearchEntry} from '../types';

type TimestampCheckpoint = 'today'|'yesterday'|'week'|'month'|'older';
const DAY_MS = 86400000;
const LIST_MAX_SIZE = 20;

function groupByTimestamp<T extends {group: TimestampCheckpoint|null; entry: {timestamp: string}}>(items: T[]):
{[category in TimestampCheckpoint]?: T[]} {
  const now = new Date();
  const startOfTodayDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  const startOfToday = startOfTodayDate.toISOString();
  const startOfYesterday = new Date(startOfTodayDate.getTime() - DAY_MS).toISOString();
  const sevenDaysAgo = new Date(now.getTime() - DAY_MS * 7).toISOString();
  const thirtyDaysAgo = new Date(now.getTime() - DAY_MS * 30).toISOString();

  const groups: {[category in TimestampCheckpoint]?: T[]} = {};

  items.forEach(item => {
    let checkpoint: TimestampCheckpoint = 'older';
    const timestamp = item.entry.timestamp;
    if (timestamp >= startOfToday) {
      checkpoint = 'today';
    } else if (timestamp >= startOfYesterday) {
      checkpoint = 'yesterday';
    } else if (timestamp >= sevenDaysAgo) {
      checkpoint = 'week';
    } else if (timestamp >= thirtyDaysAgo) {
      checkpoint = 'month';
    }
    item.group = checkpoint;
    (groups[checkpoint] ??= []).push(item);
  });

  return groups;
}

type EntryRow = {
  type: 'entry';
  index: number;
  ref: HTMLElement | null;
  entry: SearchEntry;
  group: TimestampCheckpoint | null;
  clearRef: HTMLElement | null;
};
type ElementRow = {
  type: 'default'|'load';
  index: number;
  ref: HTMLElement | null;
};
type ItemRow = EntryRow | ElementRow;

type SearchListProps = {
  entries: SearchEntry[];
  parentRef: RefObject<HTMLElement>;
  onTermSelected: (term: string) => void;
  onClose: () => void;
};
function SearchRecentList(props: SearchListProps): RNode | null {
  const {entries: all, parentRef, onClose, onTermSelected} = props;

  const {setSelection} = useNavigation();
  const intl = useTranslation();
  const mm = useModel();
  const {clear, push, remove} = useSearchHistoryUpdater();

  const [limit, setLimit] = useState<number|null>(LIST_MAX_SIZE);
  const [focusedIndex, setFocusedIndex] = useState(0);

  const translations: {[category: string]: {label: string}} = {
    'today': {label: intl.translate({defaultMessage: 'Today'})},
    'yesterday': {label: intl.translate({defaultMessage: 'Yesterday'})},
    'week': {label: intl.translate({defaultMessage: 'Last 7 days'})},
    'month': {label: intl.translate({defaultMessage: 'Last 30 days'})},
    'older': {label: intl.translate({defaultMessage: 'Older'})},
  };

  const loadMore = limit != null && limit < all.length;

  const entries = useMemo(() => {
    const size = Math.min(all.length, limit ?? Infinity);
    return all.slice(0, size);
  }, [all, limit]);

  const itemRows: ItemRow[] = useMemo(() => {
    return entries.map((entry, index) => ({type: 'entry', entry, index, ref: null, clearRef: null, group: null}));
  }, [entries]);

  const groups = useMemo(() => {
    const filtered = itemRows.filter(item => item.type === 'entry') as EntryRow[];
    return groupByTimestamp(filtered);
  }, [itemRows]);

  // auto-selecting when list is modified, including load more.
  useEffectAfterMount(() => {
    if (entries.length) {
      moveTo(focusedIndex);
    }
  }, [entries]);

  const moveTo = (index: number) => {
    const nextIndex
      = index === itemRows.length ? 0
        : index === -1 ? itemRows.length - 1
          : index;
    itemRows[nextIndex].ref?.focus();
    setFocusedIndex(nextIndex);
  };

  const removeEntry = (entry: SearchEntry) => {
    remove(entry);
    moveTo(focusedIndex);
  };

  const onLoadClicked = () => {
    setLimit(null);
  };

  const onItemClicked = (entry: SearchEntry) => {
    onClose();
    push(entry);
  };

  const onTermClicked = (entry: SearchEntry) => {
    onTermSelected(entry.value);
    push(entry);
  };

  const onEntryClearKeyDown = (e: KeyboardEvent, entry: SearchEntry) => {
    e.stopPropagation();
    if (e.key === 'Enter') {
      e.preventDefault();
      removeEntry(entry);
    } else if (e.key === 'ArrowLeft') {
      e.preventDefault();
      moveTo(focusedIndex);
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      moveTo(focusedIndex - 1);
    } else if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {
      e.preventDefault();
      moveTo(focusedIndex + 1);
    }
  };

  const clearButton = (row: EntryRow) => <Button
    refCallback={elem => row.clearRef = elem}
    className="vm-anchored-button vm-toggle-button"
    aria-pressed="false"
    tooltipMessage={intl.translate({defaultMessage: 'Clear'})}
    tabIndex={-1}
    onKeyDown={e => onEntryClearKeyDown(e, row.entry)}
    onBlur={e => e.currentTarget.classList.remove('focused')}
    onClick={e => {
      e.stopPropagation();
      removeEntry(row.entry);
    }}>
    <SVGBin className="vm-svg vm-stroke-xsm" />
  </Button>;

  const onEntryKeyDown = (e: KeyboardEvent, row: EntryRow) => {
    const entry = row.entry;

    if (e.key === 'Enter') {
      e.preventDefault();
      if (entry.type === 'item') {
        setSelection(entry.value, {name: 'search'});
        onItemClicked(entry);
      } else {
        onTermClicked(entry);
      }
    } else if (e.key === 'ArrowRight') {
      e.preventDefault();
      const elem = row.clearRef;
      if (elem) {
        elem.classList.add('focused');
        elem.focus();
      }
    } else if (e.key === 'Delete') {
      e.preventDefault();
      removeEntry(entry);
    }
  };

  const onLoadKeyDown = (e: KeyboardEvent, row: ItemRow) => {
    if (e.key === 'Enter' && row.type === 'load') {
      e.preventDefault();
      onLoadClicked();
    }
  };

  const onItemKeyDown = (e: KeyboardEvent, row: ItemRow) => {
    e.stopPropagation();
    if (e.key === 'ArrowUp' || e.key === 'ArrowLeft') {
      e.preventDefault();
      moveTo(row.index - 1);
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      moveTo(row.index + 1);
    } else if (e.key === 'Esc' || e.key === 'Escape') {
      e.preventDefault();
      parentRef.current?.focus();
    } else if (row.type === 'entry') {
      onEntryKeyDown(e, row);
    } else if (row.type === 'load') {
      onLoadKeyDown(e, row);
    }
  };

  const clearAllIndex = entries.length + +(loadMore);

  return <Fragment>
    {(Object.entries(groups) as [TimestampCheckpoint, EntryRow[]][]).map(([group, groupEntries]) => <section
      className="vm-search-group"
      key={group}
      aria-labelledby={`recent-${group}`}>
      <header>
        <span id={`recent-${group}`}>{translations[group].label}</span>
      </header>
      <ul className="vm-item-list" tabIndex={-1} >
        {groupEntries.map(row => row.entry.type === 'item'
          && <li
            ref={(elem) => row.ref = elem}
            className="vm-search-item"
            key={row.entry.value}
            tabIndex={focusedIndex === row.index ? 0 : -1}
            aria-setsize={entries.length}
            aria-posinset={row.index + 1}
            onKeyDown={e => onItemKeyDown(e, row)}
          >
            <LinkForSelect
              iri={row.entry.value}
              hint={{name: 'search'}}
              tabIndex={-1}
              onClick={() => onItemClicked(row.entry)}
            >
              <SearchItemContent mm={mm} iri={row.entry.value} />
            </LinkForSelect>
            {clearButton(row)}
          </li>
          || <li
            ref={(elem) => row.ref = elem}
            className="vm-search-item"
            key={row.entry.value}
            tabIndex={focusedIndex === row.index ? 0 : -1}
            aria-setsize={entries.length}
            aria-posinset={row.index + 1}
            onClick={() => onTermClicked(row.entry)}
            onKeyDown={e => onItemKeyDown(e, row)}
          >
            <SearchTermContent term={row.entry.value} />
            {clearButton(row)}
          </li>)}
      </ul>
    </section>)}
    <footer>
      {loadMore && <Button
        refCallback={(elem) => {
          if (elem) {
            itemRows[entries.length] = {type: 'default', index: entries.length, ref: elem};
          } else {
            delete itemRows[entries.length];
          }
        }}
        className="vm-label-button"
        tabIndex={focusedIndex === entries.length ? 0 : -1}
        onClick={() => onLoadClicked()}
        onKeyDown={e => onItemKeyDown(e, itemRows[entries.length])}
      >
        <Translate defaultMessage="Load more..." />
      </Button>}
      <Button
        refCallback={(elem) => {
          if (elem) {
            itemRows[clearAllIndex] = {type: 'default', index: clearAllIndex, ref: elem};
          } else {
            delete itemRows[clearAllIndex];
          }
        }}
        className="vm-label-button"
        aria-pressed="false"
        aria-label={intl.translate({defaultMessage: 'Clear All Recent'})}
        tabIndex={focusedIndex === clearAllIndex ? 0 : -1}
        onKeyDown={e => onItemKeyDown(e, itemRows[clearAllIndex])}
        onClick={clear}>
        <Translate defaultMessage="Clear All" />
      </Button>
    </footer>
  </Fragment>;
}

export type Props = Readonly<{
  contentRef: RefObject<HTMLElement>;
  parentRef: RefObject<HTMLElement>;
  onTermSelected: (term: string) => void;
  onClose: () => void;
}>;
export default function MaybeSearchRecent(props: Props): RNode | null {
  const {contentRef, parentRef, onTermSelected, onClose} = props;
  const intl = useTranslation();
  const entries = useSearchHistory();

  return entries.length && <section
    className="vm-float-block-section vm-search vm-search-recent"
    aria-label={PANEL_LABELS[Panel.RecentSearches]}>
    <section ref={contentRef} className="vm-float-block-content">
      <SearchRecentList
        entries={entries}
        parentRef={parentRef}
        onTermSelected={onTermSelected}
        onClose={onClose} />
    </section>
    <PanelHeader
      icon={<SVGMagnifyingGlass />}
      title={intl.translate({defaultMessage: 'Recent'})}
      panel={Panel.RecentSearches}
      skipFocus={true}
      onClose={onClose} />
  </section> || null;
}
