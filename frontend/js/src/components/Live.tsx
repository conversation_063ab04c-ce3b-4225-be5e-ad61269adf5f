// Top component for map as it finishes loading
//
// Will throw a Promise on render when api requests are still pending.
/** @jsx h */

import {h} from 'preact';
import {useMemo} from 'preact/hooks';

import {FocusProvider} from '../context/focus';
import {MaybeGeek} from '../context/geek';
import {TranslationProvider, useUserLanguage} from '../context/intl';
import {MapProvider} from '../context/ol';
import {PageProvider} from '../context/page';
import {PlaceInitialLoad} from '../context/place';
import {TutorialProvider} from '../context/tutorial';
import {usePlaceBase} from '../context/placebase';
import {ScreenProvider} from '../context/screen';
import {SearchHistoryProvider} from '../context/searchhistory';
import {HistoryProvider} from '../context/selectionhistory';
import {getMapLanguages, useModel} from '../context/triples';
import useOlSettings from '../hooks/useOlSettings';
import BaseMapLayer from './BaseMapLayer';
import CustomStyles from './CustomStyles';
import DataProviders from './DataProviders';
import MeaningMap from './MeaningMap';
import Page from './Page';

import type {SupportedLocale} from '../context/intl';
import type {OlOptions} from '../hooks/useOlSettings';
import type {ComponentChildren, MapData, RNode} from '../types';


export type PlatformProps = Readonly<{
  mapData: MapData;
  lang: string;
  setLang: (lang: SupportedLocale) => void;
}>;

function MapTitleLoader(props: Readonly<{mapName?: string}>): null {
  const {mapName} = props;
  const mm = useModel();
  const {view} = usePlaceBase();

  useMemo(() => {
    if (view) {
      const viewName: string|null = mm.nameOf(view);
      document.title = [mapName, viewName].filter(x => x && x.length).join(' - ');
    }
  }, [mapName, mm, view]);

  return null;
}

type MapLoaderProps = {
  ol: OlOptions;
  tilesSrc: string;
  children: ComponentChildren;
};
function MapLoader(props: MapLoaderProps): RNode {
  const {children, ol, tilesSrc} = props;
  return <MapProvider options={ol}>
    <BaseMapLayer extent={ol.extent} tilesSrc={tilesSrc} />
    {children}
  </MapProvider>;
}

// has access to map model from DataProvider
function Platform(props: PlatformProps): RNode {
  const mm = useModel();
  const {mapData, lang} = props;
  const ol = useOlSettings(mm.leafletMapSettings());

  return (
    <TranslationProvider lang={lang}>
      <MapLoader ol={ol} tilesSrc={mapData['tiles-src']}>
        <HistoryProvider>
          <SearchHistoryProvider mapIri={mapData.iri}>
            <FocusProvider>
              <ScreenProvider>
                <MapTitleLoader mapName={mapData.name} />
                <PlaceInitialLoad />
                <CustomStyles mapStyles={mapData['map-extra-css']} />
                <TutorialProvider>
                  <PageProvider>
                    <Page
                      {...props}
                      ol={ol}
                      map={<MeaningMap
                        {...ol}
                        tilesSrc={mapData['tiles-src']}
                      />}
                    />
                  </PageProvider>
                </TutorialProvider>
              </ScreenProvider>
            </FocusProvider>
          </SearchHistoryProvider>
        </HistoryProvider>
      </MapLoader>
    </TranslationProvider>
  );
}

export type Props = Readonly<{
  mapReader: () => MapData;
}>;

export default function Live(props: Props): RNode {
  const {mapReader} = props;
  const mapData = mapReader();
  const mapLanguages = useMemo(() => getMapLanguages(mapData), [mapData]);
  const [lang, setLang] = useUserLanguage(mapLanguages);

  return (
    <DataProviders {...{mapReader, lang}}>
      <MaybeGeek>
        <Platform
          lang={lang}
          setLang={setLang}
          mapData={mapData}
        />
      </MaybeGeek>
    </DataProviders>
  );
}
