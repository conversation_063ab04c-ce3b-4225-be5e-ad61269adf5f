// Display of unrecoverable error conditions for the app
/** @jsx h */

import {h} from 'preact';

import type {RNode} from '../types';


type Props = {
  error: Error;
};

export default function ErrorReport(props: Props): RNode {
  return <section className="vm-errorreport">
    <details>
      <summary>{props.error.toString()}</summary>
      <pre>{props.error.stack}</pre>
    </details>
    <button type="button" onClick={() => location.reload()}>Reload</button>
  </section>;
}
