// In list display of individual things
/** @jsx h */

import {h} from 'preact';

import {useCallback} from 'preact/hooks';
import {useAnnotationSummary, useSidebarState} from '../context/annotation';
import {enclosedKnownSymbolFor} from '../icons';
import LinkForSelect from './LinkForSelect';
import SvgSpeechBubble from '../svg/SVGSpeechBubble';

import type {Hint, RNode} from '../types';

type AnnotationsProps = {iri: string; showVotes: boolean; hint?: Hint};
export default function ItemListEntryAnnotations(props: AnnotationsProps): RNode|null {
  const {setExpandState} = useSidebarState();
  const annotations = useAnnotationSummary(props.iri);
  // TODO: pull in current annotations, and dispatch for creating them

  const clickHandler = useCallback(() => {
    setExpandState('expand');
  }, [setExpandState]);

  if (!annotations) {
    return null;
  }

  const votes = annotations.votes && props.showVotes
    ? enclosedKnownSymbolFor(annotations.votes.toString())
    : null;
  const comments = annotations.comments?.length || null;

  return <span className="annotations">
    {votes && <span className="vm-i">{votes}</span>}
    {comments && <LinkForSelect hint={props.hint} iri={props.iri} onClick={clickHandler}>
      {comments > 1 ? comments : ''}
      {' '}
      <SvgSpeechBubble />
    </LinkForSelect>}
  </span>;
}
