// Transforms the filters object into human readable form

/** @jsx h */
import {h} from 'preact';
import {useLayoutEffect, useRef, useState} from 'preact/hooks';
import {Translate, createBold} from '../intl';
import {localeInsensitive} from '../sort';
import type {Filters, Inclusion, RNode} from '../types';

function splitByInclusion(filter: Inclusion|undefined, namer: (iri: string) => string): {included: string; excluded: string} {
  let included = '';
  let excluded = '';
  const filterKeys = filter && Object.keys(filter);
  if (filterKeys && filter) {
    // TODO: this should also be aware of locale
    included = filterKeys.filter(x => filter[x] === true).map(namer).sort(localeInsensitive).join(', ');
    excluded = filterKeys.filter(x => filter[x] === false).map(namer).sort(localeInsensitive).join(', ');
  }

  return {included, excluded};
}

// Rough heuristic, character width and information density will vary.
const MAX_EXPLAIN_LENGTH = 200;

const enum State {
  UNSET,
  ASWAS,
  TOOLONG,
}

export type ExplainFiltersProps = Readonly<{
  filters: Filters;
  classNamer: (iri: string) => string;
  itemNamer: (iri: string) => string;
  lang: string;
}>;

export function ExplainFilters(props: ExplainFiltersProps): RNode {
  const {filters, classNamer, itemNamer, lang} = props;
  const {plottable, itemKinds, relation, search, aType} = filters;
  const spanRef = useRef<HTMLSpanElement>(null);
  const [state, setState] = useState<State>(State.UNSET);

  useLayoutEffect(() => {
    setState(State.UNSET);
  }, [filters, lang]);

  useLayoutEffect(() => {
    const span = spanRef.current;
    if (span && state === State.UNSET) {
      const content = span.textContent;
      if (content) {
        const length = content.replace(/\s+/g, ' ').length;
        setState(length > MAX_EXPLAIN_LENGTH ? State.TOOLONG : State.ASWAS);
      }
    }
  }, [state]);

  const aTypeSplit = splitByInclusion(aType, classNamer);
  const kindsSplit = splitByInclusion(itemKinds, itemNamer);
  const relationSplit = splitByInclusion(relation, itemNamer);

  const result = <Translate
    defaultMessage={`Showing items
    {hasSearch, select,
      true {<b>containing</b> "{search}"}
      other {}
    }
    {plottable, select,
      true {which <b>appear</b> on map}
      false {which do <b>not appear</b> on map}
      other {}
    }
    {aTypes, select,
      null {}
      other {<b>of class</b> {aTypes}}
    }
    {notATypes, select,
      null {}
      other {<b>not of class</b> {notATypes}}
    }
    {kinds, select,
      null {}
      other {<b>of category</b> {kinds}}
    }
    {notKinds, select,
      null {}
      other {<b>not of category</b> {notKinds}}
    }
    {related, select,
      null {}
      other {<b>related to</b> {related}}
    }
    {notRelated, select,
      null {}
      other {<b>not related to</b> {notRelated}}
    }`}
    values={{
      plottable,
      search,
      hasSearch: !!search,
      aTypes: aTypeSplit.included || null,
      notATypes: aTypeSplit.excluded || null,
      kinds: kindsSplit.included || null,
      notKinds: kindsSplit.excluded || null,
      related: relationSplit.included || null,
      notRelated: relationSplit.excluded || null,
      b: createBold,
    }} />;

  if (state === State.TOOLONG) {
    return <Translate defaultMessage="Multiple filters applied" />;
  }

  return <span ref={spanRef}>{result}</span>;
}
