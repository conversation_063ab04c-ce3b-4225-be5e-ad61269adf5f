// Components and events to use on the map
/** @jsx h */

import {Fragment, h} from 'preact';
import {useEffect} from 'preact/hooks';

import {flagInStorage} from '../flags';
import {useVoteToggle} from '../context/annotation';
import {useMap, useZoom} from '../context/ol';
import {useGetSelection, useMatchingView, useNavigation} from '../context/place';
import {useModel} from '../context/triples';
import {MaybeMapEdges} from './MapEdges';
import MapColorRegions from './MapColorRegions';
import MapGeek from './MapGeek';
import MapItems from './MapItems';
import MapKeys from './MapKeys';
import MapLabels from './MapLabels';
import useMapAnimations from '../hooks/useMapAnimations';

import type {Extent} from 'ol/extent';
import type {Hint, RNode} from '../types';
import type MapBrowserEvent from 'ol/MapBrowserEvent';

function scaleFromZoom(zoom: number): number {
  // Render things six times larger, or a multiple of that on bigger maps.
  return 6 << Math.max(0, zoom - 5);
}

export type Props = {
  extent: Extent;
  maxNativeZoom: number;
};

export default function MapContent({extent, maxNativeZoom}: Props): RNode {
  const {setSelection} = useNavigation();
  const getSelection = useGetSelection();
  const toggleVote = useVoteToggle();
  const [isVoting, _] = useMatchingView('voting');
  const {map} = useMap();
  const mm = useModel();

  // TODO: unhack this in some form
  useZoom();

  // TODO: Not clear this is the right interface, but functional for now.
  useMapAnimations(extent, isVoting);

  useEffect(() => {
    const handleClick = (event: MapBrowserEvent<PointerEvent|KeyboardEvent|WheelEvent>) => {
      const mapHint: Hint = {name: 'map'};
      const target = event.originalEvent.target as HTMLElement|null;
      let iri = null;
      if (target?.nodeName === 'CANVAS') {
        for (const feature of map.getFeaturesAtPixel(event.pixel)) {
          iri = feature.get('iri');
          if (iri) {
            break;
          }
        }

        setSelection(iri, mapHint);
        event.preventDefault();
      } else if (target?.closest('.vm-marker')) {
        iri = target.closest('.vm-marker')?.getAttribute('data-iri') ?? null;
        if (getSelection() !== iri) {
          setSelection(iri, mapHint);
        } else if (isVoting && iri != null) {
          toggleVote(iri);
        }

        event.preventDefault();
        target.focus();
      } else if (target?.closest('.vm-map-label')) {
        iri = target.closest('.vm-map-label')?.getAttribute('data-iri') ?? null;
        setSelection(iri, mapHint);
        event.preventDefault();
      } else if (target?.getAttribute('data-iri')) {
        iri = target.getAttribute('data-iri');
        setSelection(iri, mapHint);
        event.preventDefault();
      }
    };
    map.on('click', handleClick);
    return () => {
      map.un('click', handleClick);
    };
  }, [map, getSelection, setSelection, isVoting, toggleVote, mm]);

  return <Fragment>
    <MapKeys />
    <MaybeMapEdges />
    <MapColorRegions />
    {flagInStorage('geek') ? <MapGeek /> : null }
    <MapLabels scaleFactor={scaleFromZoom(maxNativeZoom)} />
    <MapItems showVotes={isVoting} />
  </Fragment>;
}
