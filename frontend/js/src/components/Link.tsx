// Basic anchor wrapper.
/** @jsx h */

import {h} from 'preact';
import {forwardRef} from 'preact/compat';

import type {AnchorAttrs, ForwardRef, RNode} from '../types';


type LinkProps = Readonly<Omit<AnchorAttrs, 'aria-disabled'>> & {
  disabled?: boolean;
};
const Link = forwardRef(({children, disabled, href, ...remainingProps}: LinkProps, ref: ForwardRef<HTMLAnchorElement>): RNode => {
  return <a
    {...remainingProps}
    ref={ref}
    href={disabled ? undefined : href}
    role={disabled ? 'link' : undefined}
    aria-disabled={disabled}
  >
    {children}
  </a>;
});

export default Link;
