// Base map layer processing

/** @jsx h */

import {useCallback, useEffect, useLayoutEffect, useMemo, useRef} from 'preact/hooks';

import LayerGroup from 'ol/layer/Group';
import ImageLayer from 'ol/layer/Image';
import Static from 'ol/source/ImageStatic';

import {useGeekery} from '../context/geek';
import {useMap} from '../context/ol';
import {usePlace} from '../context/place';
import {usePlatformStates, usePlatformStatesUpdater} from '../context/platform';
import {useModel} from '../context/triples';
import {useDetailLevelByZoomOf} from '../hooks/lenses';
import {IRIS} from '../rdf';
import {lensFromLocation} from '../triplejump';

import type {Extent} from 'ol/extent';
import type {Term} from '../types';

type Layers = {
  [key: string]: ImageLayer<Static>;
};
type LayersGroup = [
  string,
  Layers,
];

function getBase(src: string) {
  return src
    .split('#')[0]
    .replace('{z}-{x}-{y}', '0-0-0');
}

export type Props = Readonly<{
  tilesSrc: string;
  extent: Extent;
}>;
/**
 * Preloads static image 0-0-0.png current detail on detail change
 * and map change.
 *
 * Preloading in memory is not enough for image to be rendered immediately.
 * It needs to be rendered on layer at least once, but not displayed.
 * There is a hack here, where setting visibility to true, but opacity to 0
 * would load the image but not have it visible.
 * The steps:
 * 1/ set visibility to all base layers to true to load in layer to preload
 *    and opacity 0 to not reveal them.
 * 2/ set opacity on the current base tile to 1 to have it displayed.
 * 3/ set visibility to all but base to false.
 */
export default function BaseMapLayer(props: Props): null {
  const {
    extent,
    tilesSrc,
  } = props;
  const mm = useModel();
  const {lensLocation} = usePlace();
  const {map} = useMap();
  const {tilesOverride} = useGeekery();
  const {map: mapReady} = usePlatformStates();
  const updateStates = usePlatformStatesUpdater();
  const recalc = useRef(true);
  // move to a context provider
  const detailLevelByZoomOf = useDetailLevelByZoomOf();

  const baseLayers = useMemo(() => {
    recalc.current = true;
    const baseLayers = new LayerGroup({zIndex: 1});
    map.addLayer(baseLayers);
    return baseLayers;
  }, [map]);

  useEffect(() => () => {
    map.removeLayer(baseLayers);
    updateStates({base: false});
  }, [map, baseLayers, updateStates]);

  // Map lenses used in vm:onLens triples to the lowest lens with any tiles in their stack.
  const [groupedLayers, allLayers]: [{[iri: string]: LayersGroup}, Layers] = useMemo(() => {
    const byMappedLens: {[iri: string]: LayersGroup} = {};
    const byUrl: Layers = {};
    const addImageLayer = (url: string) => {
      const layer = new ImageLayer({
        source: new Static({
          url,
          imageExtent: extent,
        }),
        opacity: 0,
        visible: true,
      });
      baseLayers.getLayers().push(layer);
      return layer;
    };

    (mm._store.triplesWithPred(IRIS.VM.onLens) as Term[]).forEach(({obj}) => {
      const mappedLens = obj as string;
      const lensStack: string[] = mm.lensStackOfLens(mappedLens);
      for (let i = lensStack.length - 1; i >= 0; i--) {
        const lens = lensStack[i];
        const details = mm.detailsOf(lens);
        // having any detail is good enough, because detail should have src.
        if (details.length) {
          for (const detail of details) {
            const src = mm.usesMapTilesOf(detail) || getBase(tilesSrc);
            const baseTileUrl = getBase(src);
            (byMappedLens[mappedLens] ??= [lens, {}])[1][detail] = byUrl[baseTileUrl] ??= addImageLayer(baseTileUrl);
          }
          break;
        }
      }
    });

    const defaultUrl = getBase(tilesSrc);
    byMappedLens['default'] = ['default', {default: byUrl[defaultUrl] ??= addImageLayer(defaultUrl)}];

    return [byMappedLens, byUrl];
  }, [mm, tilesSrc, extent, baseLayers]);

  const mappedLens = lensFromLocation(mm, lensLocation);
  const [lens, layers] = mappedLens && groupedLayers[mappedLens] || groupedLayers['default'];
  const detailAtZoom = detailLevelByZoomOf(lens)[0] || 'default';
  const base = layers[detailAtZoom];

  const reveal = useCallback((layer: ImageLayer<Static>, base: boolean = true) => {
    if (!tilesOverride || tilesOverride === 'none' || (base && tilesOverride === 'base')) {
      layer.setVisible(true);
      layer.setOpacity(1);
    }
  }, [tilesOverride]);

  useLayoutEffect(() => {
    const setLayersVisibility = () => {
      Object.values(allLayers).forEach(layer => layer.setVisible(false));
      reveal(base);
      updateStates({base: true});
    };
    if (mapReady) {
      if (recalc.current) {
        recalc.current = false;
        const handler = () => {
          setLayersVisibility();
          map.un('loadend', handler);
        };
        map.on('loadend', handler);
      } else {
        setLayersVisibility();
      }
    }
  }, [allLayers, base, map, mapReady, reveal, updateStates]);

  useLayoutEffect(() => {
    reveal(base);
    return () => base.setVisible(false);
  }, [base, reveal]);

  return null;
}
