// Link to select a single triple subject
/** @jsx h */

import {h} from 'preact';
import {useCallback} from 'preact/hooks';

import {useNavigation} from '../context/place';

import Link from './Link';

import type {AnchorAttrs, Hint, RNode} from '../types';

export type Props = Readonly<Omit<AnchorAttrs, 'onClick'|'href'> & {
  iri: string;
  disabled?: boolean;
  hint?: Hint;
  skipHistory?: boolean;
  onClick?: (e: MouseEvent) => void;
}>;

export default function LinkForSelect(props: Props): RNode {
  const {hint, iri, skipHistory, onClick, ...remainingProps} = props;
  const {hrefForSelection, setSelection} = useNavigation();

  const clickHandler = useCallback((e: MouseEvent) => {
    if (iri) {
      e.preventDefault();
      setSelection(iri, hint, skipHistory);
    }
    if (onClick) {
      onClick(e);
    }
  }, [iri, hint, onClick, setSelection, skipHistory]);

  return <Link
    {...remainingProps}
    data-iri={iri}
    href={hrefForSelection(iri)}
    onClick={clickHandler}
  />;
}
