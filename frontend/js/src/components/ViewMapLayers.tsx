// Map layers for current view

import {useLayoutEffect, useMemo, useRef} from 'preact/hooks';

import {makeStackTransform, useGeekery} from '../context/geek';
import type {BackgroundType} from '../context/ol';
import {useMap} from '../context/ol';
import {usePlace} from '../context/place';
import {useModel} from '../context/triples';
import {useLensDetailStackByZoom} from '../hooks/lenses';
import useMapLayersUpdate from '../hooks/useMapLayersUpdate';
import {parseTilesSrc} from '../tiles';
import {lensFromLocation} from '../triplejump';

import type {Map} from 'ol';
import type {TileLayerOptionSubset} from '../hooks/useOlSettings';
import type {MapModel} from '../rdfmodels';

function updateStackWithFinalStyles(mm: MapModel, srcStack: string[], origins: string[]): ReturnType<typeof parseTilesSrc>['styles']|null {
  let finalStyles = null;
  for (let i = 0; i < origins.length; ++i) {
    const tiles = mm.usesMapTilesOf(origins[i]);
    if (tiles) {
      const {src, styles} = parseTilesSrc(tiles);
      srcStack.unshift(src);
      if (styles.background !== 'transparent') {
        finalStyles = styles;
        break;
      }
    }
  }
  return finalStyles;
}

function usePendingBackground(map: Map, background: BackgroundType): void {
  const targetPending = useRef<boolean|null>(false);
  useLayoutEffect(() => {
    const applyBackground = (bg: string) => {
      const target = map.getTargetElement();
      if (target) {
        // if bg is an invalid background value, it won't change,
        // but we still need to undo pending step.
        if (target.style.background === 'transparent') {
          target.style.background = '';
        }
        target.style.background = bg;
      }
    };
    const handleLoaded = () => {
      if (targetPending.current) {
        applyBackground(background);
        targetPending.current = false;
      }
    };
    const handlePending = () => {
      applyBackground('transparent');
      targetPending.current = true;
    };
    map.on('change:target', handlePending);
    map.on('loadend', handleLoaded);
    handlePending();
    return () => {
      map.un('change:target', handlePending);
      map.un('loadend', handleLoaded);
    };
  }, [background, map]);
}

export function evaluateStack(
  mm: MapModel,
  view: string|null,
  lensDetails: string[],
  fallbackTilesSrc: string,
  filterStack: ReturnType<typeof makeStackTransform> = (s: string[]) => s,
): [BackgroundType, string] {
  let current = view;
  let finalStyles = null;
  const stack: string[] = [];
  finalStyles = current && updateStackWithFinalStyles(mm, stack, lensDetails);
  /**
   * During reshape we may create abstract lens details to serve labels, but on no
   * actual tile (no geography). We should consider this a legacy map and check
   * for view tiles, else we consider the lens stack the entire source for tiles.
   */
  if (!stack.length) {
    /**
     * Currently the only parent view case is a storypoint -> story.
     * We do not support parent story on a lens stack, but only direct view tiles
     * (in legacy maps).
     */
    while (current && !finalStyles) {
      finalStyles = updateStackWithFinalStyles(mm, stack, [current]);
      current = mm.parentView(current);
    }
  }
  if (!finalStyles) {
    const {src, styles} = parseTilesSrc(fallbackTilesSrc);
    stack.unshift(src);
    finalStyles = styles;
  }
  return [finalStyles?.background || '', filterStack(stack).join('\n')];
}

export type Props = Readonly<{
  tilesSrc: string;
} & TileLayerOptionSubset>;
export default function ViewMapLayers(props: Props): null {
  const {map} = useMap();
  const mm = useModel();
  const {view, lensLocation} = usePlace();
  const {tilesOverride} = useGeekery();
  const lensDetailStack = useLensDetailStackByZoom().join('\n');
  const {
    attribution,
    maxNativeZoom,
    tilesSrc,
  } = props;

  const resolveStack = makeStackTransform(tilesOverride);
  const lens = lensFromLocation(mm, lensLocation);

  const [bg, stack] = useMemo(() => {
    const lensDetails = lensDetailStack.split('\n').filter(Boolean);
    return evaluateStack(mm, view, lensDetails, tilesSrc, resolveStack);
  }, [lensDetailStack, mm, tilesSrc, resolveStack, view]);

  usePendingBackground(map, bg);
  useMapLayersUpdate(attribution, maxNativeZoom, lens, stack, bg);

  return null;
}
