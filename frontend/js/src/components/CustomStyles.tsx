/**
 * Custom styles overriding CSS.
 *
 * The style overriding order is CSS, map model, Django CSS.
 * The page styling (e.g. button colors) should be calculated against the final styles.
 */
/** @jsx h */

import {Fragment, h} from 'preact';

import {useModel} from '../context/triples';
import {colorRGBWithOpacity, getRGB} from '../functions/colorResolver';
import {IRIS} from '../rdf';

import type {ColorRGB} from '../functions/colorResolver';
import type {MapModel} from '../rdfmodels';
import type {RNode, Term} from '../types';

type CssProperties = {[property: string]: string};
const BACKGROUND_COLOR: ColorRGB = {r: 255, g: 255, b: 255};
const HOVER_OPACITY = 0.8;

function buildFilterToggleStyles() {
  const primaryColor = getRGB('--color-primary-rgb');
  const backgroundColor = BACKGROUND_COLOR;
  const lightColor = colorRGBWithOpacity(primaryColor, backgroundColor, HOVER_OPACITY);
  return {
    ['--color-toggle']: `rgb(${[primaryColor.r, primaryColor.g, primaryColor.b].join(',')})`,
    ['--color-toggle-semi']: `rgb(${[lightColor.r, lightColor.g, lightColor.b].join(',')})`,
    ['--color-toggle-inverse']: `rgb(${[backgroundColor.r, backgroundColor.g, backgroundColor.b].join(',')})`,
  };
}

function buildColorCategoryStyles(mm: MapModel) {
  return (mm._store.triplesWithPred(IRIS.VM.cssVariable) as Term[]).reduce<CssProperties>((colors, {subj, obj: cssVariable}) => {
    const classChain = mm._classChains()[mm.classOf(subj)];
    if (classChain && classChain.includes(IRIS.VM.ColorCategory)) {
      const hexCode = mm.hexCodeOf(subj);
      if (cssVariable && hexCode) {
        colors[cssVariable as string] = hexCode;
      }
    }
    return colors;
  }, {});
}

function MaybeRootStyle({properties}: {properties: CssProperties}): RNode|null {
  return Object.keys(properties).length > 0 ? <style>
    {':root {\n'
    + Object.entries(properties).map(([p, value]) => `${p}: ${value};`).join('\n')
    + '\n}'}
  </style> : null;
}

function MaybeModelStyle() {
  const mm = useModel();
  // TODO: a standardized ontology for css styles in model
  const properties = buildColorCategoryStyles(mm);
  return <MaybeRootStyle {...{properties}} />;
}

function MaybePageStyle() {
  const properties = buildFilterToggleStyles();
  return <MaybeRootStyle {...{properties}} />;
}

type Props = {
  mapStyles?: string;
};

export default function CustomStyles({mapStyles}: Props): RNode {
  return <Fragment>
    <MaybeModelStyle />
    {mapStyles && <style>{mapStyles}</style>}
    <MaybePageStyle />
  </Fragment>;
}
