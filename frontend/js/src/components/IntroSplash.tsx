// Introduction to map as a splash screen
/** @jsx h */

import {Fragment, h} from 'preact';
import {useEffect, useRef} from 'preact/hooks';

import {useMarkdown} from '../context/mark';
import {PANEL_LABELS, Panel, usePanelUpdaters} from '../context/panels';
import {useModel} from '../context/triples';
import {useTutorial} from '../context/tutorial';
import {Translate} from '../intl';
import SVGLogo from '../svg/SVGLogo';

import PlatformLicense from './PlatformLicense';
import PanelHeader from './shared/PanelHeader';

import type {RNode} from '../types';
import Button from './Button';

export type Props = Readonly<{
  onClose: () => void;
}>;

export default function IntroSplash(props: Props): RNode {
  const kd = useMarkdown();
  const mm = useModel();
  const view = 'http://visual-meaning.com/rdf/introduction';
  const desc = mm.descriptionOf(view) || '';
  const contentRef = useRef<HTMLDivElement>(null);
  const {changeOpen} = usePanelUpdaters(Panel.About);
  const {startTutorial} = useTutorial();

  useEffect(() => {
    contentRef.current?.focus();
  }, []);

  return <div className="vm-splash">
    <div />
    <section
      aria-label={PANEL_LABELS[Panel.About]}
      aria-describedby="vm-about-desc"
      ref={contentRef} tabIndex={-1}>
      <PanelHeader
        panel={Panel.About} skipFocus={true} onClose={props.onClose} customControls={<Fragment><Button
          className="vm-primary-button vm-toggle-button"
          data-testid="intro-splash-start-tutorial-button"
          onClick={() => {
            changeOpen(false);
            startTutorial();
          }}
        >
          <Translate defaultMessage="Start Tutorial" />
        </Button></Fragment>} />
      <div className="contents">
        {
          desc !== '' && <div
            className="vm-c"
            key="desc"
            id="vm-about-desc"
            {...kd.render(desc)} />
        }
        <div>
          <PlatformLicense />
          <p className="vm-d"><a href="/privacy-policy/">Privacy Policy</a></p>
        </div>
        <a className="vm-logo" href="https://visual-meaning.com" target="_blank">
          <SVGLogo />
        </a>
        <p className="vm-build-info">version <code>{BUILD_INFO.commit}</code></p>
      </div>
    </section>
  </div>;
}
