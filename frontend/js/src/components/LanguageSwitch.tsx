// Component for switching language
/** @jsx h */

import {h} from 'preact';

import {Translate} from '../intl';

import type {SupportedLocale} from '../context/intl';
import type {RNode, TargetedEvent} from '../types';

function getDisplayForLang(lang: string) {
  switch (lang) {
    case 'en':
      return 'en - English';
    case 'fr':
      return 'fr - Français';
    case 'pl':
      return 'pl - Polski';
    case 'ru':
      return 'ru - Русский';
    case 'zh':
      return 'zh - 中文';
    default:
      return lang;
  }
}

export type LanguageProps = Readonly<{
  lang: string;
  languages: string[];
  setLang: (lang: SupportedLocale) => void;
}>;
export function LanguageSwitch({setLang, lang, languages}: LanguageProps): RNode|null {
  const changeLanguage = (e: TargetedEvent<HTMLSelectElement>) => {
    const newLang = e.currentTarget.value as SupportedLocale;
    setLang(newLang);
  };
  const language = languages.length !== 0 ? (<div className="vm-languages">
    <label><Translate defaultMessage="Language:" />&nbsp;</label>
    <select onChange={changeLanguage}>
      {languages.map(l => <option key={l} value={l} selected={l === lang}>{getDisplayForLang(l)}</option>)}
    </select>
  </div>)
    : null;

  return language;
}
