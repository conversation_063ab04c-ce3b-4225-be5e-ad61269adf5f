// Rendering of connections between map objects
/** @jsx h */
import {h} from 'preact';
import {useEffect, useMemo} from 'preact/hooks';

import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';

import {translateCoord, useMap} from '../context/ol';
import {usePlace} from '../context/place';
import {usePlatformStates} from '../context/platform';
import {useModel} from '../context/triples';
import flag from '../flags';
import {getGloss, getGlossForTrust} from '../functions/glosschoose';
import {featureWithGloss} from '../functions/glossdraw';
import {useCurrentLabels} from '../hooks/labels';
import useMapItems from '../hooks/useMapItems';
import {IRIS} from '../rdf';
import {partitionSubj} from '../triplejump';

import type {Coord} from '../context/ol';
import type {Gloss} from '../functions/glosschoose';
import type {Existence, RNode} from '../types';

type Locations = {[iri: string]: Coord[]};
type Relations = [string, string, Gloss][];
type LinkValue = {[key in Gloss]: [Coord, Coord][]};
type Links = {[iri: string]: LinkValue};

export type Props = {
  filter: (gloss: Gloss) => boolean;
};

const NOT_LINKED: Readonly<Existence> = {
  [IRIS.VM.atGeoPoint]: true,
  [IRIS.VM.name]: true,
  [IRIS.VM.description]: true,
  [IRIS.VM.aidsCapitalType]: true,
  [IRIS.VM.harmsCapitalType]: true,
  [IRIS.VM.ofStory]: true,
  [IRIS.RDF.type]: true,
};

function generateLinks(locationMap: Locations, relations: Relations): Links {
  const links: Links = {};
  relations.forEach(([aIRI, bIRI, gloss]) => {
    const aLoc = locationMap[aIRI];
    const bLoc = locationMap[bIRI];
    if (aLoc && bLoc) {
      links[aIRI] ||= {} as LinkValue;
      links[bIRI] ||= {} as LinkValue;
      for (const a of aLoc) {
        for (const b of bLoc) {
          const pair: [Coord, Coord] = [a, b];
          (links[aIRI][gloss] ||= []).push(pair);
          (links[bIRI][gloss] ||= []).push(pair);
        }
      }
    }
  });
  return links;
}

function allEdges() {
  return true;
}

export function MaybeMapEdges(): RNode | null {
  const mm = useModel();
  const {view} = usePlace();
  const filter = useMemo(() => {
    const showViewEdges = () => view ? !!mm.showEdgesOf(view) : false;
    if (flag(mm, 'edgeDisplay') || showViewEdges()) {
      return allEdges;
    }
    return null;
  }, [mm, view]);
  return filter ? <MapEdges {...{filter}} /> : null;
}

export default function MapEdges({filter}: Props): null {
  const {map} = useMap();
  const {all: items} = useMapItems();
  const mm = useModel();
  const {selection, view} = usePlace();
  const labels = useCurrentLabels();
  const {base} = usePlatformStates();

  const connectionsMap = useMemo(() => {
    const locationMap: Locations = {};
    const relations: Relations = [];
    const relationsAdd = ([from, to, gloss]: [string, string, Gloss]) => {
      if (filter(gloss)) {
        relations.push([from, to, gloss]);
      }
    };

    // locations
    labels.forEach(label => {
      if (label.targetIRI) {
        const coord = translateCoord(label.point);
        if (label.targetIRI in locationMap) {
          locationMap[label.targetIRI].push(coord);
        } else {
          locationMap[label.targetIRI] = [coord];
        }
      }
    });
    items.forEach(({info, point}) => {
      if (info.kind === 'item') {
        (locationMap[info.iri] ??= []).push(translateCoord(point));
      }
    });

    // relations
    for (const targetIRI in locationMap) {
      const [{
        [IRIS.RDF.type]: t,
      }, rest] = partitionSubj(mm, targetIRI, NOT_LINKED, false);
      rest.forEach(([p, o]) => {
        if (o in locationMap) {
          relationsAdd([targetIRI, o, getGloss(mm, t, p)]);
        } else if (p === IRIS.VM.hasTrustPerception) {
          const trustee = mm.trusteeOf(o);
          const trustScore = mm.trustScoreOf(o);
          const importanceScore = mm.importanceScoreOf(o);
          relationsAdd([targetIRI, trustee, getGlossForTrust(trustScore, importanceScore)]);
        }
      });
    }
    return generateLinks(locationMap, relations);
  }, [mm, items, labels, filter]);

  useEffect(() => {
    if (!selection || !base) {
      return undefined;
    }
    const connections = connectionsMap[selection];
    if (!connections) {
      return undefined;
    }
    const source = new VectorSource({wrapX: false});
    for (const gloss in connections) {
      const coords = connections[gloss as Gloss];
      if (coords.length) {
        source.addFeature(featureWithGloss(coords, gloss as Gloss));
      }
    }
    const layer = new VectorLayer({
      source,
      updateWhileAnimating: true,
      updateWhileInteracting: true,
      zIndex: 2,
    });
    map.addLayer(layer);
    return () => {
      map.removeLayer(layer);
    };
  }, [base, connectionsMap, map, mm, selection, view]);

  return null;
}
