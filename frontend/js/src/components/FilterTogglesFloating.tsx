// Toggles filters

/** @jsx h */
import {Fragment, h} from 'preact';
import {forwardRef} from 'preact/compat';
import {useCallback, useMemo, useRef} from 'preact/hooks';

import {usePlace} from '../context/place';
import {useModel} from '../context/triples';
import useFilterToggles from '../hooks/useFilterToggles';
import {useTranslation} from '../intl';
import {localeInsensitive} from '../sort';
import SVGFilterRestore from '../svg/SVGFilterRestore';
import Button from './Button';
import FilterToggle from './FilterToggle';
import Scrollable from './shared/Scrollable';

import type {ForwardRef, TargetedEvent} from '../types';

type FilterTogglesFloatingProps = {
  maxWidth?: string;
};
const FilterTogglesFloating = forwardRef(({maxWidth}: FilterTogglesFloatingProps, ref: ForwardRef<HTMLElement>) => {
  const mm = useModel();
  const {view} = usePlace();
  const intl = useTranslation();
  const touched = useRef<boolean>(false);

  const classes = useMemo<string[]>(() => mm.classOfInterestOf(view), [mm, view]);
  const {isResettable, reset: resetFilters, checkFilter, toggleFilter} = useFilterToggles();

  const namedClasses = useMemo<{iri: string; name: string}[]>(() => {
    const namedClasses = classes.map(iri => ({iri, name: mm.classNamePlural(iri) || '?'}));
    namedClasses.sort((a, b) => localeInsensitive(a.name, b.name));

    return namedClasses;
  }, [mm, classes]);

  const touch = useCallback(() => {
    touched.current = true;
  }, []);

  const handleFocus = useCallback((e: TargetedEvent<HTMLButtonElement>) => {
    /**
     * onFocus is used for tabbing through toggles and scroll them into view.
     * This creates undesired jumping when clicking, which inherently focuses
     * on them. The touched ref mitigates scrolling on such action.
     */
    if (touched.current) {
      touched.current = false;
      return;
    }
    // block: 'nearest' is needed for anchoring verticality
    e.currentTarget.scrollIntoView({behavior: 'smooth', block: 'nearest', inline: 'center'});
  }, []);

  return namedClasses.length > 0 && <section
    className="vm-float-block vm-filter-toggle-container"
    id="filter-toggles-container"
    ref={ref}
    role="group"
    aria-label="Filter Toggles"
    style={{maxWidth}}>
    <Scrollable>
      {namedClasses.map(cls => {
        const filterType = mm.isCategoryInstance(cls.iri) ? 'itemKinds' : 'aType';
        return <FilterToggle
          key={cls.iri}
          iri={cls.iri}
          name={cls.name}
          showClass={true}
          isSelected={checkFilter(filterType, cls.iri)}
          onPointerDown={touch}
          onFocus={handleFocus}
          onToggle={iri => toggleFilter(filterType, iri)} />;
      })}
    </Scrollable>
    {isResettable && <Fragment>
      <div className="vm-separator vertical"></div>
      <Button
        className="vm-toggle-button vm-restore-button"
        tooltipMessage={intl.translate({defaultMessage: 'Restore default filters'})}
        onClick={resetFilters}>
        <SVGFilterRestore />
      </Button>
    </Fragment>}
  </section> || null;
});

export default FilterTogglesFloating;
