// In list display of individual things
/** @jsx h */

import {Fragment, h} from 'preact';
import {forwardRef} from 'preact/compat';

import {useMarkdown} from '../context/mark';
import {useModel} from '../context/triples';
import {broader, exampleFor, explainType} from '../functions/explainItems';
import {Icon} from '../icons';
import {maybeMedia} from '../media';
import {IRIS} from '../rdf';
import GenericRelations from './GenericRelations';
import {MaybeInvolvements} from './Involvement';
import ItemListEntryAnnotations from './ItemListEntryAnnotations';
import LinkForSelect from './LinkForSelect';
import MaybeTrustPerception from './TrustPerception';

import type {MarkedEnv} from 'markdown-it-marked';
import type {MarkdownContent} from '../context/mark';
import type {Attrs, Existence, ForwardRef, Hint} from '../types';

export type ExpandType = 'none'|'selection'|'all';

export type Props = Omit<Attrs['li'], 'name'> & Readonly<{
  expand: ExpandType;
  expandableContent: boolean;
  iri: string;
  isSelected: boolean;
  name: MarkdownContent;
  prominent: boolean;
  showVotes: boolean;
  hint?: Hint;
  markEnv?: MarkedEnv|undefined;
}>;

const HANDLED_PREDS: Existence = {
  [IRIS.SKOS.altLabel]: true,
  [IRIS.SKOS.comment]: true,
  [IRIS.SKOS.definition]: true,
  [IRIS.SKOS.example]: true,
  [IRIS.VM.aidsCapitalType]: true,
  [IRIS.VM.broader]: true,
  [IRIS.VM.description]: true,
  [IRIS.VM.harmsCapitalType]: true,
  [IRIS.VM.hasContentType]: true,
  [IRIS.VM.hasTrustPerception]: true,
  [IRIS.VMHE.hasInvolvement]: true,
  [IRIS.VM.hasDocumentType]: true,
  [IRIS.VM.canEmbed]: true,
};

export const ItemListEntry = forwardRef((props: Props, ref: ForwardRef<HTMLLIElement>) => {
  const mm = useModel();
  const kd = useMarkdown();
  const {iri, name, showVotes, prominent, expand, expandableContent, isSelected, markEnv, hint, ...restProps} = props;
  const full = expand === 'all' || expand === 'selection' && isSelected;

  return <li
    ref={ref}
    aria-current={isSelected && 'location'}
    className={prominent ? 'vm-thing vm-prominent' : 'vm-thing'}
    {...restProps}
  >
    <LinkForSelect hint={hint} iri={iri}>
      <div className="vm-label">
        <div>
          <div className="vm-i-container">
            <Icon iri={iri} offset={true} />
          </div>
          <h4 {...name} />
          <ItemListEntryAnnotations iri={iri} showVotes={showVotes} hint={hint} />
        </div>
        {full && explainType(mm, iri)}
      </div>
    </LinkForSelect>
    {full && <Fragment>
      {maybeMedia(mm, iri)}
      <div
        className="vm-c"
        key="desc"
        {...kd.render(mm.descriptionOf(iri) || '', markEnv)}
      />
      {exampleFor(mm, iri)}
      {broader(mm, iri)}
      <MaybeTrustPerception iri={iri} expandable={expandableContent} />
      <MaybeInvolvements iri={iri} hint={hint} />
      <GenericRelations iri={iri} handledPreds={HANDLED_PREDS} hint={hint} />
    </Fragment>}
  </li>;
});

export default ItemListEntry;
