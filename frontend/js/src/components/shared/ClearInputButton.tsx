/** @jsx h */

import {h} from 'preact';

import {useTranslation} from '../../intl';
import SV<PERSON><PERSON> from '../../svg/SV<PERSON>ross';
import Button from '../Button';

import type {RNode} from '../../types';

type Props = {
  value: string;
  onClearInput: ()=>void;

};

/**
 * A button component that clears the input when clicked.
 * The button is rendered only if the `value` prop is a non-empty string
 * and becomes visible when the input container is hovered or focused (achieved via CSS).
 *
 * @component
 * @param {Object} props - The props for the ClearInputButton component.
 * @param {string} props.value - A string representing the current value of the input.
 *                               The button is rendered if this string is non-empty.
 * @param {() => void} props.onClearInput - A callback function invoked when the button is clicked to clear the input.
 */
export default function ClearInputButton({value, onClearInput}:Props):RNode | null {
  const intl = useTranslation();
  if (!value) {
    return null;
  }

  return (
    <Button
      onClick={onClearInput}
      type="button"
      className="vm-icon-button"
      tooltipMessage={intl.translate({defaultMessage: 'Clear input'})}
    >
      <SVGCross />
    </Button>
  );
}
