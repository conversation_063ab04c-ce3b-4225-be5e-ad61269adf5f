
/** @jsx h */

import {h} from 'preact';
import {forwardRef} from 'preact/compat';
import {useEffect, useRef} from 'preact/hooks';

import {PANEL_LABELS, usePanel, usePanelUpdaters} from '../../context/panels';
import {useTranslation} from '../../intl';
import SVGCross from '../../svg/SVGCross';
import Button from '../Button';

import type {Panel} from '../../context/panels';
import type {Attrs, ForwardRef, RNode, RefCallback} from '../../types';
import ScrollingText from './ScrollingText';

export function focusElement<T extends HTMLElement>(skip?: boolean): RefCallback<T> {
  return (e) => {
    if (!e || skip) {
      return;
    };
    e.focus();
  };
}

type Props = Omit<Attrs['header'], 'title'> & {
  panel: Panel;
  title?: string;
  subTitle?: RNode;
  icon?: RNode;
  customControls?: RNode;
  skipFocus?: boolean;
  closeDisabled?: boolean;
  deferredFocusMs?: number;
  onClose?: () => void;
};

/**
 * A Panel header with convenient layout for icon, title text and close
 * button, extended to support additional text and controls.
 *
 * @param props.panel Supported Panel type.
 * @param props.title Title name as text.
 * @param props.subTitle A sub-title element, under the title.
 * @param props.icon Icon element.
 * @param props.customControls Additional controls beside the close button.
 * @param props.skipFocus Skips focusing on panel and announcing panel title.
 *     Used when panel has its own specific focus implementation.
 * @param props.closeDisabled For panels that are always open, e.g. overlays,
 *     assistive tools has assess to the close button. This should hide it
 *     from such tools.
 * @param props.deferredFocusMs Defer focusing after a certain time.
 * @param props.onClose A callback to resolve close button press.
 */
const PanelHeader = forwardRef((props: Props, forwardRef: ForwardRef<HTMLElement>): RNode => {
  const {onClose, className, title, subTitle, customControls, icon, closeDisabled, deferredFocusMs, panel, skipFocus, ...rest} = props;
  const intl = useTranslation();
  const titleRef = useRef<HTMLDivElement>(null);
  const panelMeta = usePanel(panel);
  const {clearOpenTrigger, changeOpen} = usePanelUpdaters(panel);

  const classes = ['vm-panel-header', className].filter(Boolean).join(' ');
  const closeLabel = intl.translate({defaultMessage: 'Close {panel}'}, {panel: PANEL_LABELS[panel]});

  useEffect(() => {
    if (panelMeta.open && panelMeta.openTriggered) {
      if (deferredFocusMs) {
        setTimeout(() => {
          focusElement(skipFocus)(titleRef.current);
        }, deferredFocusMs);
      } else {
        focusElement(skipFocus)(titleRef.current);
      }
      clearOpenTrigger();
    }
  }, [panelMeta, panel, clearOpenTrigger, skipFocus, deferredFocusMs]);

  return <header
    ref={forwardRef}
    className={classes}
    {...rest}>
    {(title || icon) && <div className="vm-t">
      {icon && <div className="vm-i-container">{icon}</div>}
      {title && <div className="vm-l">
        <ScrollingText ref={titleRef} className="title" tabIndex={-1}>
          {title}
        </ScrollingText>
        {subTitle && subTitle}
      </div>}
    </div>}
    {!title && <div ref={titleRef} tabIndex={-1} />}
    <div className="vm-panel-header-controls">
      <Button
        className="vm-close-button vm-toggle-button"
        tooltipMessage={closeLabel}
        onClick={() => {
          onClose?.();
          changeOpen(false);
        }}
        disabled={closeDisabled}
        aria-label={closeLabel}
        aria-pressed="false"
      >
        <SVGCross />
      </Button>
      {customControls && customControls}
    </div>
  </header>;
});

export default PanelHeader;
