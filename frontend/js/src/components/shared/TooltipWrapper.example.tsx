/** @jsx h */
import {h} from 'preact';

import TooltipWrapper from './TooltipWrapper';

import type {RNode} from '../../types';

/**
 * Example usage of the TooltipWrapper component demonstrating various features.
 * This file shows how to use the updated TooltipWrapper with the new design specifications.
 */
export default function TooltipWrapperExample(): RNode {
  return (
    <div style={{padding: '2rem', display: 'flex', flexDirection: 'column', gap: '2rem'}}>
      <h2>TooltipWrapper Examples</h2>

      {/* Basic usage with auto positioning */}
      <div>
        <h3>Auto Positioning (Default)</h3>
        <TooltipWrapper tooltipMessage="This tooltip automatically positions itself based on screen location">
          <button style={{padding: '0.5rem 1rem'}}>Hover me for auto-positioned tooltip</button>
        </TooltipWrapper>
      </div>

      {/* Manual positioning */}
      <div>
        <h3>Manual Positioning</h3>
        <div style={{display: 'flex', gap: '1rem', flexWrap: 'wrap'}}>
          <TooltipWrapper tooltipMessage="Tooltip positioned on top" position="top">
            <button style={{padding: '0.5rem 1rem'}}>Top</button>
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="Tooltip positioned on bottom" position="bottom">
            <button style={{padding: '0.5rem 1rem'}}>Bottom</button>
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="Tooltip positioned on left" position="left">
            <button style={{padding: '0.5rem 1rem'}}>Left</button>
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="Tooltip positioned on right" position="right">
            <button style={{padding: '0.5rem 1rem'}}>Right</button>
          </TooltipWrapper>
        </div>
      </div>

      {/* Disabled elements */}
      <div>
        <h3>Disabled Elements (No Tooltip)</h3>
        <div style={{display: 'flex', gap: '1rem', flexWrap: 'wrap'}}>
          <TooltipWrapper tooltipMessage="This tooltip won't show because button is disabled">
            <button disabled style={{padding: '0.5rem 1rem'}}>Disabled Button</button>
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="This tooltip won't show because of aria-disabled">
            <button aria-disabled="true" style={{padding: '0.5rem 1rem'}}>Aria Disabled</button>
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="This tooltip won't show" disabled={true}>
            <button style={{padding: '0.5rem 1rem'}}>Wrapper Disabled</button>
          </TooltipWrapper>
        </div>
      </div>

      {/* Focus accessibility */}
      <div>
        <h3>Accessibility (Focus Support)</h3>
        <TooltipWrapper tooltipMessage="This tooltip appears on both hover and focus for accessibility">
          <button style={{padding: '0.5rem 1rem'}}>Tab to focus me</button>
        </TooltipWrapper>
      </div>

      {/* Different content types */}
      <div>
        <h3>Different Content Types</h3>
        <div style={{display: 'flex', gap: '1rem', flexWrap: 'wrap'}}>
          <TooltipWrapper tooltipMessage="Tooltip for a link">
            <a href="#" style={{padding: '0.5rem', textDecoration: 'underline'}}>Link with tooltip</a>
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="Tooltip for an input">
            <input type="text" placeholder="Input with tooltip" style={{padding: '0.5rem'}} />
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="Tooltip for a div">
            <div style={{
              padding: '1rem',
              border: '1px solid #ccc',
              borderRadius: '4px',
              cursor: 'pointer',
            }}>
              Div with tooltip
            </div>
          </TooltipWrapper>
        </div>
      </div>

      {/* Tutorial-style messages */}
      <div>
        <h3>Tutorial-Style Messages</h3>
        <div style={{display: 'flex', gap: '1rem', flexWrap: 'wrap'}}>
          <TooltipWrapper tooltipMessage="Main menu">
            <button style={{padding: '0.5rem 1rem'}}>☰ Menu</button>
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="Search items">
            <button style={{padding: '0.5rem 1rem'}}>🔍 Search</button>
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="Filter options">
            <button style={{padding: '0.5rem 1rem'}}>🔧 Filters</button>
          </TooltipWrapper>
        </div>
      </div>

      {/* Edge detection demonstration */}
      <div>
        <h3>Edge Detection & Stretching</h3>
        <p>Try these tooltips near the viewport edges to see automatic stretching:</p>
        <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start'}}>
          <TooltipWrapper tooltipMessage="This tooltip will stretch to fit when near the left edge of the viewport and has a very long message">
            <button style={{padding: '0.5rem 1rem'}}>Left Edge Test</button>
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="This tooltip will stretch to fit when near the right edge of the viewport and has a very long message">
            <button style={{padding: '0.5rem 1rem'}}>Right Edge Test</button>
          </TooltipWrapper>
        </div>
      </div>

      <div style={{marginTop: '2rem', padding: '1rem', backgroundColor: '#f5f5f5', borderRadius: '4px'}}>
        <h4>Design Features:</h4>
        <ul>
          <li>✅ Rounded corners: 6px (0.375rem)</li>
          <li>✅ No borders or box shadows</li>
          <li>✅ Padding: --panel-inner-half-spacing (0.5rem)</li>
          <li>✅ Font: white, 14px, 700 weight</li>
          <li>✅ Background: --color-text-rgb</li>
          <li>✅ Smart positioning based on screen location</li>
          <li>✅ Smooth fade-in animation (0.3s ease)</li>
          <li>✅ Hidden on mobile devices</li>
          <li>✅ Hidden for disabled elements</li>
          <li>✅ Accessible with focus support</li>
          <li>✅ Pointing arrow centered on element</li>
          <li>✅ <strong>NEW:</strong> Edge detection and tooltip stretching</li>
          <li>✅ <strong>NEW:</strong> Consolidated event handlers (reduced code duplication)</li>
          <li>✅ <strong>NEW:</strong> Arrow positioning adapts when tooltip stretches</li>
        </ul>

        <h4>Code Improvements:</h4>
        <ul>
          <li>🔧 Refactored 4 event handlers into 2 reusable functions</li>
          <li>🔧 Implemented viewport edge detection for top/bottom positioned tooltips</li>
          <li>🔧 Added automatic tooltip width adjustment and text wrapping</li>
          <li>🔧 Enhanced arrow positioning to point to element center even when tooltip stretches</li>
          <li>🔧 Added comprehensive tests for new functionality</li>
        </ul>
      </div>
    </div>
  );
}
