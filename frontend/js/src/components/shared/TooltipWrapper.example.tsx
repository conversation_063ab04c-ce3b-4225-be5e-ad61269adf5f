/** @jsx h */
import {h} from 'preact';

import TooltipWrapper from './TooltipWrapper';

import type {RNode} from '../../types';

/**
 * Example usage of the TooltipWrapper component demonstrating various features.
 * This file shows how to use the updated TooltipWrapper with the new design specifications.
 */
export default function TooltipWrapperExample(): RNode {
  return (
    <div style={{padding: '2rem', display: 'flex', flexDirection: 'column', gap: '2rem'}}>
      <h2>TooltipWrapper Examples</h2>

      {/* Basic usage with auto positioning */}
      <div>
        <h3>Auto Positioning (Default)</h3>
        <TooltipWrapper tooltipMessage="This tooltip automatically positions itself based on screen location">
          <button style={{padding: '0.5rem 1rem'}}>Hover me for auto-positioned tooltip</button>
        </TooltipWrapper>
      </div>

      {/* Manual positioning */}
      <div>
        <h3>Manual Positioning</h3>
        <div style={{display: 'flex', gap: '1rem', flexWrap: 'wrap'}}>
          <TooltipWrapper tooltipMessage="Tooltip positioned on top" position="top">
            <button style={{padding: '0.5rem 1rem'}}>Top</button>
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="Tooltip positioned on bottom" position="bottom">
            <button style={{padding: '0.5rem 1rem'}}>Bottom</button>
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="Tooltip positioned on left" position="left">
            <button style={{padding: '0.5rem 1rem'}}>Left</button>
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="Tooltip positioned on right" position="right">
            <button style={{padding: '0.5rem 1rem'}}>Right</button>
          </TooltipWrapper>
        </div>
      </div>

      {/* Disabled elements */}
      <div>
        <h3>Disabled Elements (No Tooltip)</h3>
        <div style={{display: 'flex', gap: '1rem', flexWrap: 'wrap'}}>
          <TooltipWrapper tooltipMessage="This tooltip won't show because button is disabled">
            <button disabled style={{padding: '0.5rem 1rem'}}>Disabled Button</button>
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="This tooltip won't show because of aria-disabled">
            <button aria-disabled="true" style={{padding: '0.5rem 1rem'}}>Aria Disabled</button>
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="This tooltip won't show" disabled={true}>
            <button style={{padding: '0.5rem 1rem'}}>Wrapper Disabled</button>
          </TooltipWrapper>
        </div>
      </div>

      {/* Focus accessibility */}
      <div>
        <h3>Accessibility (Focus Support)</h3>
        <TooltipWrapper tooltipMessage="This tooltip appears on both hover and focus for accessibility">
          <button style={{padding: '0.5rem 1rem'}}>Tab to focus me</button>
        </TooltipWrapper>
      </div>

      {/* Different content types */}
      <div>
        <h3>Different Content Types</h3>
        <div style={{display: 'flex', gap: '1rem', flexWrap: 'wrap'}}>
          <TooltipWrapper tooltipMessage="Tooltip for a link">
            <a href="#" style={{padding: '0.5rem', textDecoration: 'underline'}}>Link with tooltip</a>
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="Tooltip for an input">
            <input type="text" placeholder="Input with tooltip" style={{padding: '0.5rem'}} />
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="Tooltip for a div">
            <div style={{
              padding: '1rem',
              border: '1px solid #ccc',
              borderRadius: '4px',
              cursor: 'pointer',
            }}>
              Div with tooltip
            </div>
          </TooltipWrapper>
        </div>
      </div>

      {/* Tutorial-style messages */}
      <div>
        <h3>Tutorial-Style Messages</h3>
        <div style={{display: 'flex', gap: '1rem', flexWrap: 'wrap'}}>
          <TooltipWrapper tooltipMessage="Main menu">
            <button style={{padding: '0.5rem 1rem'}}>☰ Menu</button>
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="Search items">
            <button style={{padding: '0.5rem 1rem'}}>🔍 Search</button>
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="Filter options">
            <button style={{padding: '0.5rem 1rem'}}>🔧 Filters</button>
          </TooltipWrapper>
        </div>
      </div>

      {/* Fixed positioning demonstration */}
      <div>
        <h3>Fixed Positioning - No More Clipping!</h3>
        <p>Tooltips now use position: fixed and are never clipped by parent containers:</p>

        {/* Container with overflow: hidden to demonstrate the fix */}
        <div style={{
          border: '2px solid #ccc',
          borderRadius: '4px',
          padding: '1rem',
          overflow: 'hidden',
          height: '100px',
          position: 'relative',
          backgroundColor: '#f9f9f9',
        }}>
          <p style={{margin: '0 0 0.5rem 0', fontSize: '0.875rem', color: '#666'}}>
            This container has <code>overflow: hidden</code> and limited height
          </p>

          <TooltipWrapper tooltipMessage="This tooltip uses position: fixed and will NOT be clipped by the parent container's overflow: hidden!" position="top">
            <button style={{padding: '0.5rem 1rem'}}>Hover me - No clipping!</button>
          </TooltipWrapper>
        </div>
      </div>

      {/* Edge detection demonstration */}
      <div>
        <h3>Edge Detection & Stretching</h3>
        <p>Compare normal centered tooltips vs edge-adapted tooltips:</p>

        {/* Normal centered tooltips */}
        <div style={{display: 'flex', justifyContent: 'center', gap: '2rem', marginBottom: '1rem'}}>
          <TooltipWrapper tooltipMessage="Normal centered tooltip" position="bottom">
            <button style={{padding: '0.5rem 1rem'}}>Normal Tooltip</button>
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="Another normal tooltip with slightly longer text" position="bottom">
            <button style={{padding: '0.5rem 1rem'}}>Normal Long</button>
          </TooltipWrapper>
        </div>

        {/* Edge case tooltips */}
        <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start'}}>
          <TooltipWrapper tooltipMessage="This tooltip will stretch to fit when near the left edge of the viewport and has a very long message that would normally overflow" position="bottom">
            <button style={{padding: '0.5rem 1rem'}}>Left Edge Test</button>
          </TooltipWrapper>

          <TooltipWrapper tooltipMessage="This tooltip will stretch to fit when near the right edge of the viewport and has a very long message that would normally overflow" position="bottom">
            <button style={{padding: '0.5rem 1rem'}}>Right Edge Test</button>
          </TooltipWrapper>
        </div>

        <p style={{fontSize: '0.875rem', color: '#666', marginTop: '0.5rem'}}>
          <strong>Notice:</strong> Normal tooltips have centered arrows, while edge-case tooltips have arrows that point to the button center even when the tooltip stretches.
        </p>
      </div>

      <div style={{marginTop: '2rem', padding: '1rem', backgroundColor: '#f5f5f5', borderRadius: '4px'}}>
        <h4>Design Features:</h4>
        <ul>
          <li>✅ Rounded corners: 6px (0.375rem)</li>
          <li>✅ No borders or box shadows</li>
          <li>✅ Padding: --panel-inner-half-spacing (0.5rem)</li>
          <li>✅ Font: white, 14px, 700 weight</li>
          <li>✅ Background: --color-text-rgb</li>
          <li>✅ Smart positioning based on screen location</li>
          <li>✅ Smooth fade-in animation (0.3s ease)</li>
          <li>✅ Hidden on mobile devices</li>
          <li>✅ Hidden for disabled elements</li>
          <li>✅ Accessible with focus support</li>
          <li>✅ Pointing arrow centered on element</li>
          <li>✅ Edge detection and tooltip stretching</li>
          <li>✅ Consolidated event handlers (reduced code duplication)</li>
          <li>✅ Arrow positioning adapts when tooltip stretches</li>
          <li>✅ <strong>NEW:</strong> Fixed positioning (no clipping by parent containers)</li>
          <li>✅ <strong>NEW:</strong> Viewport-based positioning with scroll/resize handling</li>
          <li>✅ <strong>NEW:</strong> Higher z-index (10000) for better layering</li>
        </ul>

        <h4>Code Improvements:</h4>
        <ul>
          <li>🔧 Refactored 4 event handlers into 2 reusable functions</li>
          <li>🔧 Implemented viewport edge detection for top/bottom positioned tooltips</li>
          <li>🔧 Added automatic tooltip width adjustment and text wrapping</li>
          <li>🔧 Enhanced arrow positioning to point to element center even when tooltip stretches</li>
          <li>🔧 <strong>NEW:</strong> Migrated from position: absolute to position: fixed</li>
          <li>🔧 <strong>NEW:</strong> Added scroll and resize event listeners for dynamic positioning</li>
          <li>🔧 <strong>NEW:</strong> Viewport-based coordinate calculations using getBoundingClientRect()</li>
          <li>🔧 <strong>NEW:</strong> Eliminated clipping issues with parent overflow: hidden containers</li>
          <li>🔧 Added comprehensive tests for new functionality</li>
        </ul>

        <h4>🚀 Fixed Positioning Benefits:</h4>
        <ul>
          <li><strong>No Clipping:</strong> Tooltips are never cut off by parent containers with overflow: hidden</li>
          <li><strong>Consistent Layering:</strong> Always appears above other content with high z-index</li>
          <li><strong>Viewport Relative:</strong> Positioned relative to the browser window, not parent elements</li>
          <li><strong>Scroll Independent:</strong> Maintains correct position during page scrolling</li>
          <li><strong>Responsive:</strong> Automatically adjusts position on window resize</li>
          <li><strong>Better UX:</strong> Users can always see tooltips regardless of container constraints</li>
        </ul>
      </div>
    </div>
  );
}
