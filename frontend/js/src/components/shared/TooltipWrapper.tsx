/** @jsx h */
import {h} from 'preact';
import {useCallback, useLayoutEffect, useRef, useState} from 'preact/hooks';
import {isMobileDevice} from '../../functions/device';
import type {ComponentChildren, RNode} from '../../types';

export type Props = Readonly<{
  tooltipMessage: string;
  children: ComponentChildren;
  position?: 'auto' | 'top' | 'bottom' | 'left' | 'right'; // Tooltip position options
  disabled?: boolean; // Whether the wrapped element is disabled
}>;

/**
 * TooltipWrapper component that displays tooltips on hover and focus.
 * Automatically positions tooltips based on screen location and available space.
 * Hides tooltips on mobile devices and for disabled elements.
 *
 * @param tooltipMessage - The text to display in the tooltip
 * @param children - The element(s) to wrap with tooltip functionality
 * @param position - Positioning preference ('auto' for smart positioning)
 * @param disabled - Whether the wrapped element is disabled (hides tooltip)
 */
const TooltipWrapper = ({tooltipMessage, children, position = 'auto', disabled = false}: Props): RNode => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [calculatedPosition, setCalculatedPosition] = useState<'top' | 'bottom' | 'left' | 'right'>('top');
  const [tooltipStyle, setTooltipStyle] = useState<{[key: string]: string | number}>({});
  const wrapperRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const isMobile = isMobileDevice();

  /**
   * Calculates the optimal tooltip position based on available screen space
   * and the position of the target element.
   */
  const calculatePosition = useCallback((): 'top' | 'bottom' | 'left' | 'right' => {
    if (!wrapperRef.current || position !== 'auto') {
      return position === 'auto' ? 'top' : position;
    }

    const rect = wrapperRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Approximate tooltip dimensions
    const tooltipWidth = 200; // Will be adjusted based on content
    const tooltipHeight = 60;
    const margin = 16;

    // Calculate available space in each direction
    const spaceTop = rect.top;
    const spaceBottom = viewportHeight - rect.bottom;
    const spaceLeft = rect.left;

    // Determine position based on screen location and available space
    // Top-left quadrant: prefer bottom
    if (rect.left < viewportWidth / 2 && rect.top < viewportHeight / 2) {
      return spaceBottom >= tooltipHeight + margin ? 'bottom' : 'top';
    }

    // Top-right quadrant: prefer bottom
    if (rect.left >= viewportWidth / 2 && rect.top < viewportHeight / 2) {
      return spaceBottom >= tooltipHeight + margin ? 'bottom' : 'top';
    }

    // Bottom-left quadrant: prefer top
    if (rect.left < viewportWidth / 2 && rect.top >= viewportHeight / 2) {
      return spaceTop >= tooltipHeight + margin ? 'top' : 'bottom';
    }

    // Bottom-right quadrant: prefer left
    if (rect.left >= viewportWidth / 2 && rect.top >= viewportHeight / 2) {
      return spaceLeft >= tooltipWidth + margin ? 'left' : 'right';
    }

    return 'top'; // fallback
  }, [position]);

  /**
   * Calculates tooltip positioning styles based on the calculated position
   * and handles edge cases where tooltip needs to stretch to canvas edge.
   */
  const calculateTooltipStyle = useCallback((): {[key: string]: string | number} => {
    if (!wrapperRef.current) {
      return {};
    }

    const wrapperRect = wrapperRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const margin = 8; // Minimum margin from viewport edge

    const baseStyle: {[key: string]: string | number} = {
      position: 'fixed', // Changed from 'absolute' to 'fixed'
      backgroundColor: 'rgba(var(--color-text-rgb), 1)',
      color: 'white',
      padding: 'var(--panel-inner-half-spacing)',
      borderRadius: '0.375rem',
      fontSize: '14px',
      fontWeight: '700',
      zIndex: 10000, // Increased z-index for fixed positioning
      opacity: showTooltip ? 1 : 0,
      transition: 'opacity 0.3s ease',
      pointerEvents: 'none',
      boxShadow: 'none',
      border: 'none',
      whiteSpace: 'nowrap',
    };

    const pos = calculatedPosition;
    const arrowOffset = 8; // Space between element and tooltip

    // For top and bottom positions, implement edge detection and stretching
    if (pos === 'top' || pos === 'bottom') {
      // Estimate tooltip width based on content length (rough approximation)
      const estimatedWidth = Math.min(Math.max(tooltipMessage.length * 8 + 32, 120), 300);
      const halfTooltipWidth = estimatedWidth / 2;

      // Calculate ideal center position (viewport coordinates)
      const elementCenterX = wrapperRect.left + wrapperRect.width / 2;

      // Check if tooltip would overflow viewport
      const wouldOverflowLeft = elementCenterX - halfTooltipWidth < margin;
      const wouldOverflowRight = elementCenterX + halfTooltipWidth > viewportWidth - margin;

      let tooltipLeft: number;
      let tooltipWidth: number | string = 'auto';
      let whiteSpace: string = 'nowrap';

      if (wouldOverflowLeft || wouldOverflowRight) {
        // Tooltip needs to stretch to fit viewport
        whiteSpace = 'normal'; // Allow text wrapping when constrained

        if (wouldOverflowLeft) {
          // Anchor to left edge
          tooltipLeft = margin;
          tooltipWidth = Math.min(estimatedWidth, elementCenterX + halfTooltipWidth - margin);
        } else {
          // Anchor to right edge
          tooltipLeft = Math.max(margin, elementCenterX - halfTooltipWidth);
          tooltipWidth = viewportWidth - tooltipLeft - margin;
        }
      } else {
        // Normal centered positioning
        tooltipLeft = elementCenterX - halfTooltipWidth;
      }

      // Calculate vertical position based on element position
      let tooltipTop: number;
      if (pos === 'top') {
        tooltipTop = wrapperRect.top - arrowOffset;
        // Ensure tooltip doesn't go above viewport
        if (tooltipTop < margin) {
          tooltipTop = wrapperRect.bottom + arrowOffset;
        }
      } else {
        tooltipTop = wrapperRect.bottom + arrowOffset;
        // Ensure tooltip doesn't go below viewport
        if (tooltipTop + 60 > viewportHeight - margin) { // 60 is estimated tooltip height
          tooltipTop = wrapperRect.top - arrowOffset - 60;
        }
      }

      return {
        ...baseStyle,
        left: tooltipLeft,
        top: pos === 'bottom' ? tooltipTop : 'auto',
        bottom: pos === 'top' ? viewportHeight - (wrapperRect.top - arrowOffset) : 'auto',
        width: tooltipWidth,
        whiteSpace,
        maxWidth: viewportWidth - 2 * margin,
      };
    }

    // For left and right positions, use viewport-based positioning
    switch (pos) {
      case 'left': {
        const tooltipRight = viewportWidth - wrapperRect.left + arrowOffset;
        const tooltipTop = wrapperRect.top + wrapperRect.height / 2;

        return {
          ...baseStyle,
          right: tooltipRight,
          top: tooltipTop,
          transform: 'translateY(-50%)',
          whiteSpace: 'nowrap',
          maxWidth: wrapperRect.left - arrowOffset - margin,
        };
      }
      case 'right': {
        const tooltipLeft = wrapperRect.right + arrowOffset;
        const tooltipTop = wrapperRect.top + wrapperRect.height / 2;

        return {
          ...baseStyle,
          left: tooltipLeft,
          top: tooltipTop,
          transform: 'translateY(-50%)',
          whiteSpace: 'nowrap',
          maxWidth: viewportWidth - tooltipLeft - margin,
        };
      }
      default:
        return baseStyle;
    }
  }, [calculatedPosition, showTooltip, tooltipMessage]);

  /**
   * Calculates arrow positioning styles based on tooltip position.
   * Arrow always points to the center of the target element, even when tooltip is stretched.
   * Updated for fixed positioning - arrow position is calculated relative to tooltip.
   */
  const getArrowStyle = useCallback(() => {
    if (!wrapperRef.current || !tooltipRef.current) {
      return {};
    }

    const arrowSize = 6;
    const baseArrowStyle: {[key: string]: string | number} = {
      position: 'absolute',
      width: 0,
      height: 0,
    };

    const arrowColor = 'rgba(var(--color-text-rgb), 1)';
    const wrapperRect = wrapperRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();

    switch (calculatedPosition) {
      case 'top':
      case 'bottom': {
        // Calculate where the arrow should be positioned relative to the tooltip
        const elementCenterX = wrapperRect.left + wrapperRect.width / 2;
        const tooltipLeftX = tooltipRect.left;
        const arrowLeftOffset = elementCenterX - tooltipLeftX;

        // Ensure arrow stays within tooltip bounds with some padding
        const minOffset = arrowSize + 4;
        const maxOffset = tooltipRect.width - arrowSize - 4;
        const clampedOffset = Math.max(minOffset, Math.min(maxOffset, arrowLeftOffset));

        const verticalStyle = calculatedPosition === 'top' ? {
          bottom: '100%',
          borderLeft: `${arrowSize}px solid transparent`,
          borderRight: `${arrowSize}px solid transparent`,
          borderTop: `${arrowSize}px solid ${arrowColor}`,
        } : {
          top: '100%',
          borderLeft: `${arrowSize}px solid transparent`,
          borderRight: `${arrowSize}px solid transparent`,
          borderBottom: `${arrowSize}px solid ${arrowColor}`,
        };

        return {
          ...baseArrowStyle,
          ...verticalStyle,
          left: `${clampedOffset}px`,
        };
      }
      case 'left':
        return {
          ...baseArrowStyle,
          borderTop: `${arrowSize}px solid transparent`,
          borderBottom: `${arrowSize}px solid transparent`,
          borderLeft: `${arrowSize}px solid ${arrowColor}`,
          left: '100%',
          top: '50%',
          transform: 'translateY(-50%)',
        };
      case 'right':
        return {
          ...baseArrowStyle,
          borderTop: `${arrowSize}px solid transparent`,
          borderBottom: `${arrowSize}px solid transparent`,
          borderRight: `${arrowSize}px solid ${arrowColor}`,
          right: '100%',
          top: '50%',
          transform: 'translateY(-50%)',
        };
      default:
        return baseArrowStyle;
    }
  }, [calculatedPosition]);

  // Update calculated position when wrapper mounts or position prop changes
  useLayoutEffect(() => {
    const newPosition = calculatePosition();
    setCalculatedPosition(newPosition);
  }, [calculatePosition]);

  // Update tooltip styles when position or visibility changes
  useLayoutEffect(() => {
    const newStyle = calculateTooltipStyle();
    setTooltipStyle(newStyle);
  }, [calculateTooltipStyle]);

  // Handle scroll and resize events to update tooltip position for fixed positioning
  useLayoutEffect(() => {
    if (!showTooltip) {
      return undefined;
    }

    const updateTooltipPosition = () => {
      const newPosition = calculatePosition();
      setCalculatedPosition(newPosition);
      const newStyle = calculateTooltipStyle();
      setTooltipStyle(newStyle);
    };

    // Update position on scroll and resize
    window.addEventListener('scroll', updateTooltipPosition, {passive: true});
    window.addEventListener('resize', updateTooltipPosition, {passive: true});

    return () => {
      window.removeEventListener('scroll', updateTooltipPosition);
      window.removeEventListener('resize', updateTooltipPosition);
    };
  }, [showTooltip, calculatePosition, calculateTooltipStyle]);

  // Consolidated event handlers to reduce code duplication
  const handleShowTooltip = useCallback(() => {
    if (!isMobile && !disabled && tooltipMessage) {
      setShowTooltip(true);
    }
  }, [isMobile, disabled, tooltipMessage]);

  const handleHideTooltip = useCallback(() => {
    setShowTooltip(false);
  }, []);

  // Check if child element is disabled
  const isChildDisabled = useCallback(() => {
    if (!wrapperRef.current) {
      return false;
    }

    const childElement = wrapperRef.current.firstElementChild as HTMLElement;
    if (!childElement) {
      return false;
    }

    // Check various ways an element can be disabled
    return (
      childElement.hasAttribute('disabled')
      || childElement.getAttribute('aria-disabled') === 'true'
      || childElement.classList.contains('disabled')
      || disabled
    );
  }, [disabled]);



  const wrapperStyle: {[key: string]: string | number} = {
    position: 'relative',
    display: 'inline-block',
    borderRadius: 'inherit',
  };

  return (
    <div
      ref={wrapperRef}
      style={wrapperStyle}
      onMouseEnter={handleShowTooltip}
      onMouseLeave={handleHideTooltip}
      onFocus={handleShowTooltip}
      onBlur={handleHideTooltip}
    >
      {children}
      {tooltipMessage && !isMobile && !isChildDisabled() && (
        <div
          ref={tooltipRef}
          style={tooltipStyle}
          role="tooltip"
          aria-hidden={!showTooltip}
        >
          {tooltipMessage}
          <div style={getArrowStyle()}></div>
        </div>
      )}
    </div>
  );
};

export default TooltipWrapper;
