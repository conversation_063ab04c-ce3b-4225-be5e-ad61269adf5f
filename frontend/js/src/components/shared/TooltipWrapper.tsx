/** @jsx h */
import {h} from 'preact';
import {useState} from 'preact/hooks';
import type {ComponentChildren, RNode} from '../../types';

export type Props = Readonly<{
  tooltipMessage: string;
  children: ComponentChildren;
  position?: 'top' | 'bottom' | 'left' | 'right'; // Tooltip position options
}>;

const TooltipWrapper = ({tooltipMessage, children, position = 'top'}: Props): RNode => {
  const [showTooltip, setShowTooltip] = useState(false);

  // Calculate tooltip position
  const getTooltipStyle = () => {
    const baseStyle = {
      position: 'absolute',
      backgroundColor: 'var(--color-toggle)', // Green
      color: '#fff',
      boxShadow: '0 4px 6px var(--color-toggle)',
      padding: '8px',
      borderRadius: '4px',
      fontSize: '14px',
      whiteSpace: 'nowrap',
      zIndex: 10,
      opacity: showTooltip ? 1 : 0,
      transition: 'opacity 0.5s ease',
      pointerEvents: 'none',
    };

    switch (position) {
      case 'top':
        return {
          ...baseStyle,
          transform: 'translate(-50%, -120%)',
          left: '50%',
          top: 0,
        };
      case 'bottom':
        return {
          ...baseStyle,
          transform: 'translate(-50%, 20%)',
          left: '50%',
          top: '100%',
        };
      case 'left':
        return {
          ...baseStyle,
          transform: 'translate(-120%, -50%)',
          left: 0,
          top: '50%',
        };
      case 'right':
        return {
          ...baseStyle,
          transform: 'translate(20%, -50%)',
          left: '100%',
          top: '50%',
        };
      default:
        return baseStyle;
    }
  };

  // Calculate arrow position
  const getArrowStyle = () => {
    const baseArrowStyle = {
      content: '""',
      position: 'absolute',
      width: 0,
      height: 0,
    };

    switch (position) {
      case 'top':
        return {
          ...baseArrowStyle,
          borderLeft: '7px solid transparent',
          borderRight: '7px solid transparent',
          borderTop: '7px solid var(--color-toggle)',
          bottom: '-5px',
          left: '50%',
          transform: 'translateX(-50%)',
        };
      case 'bottom':
        return {
          ...baseArrowStyle,
          borderLeft: '7px solid transparent',
          borderRight: '7px solid transparent',
          borderBottom: '7px solid var(--color-toggle)',
          top: '-5px',
          left: '50%',
          transform: 'translateX(-50%)',
        };
      case 'left':
        return {
          ...baseArrowStyle,
          borderTop: '7px solid transparent',
          borderBottom: '7px solid transparent',
          borderLeft: '7px solid var(--color-toggle)',
          right: '-5px',
          top: '50%',
          transform: 'translateY(-50%)',
        };
      case 'right':
        return {
          ...baseArrowStyle,
          borderTop: '7px solid transparent',
          borderBottom: '7px solid transparent',
          borderRight: '7px solid var(--color-toggle)',
          left: '-5px',
          top: '50%',
          transform: 'translateY(-50%)',
        };
      default:
        return baseArrowStyle;
    }
  };

  const wrapperStyle = {
    position: 'relative',
    display: 'inline-block',
    borderRadius: 'inherit',
  };

  return (
    <div
      style={wrapperStyle}
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      {children}
      {tooltipMessage && (
        <div style={getTooltipStyle()}>
          {tooltipMessage}
          <div style={getArrowStyle()}></div>
        </div>
      )}
    </div>
  );
};

export default TooltipWrapper;
