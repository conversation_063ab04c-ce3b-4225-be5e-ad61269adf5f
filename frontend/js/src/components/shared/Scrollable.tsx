// Container with controls for scrolling
/** @jsx h */

import {h} from 'preact';
import {forwardRef} from 'preact/compat';
import {useCallback, useEffect, useRef, useState} from 'preact/hooks';

import {debounce} from '../../functions/debounce';
import {isMobileDevice} from '../../functions/device';
import {classes} from '../../functions/html';
import SVGArrowWide from '../../svg/SVGArrowWide';
import Button from '../Button';

import type {Attrs, ForwardRef, RefObject} from '../../types';

type Direction = 1 | -1;

type Props = Attrs['div'] & {
  className?: string;
};

/**
 * Custom hook to action on resize of an element with debounce.
 *
 * @param {RefObject<HTMLElement>} ref - The ref to the element.
 * @param {(e?: HTMLElement) => void} fn - The callback function to be executed when resized.
 * @param {number} [delayMs] - The debounce delay in milliseconds (default is 50).
 */
function useDebouncedResizeObserve(ref: RefObject<HTMLElement>, fn: (e?: HTMLElement) => void, delayMs: number = 50): void {
  useEffect(() => {
    if (!window.ResizeObserver) {
      return undefined;
    }

    const {current} = ref;

    // debounce for saner updates
    const {debouncedFunction, clearDebounce} = debounce(() => {
      if (current) {
        fn(current);
      }
    }, delayMs);

    const observer = new ResizeObserver(() => {
      debouncedFunction();
    });

    if (current) {
      observer.observe(current);
    }

    return () => {
      clearDebounce();
      observer.disconnect();
    };
  }, [ref, fn, delayMs]);
}

/**
 * Custom hook to action on scroll events on an element with debounce.
 *
 * @param {RefObject<HTMLElement>} ref - The ref to the scrollable element.
 * @param {(e?: HTMLElement) => void} fn - The callback function to be executed when scrolling.
 * @param {number} [delayMs] - The debounce delay in milliseconds (default is 50).
 */
function useDebouncedScrollObserve(ref: RefObject<HTMLElement>, fn: (e?: HTMLElement) => void, delayMs: number = 50): void {
  useEffect(() => {
    const currentScroll = ref.current;
    if (!currentScroll) {
      return () => {};
    }

    const {debouncedFunction, clearDebounce} = debounce<never, void>(fn, delayMs);
    const memoed = () => {
      debouncedFunction();
    };

    currentScroll.addEventListener('scroll', memoed);

    fn(currentScroll);

    return () => {
      if (currentScroll) {
        clearDebounce();
        currentScroll.removeEventListener('scroll', memoed);
      }
    };
  }, [ref, fn, delayMs]);
}

/**
 * A horizontally scrollable container with left and right scroll buttons on desktop.
 *
 * @component
 * @param {ComponentChildren} props.children - The content to be rendered inside the scrollable container.
 */
const Scrollable = forwardRef((props: Props, ref: ForwardRef<HTMLDivElement>) => {
  const {children, className, ...restProps} = props;
  const scrollRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);
  const isMobile = isMobileDevice();
  const hasButtons = !isMobile;

  const updateScrollButtons = useCallback(() => {
    if (!scrollRef.current) {
      return;
    }
    const {scrollLeft, scrollWidth, clientWidth} = scrollRef.current;
    setCanScrollLeft(scrollLeft > 0);
    // scroll sometimes does not go all the way within margin of 1 px(?)
    setCanScrollRight(scrollLeft + clientWidth + 1 < scrollWidth);
  }, []);

  // Considering framerate less than 30/s does not exist.
  useDebouncedResizeObserve(scrollRef, updateScrollButtons, 40);
  useDebouncedScrollObserve(scrollRef, updateScrollButtons, 40);

  const scroll = (direction: Direction) => {
    if (!scrollRef.current) {
      return;
    }
    const step = .75 * scrollRef.current.clientWidth;
    scrollRef.current.scrollBy({left: direction * step, behavior: 'smooth'});
  };

  return <div ref={ref} {...classes({}, 'vm-scrollable', className)} {...restProps}>
    {hasButtons && canScrollLeft && <Button
      className="vm-control-button left"
      onClick={() => scroll(-1)}
    >
      <SVGArrowWide />
    </Button>}
    <div
      ref={scrollRef}
      {...classes({left: canScrollLeft, right: canScrollRight, mobile: isMobile}, 'vm-scrollable-content')}
    >
      {children}
    </div>
    {hasButtons && canScrollRight && (
      <Button
        className="vm-control-button right flip"
        onClick={() => scroll(1)}
      >
        <SVGArrowWide />
      </Button>
    )}
  </div>;
});

export default Scrollable;
