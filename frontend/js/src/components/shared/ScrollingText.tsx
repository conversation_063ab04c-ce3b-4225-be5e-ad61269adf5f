// Scrolling text wrapper component

/** @jsx h */

import {h} from 'preact';
import {forwardRef} from 'preact/compat';
import {useEffect, useRef} from 'preact/hooks';

import {debounce} from '../../functions/debounce';
import {classes} from '../../functions/html';
import useCopyForwardRef from '../../hooks/useCopyForwardRef';

import type {Attrs, ForwardRef, RNode} from '../../types';


/**
 * An ever increasing but bounded function. Scaled for normalcy.
 *
 * https://gamedev.stackexchange.com/a/89744
 */
function diminish(value: number, factor: number = 0.6, scale: number = 2): number {
  return (Math.pow(value + 1, 1 - factor) - 1) / (1 - factor) / scale;
}

/**
 * Updates data used in CSS for animation.
 *
 * Speed is calculated in accordance to distance.
 * The animation has a pause/delay before and after the movement.
 * The PAUSE_OFFSET is used to account for those but we don't have an
 * easy way to simulate (pause 2s -> move Xs -> pause 2s) with variable
 * movement duration, so for now it they are relative to total duration.
 *
 * @param container Containing the scroll text.
 */
function updateScrollVariables(container: HTMLElement): void {
  const PAUSE_OFFSET = 4; // seconds

  const text = container.querySelector<HTMLElement>('.vm-scrolling-text');
  if (!text) {
    return;
  }

  const containerWidth = container.offsetWidth;
  const textWidth = text.scrollWidth;

  const scrollDistance = containerWidth - textWidth;

  if (scrollDistance < 0) {
    const totalTime = PAUSE_OFFSET + diminish(-scrollDistance);
    text.style.setProperty('--scroll-distance', `${scrollDistance}px`);
    text.style.setProperty('--scroll-total-time', `${totalTime}s`);
  } else {
    text.style.setProperty('--scroll-distance', '0px');
    text.style.setProperty('--scroll-total-time', '0s');
  }
}


type Props = Attrs['div'] & {
  className?: string;
};
/**
 * Wrapper for auto-scrolling text.
 * It should have a brief delay before moving and stays a bit after reaching
 * the end and repeat from the start.
 *
 * @param props.children A text.
 */
const ScrollingText = forwardRef((props: Props, forwardRef: ForwardRef<HTMLDivElement>): RNode => {
  const {children, className, ...restProps} = props;
  const ref = useRef<HTMLDivElement>(null);
  const copyRef = useCopyForwardRef(forwardRef, ref);

  useEffect(() => {
    if (!window.ResizeObserver) {
      return undefined;
    }

    const {current} = ref;

    // debounce for saner updates
    const {debouncedFunction, clearDebounce} = debounce(() => {
      if (current) {
        updateScrollVariables(current);
      };
    });

    const observer = new ResizeObserver(() => {
      debouncedFunction();
    });

    if (current) {
      observer.observe(current);
    }

    return () => {
      clearDebounce();
      observer.disconnect();
    };
  }, [ref]);

  return <div ref={copyRef} {...classes({}, 'vm-scrolling-container', className)} {...restProps}>
    <span className="vm-scrolling-text" >
      {children}
    </span>
  </div>;
});

export default ScrollingText;
