/** @jsx h */
import {fireEvent, render} from '@testing-library/preact';
import {h} from 'preact';

import TooltipWrapper from './TooltipWrapper';
import * as deviceModule from '../../functions/device';

// Mock the device detection function
jest.mock('../../functions/device', () => ({
  isMobileDevice: jest.fn(() => false),
}));

describe('TooltipWrapper', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('renders children correctly', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    expect(container.querySelector('button')).toBeTruthy();
    expect(container.textContent).toContain('Test Button');
  });

  it('shows tooltip on mouse enter', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    expect(container.textContent).toContain('Test tooltip');
  });

  it('hides tooltip on mouse leave', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);
    expect(container.textContent).toContain('Test tooltip');

    fireEvent.mouseLeave(wrapper);
    // Tooltip should still be in DOM but with opacity 0
    const tooltip = container.querySelector('[role="tooltip"]') as HTMLElement;
    expect(tooltip).toBeTruthy();
    expect(tooltip.style.opacity).toBe('0');
  });

  it('shows tooltip on focus', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.focus(wrapper);

    expect(container.textContent).toContain('Test tooltip');
  });

  it('hides tooltip on blur', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.focus(wrapper);
    expect(container.textContent).toContain('Test tooltip');

    fireEvent.blur(wrapper);
    const tooltip = container.querySelector('[role="tooltip"]') as HTMLElement;
    expect(tooltip).toBeTruthy();
    expect(tooltip.style.opacity).toBe('0');
  });

  it('does not show tooltip when disabled', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip" disabled={true}>
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    expect(container.textContent).not.toContain('Test tooltip');
  });

  it('does not show tooltip on mobile devices', () => {
    // Mock mobile device detection
    const mockIsMobile = jest.spyOn(deviceModule, 'isMobileDevice').mockReturnValue(true);

    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    expect(container.textContent).not.toContain('Test tooltip');

    // Reset mock
    mockIsMobile.mockRestore();
  });

  it('does not show tooltip for disabled child elements', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button disabled>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    expect(container.textContent).not.toContain('Test tooltip');
  });

  it('does not show tooltip for aria-disabled child elements', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button aria-disabled="true">Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    expect(container.textContent).not.toContain('Test tooltip');
  });

  it('applies correct ARIA attributes to tooltip', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    // Tooltip should be in DOM initially but hidden
    const tooltip = container.querySelector('[role="tooltip"]');
    expect(tooltip).toBeTruthy();
    expect(tooltip?.getAttribute('role')).toBe('tooltip');
    expect(tooltip?.getAttribute('aria-hidden')).toBe('true');

    // Show tooltip and check aria-hidden changes
    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);
    expect(tooltip?.getAttribute('aria-hidden')).toBe('false');
  });

  it('supports different positioning options', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip" position="bottom">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    // Tooltip should be in DOM
    const tooltip = container.querySelector('[role="tooltip"]');
    expect(tooltip).toBeTruthy();

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    expect(container.textContent).toContain('Test tooltip');
  });

  it('does not show tooltip when message is empty', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    const tooltip = container.querySelector('[role="tooltip"]');
    expect(tooltip).toBeFalsy();
  });
});
