// Showing current map detail and zoomlevel information
/** @jsx h */

import {h} from 'preact';

import {useGeekery} from '../context/geek';
import {useLensDetailStackByZoom} from '../hooks/lenses';
import useCurrentZoomLevel from '../hooks/useCurrentZoomLevel';
import ObjectLink from './ObjectLink';

import type {RNode} from '../types';

// Nicked from last-chance name for iri formatting in rdfmodel
function chopper(iri: string): string {
  return iri.slice(iri.lastIndexOf('#') + 1 || iri.lastIndexOf('/') + 1);
}

function toHundredths(n: number): number {
  return +(n.toFixed(2));
}

function GeekLensDisplay(): RNode {
  const detailsStack = useLensDetailStackByZoom();
  const zoom = useCurrentZoomLevel(toHundredths).toFixed(2);

  return <div className="vm-geek-lens-container">
    <div className="vm-geek-lens vm-float-block">
      <span title="tails of each lens detail currently seen">Lens details stack: {'\u2191'}</span><br />
      <ul className="vm-geek-detail-list">
        {detailsStack.map(detail => <li key={detail} title={detail}>
          <ObjectLink iconFlag="noIcon" value={detail} namer={chopper} />
        </li>)}
      </ul>
    </div>
    <div className="vm-geek-zoom vm-float-block">
      <span title="a number which grows as the view zooms in">{zoom}</span>
    </div>
  </div>;
}

export default function MaybeGeekLensDisplay(): RNode|null {
  const {showLenses} = useGeekery();

  return showLenses ? <GeekLensDisplay /> : null;
}
