// Comments for an item

/** @jsx h */

import {Fragment, h} from 'preact';
import {useCallback} from 'preact/hooks';

import {ANNOTATION_CATEGORY_DEFAULT} from '../constants';
import {useCommenter} from '../context/annotation';
import {usePanelFocus, usePanelFocusUpdater} from '../context/comments';
import {useNavigation} from '../context/place';
import {Translate, useTranslation} from '../intl';
import SVGBin from '../svg/SVGBin';
import SVGBubblePlus from '../svg/SVGBubblePlus';
import SVGPencil from '../svg/SVGPencil';
import Accordion from './Accordion';
import Button from './Button';
import CommentEditor from './CommentEditor';
import LinkForSelect from './LinkForSelect';

import type {CommentCategory} from '../context/annotation';
import type {Renderer} from '../context/mark';
import type {Translator} from '../intl';
import type {RNode, RefObject} from '../types';
import type {CommentsItem, ExtendedComment} from './CommentsPanelFloating';

const SANE_DT_LOCALE = 'sv-SE';

function getLocaleISODateString(date: Date): string {
  return new Date(date).toLocaleString(SANE_DT_LOCALE, {dateStyle: 'short'});
}

type CommentCategoryProps = {
  showCategories: boolean;
  className?: string;
  category?: CommentCategory;
};
function CommentType({className, category, showCategories}: CommentCategoryProps): RNode | null {
  if (!category || !showCategories || category.iri === ANNOTATION_CATEGORY_DEFAULT) {
    return null;
  }

  const tooltip = category.helpText || category.name;

  const extendedClassName = [
    'vm-label-tag',
    className,
  ].filter(Boolean).join(' ');

  return <div className={extendedClassName} title={tooltip}>
    {category.icon && <img className="vm-i" src={category.icon}></img>}
    {category.name}
  </div>;
}

type CommentContentProps = Readonly<{
  intl: Translator;
  kd: Renderer;
  disabled: boolean;
  iri: string;
  comment: ExtendedComment;
  user: string;
  displayUser: boolean;
  onEditModeChange: (value: boolean) => void;
  setComment: ReturnType<typeof useCommenter>;
  showCategories: boolean;
}>;
function CommentContent(props: CommentContentProps): RNode {
  const {intl, kd, disabled, iri, comment, user, displayUser, onEditModeChange, setComment, showCategories} = props;
  const {focus} = usePanelFocus();
  const {changeFocus} = usePanelFocusUpdater();
  const {setSelection} = useNavigation();

  const editMode = focus?.comment === comment.commentIri && focus?.mode === 'edit';
  const deleteMode = focus?.comment === comment.commentIri && focus?.mode === 'delete';

  const changeEditMode = useCallback((value: boolean) => {
    changeFocus('edit', value, iri, comment.commentIri);
  }, [changeFocus, comment.commentIri, iri]);
  const changeDeleteMode = useCallback((value: boolean) => {
    changeFocus('delete', value, iri, comment.commentIri);
  }, [changeFocus, comment.commentIri, iri]);

  const sameUser = user === comment.user;
  const date = getLocaleISODateString(comment.created);

  const setEditModeValue = useCallback((value: boolean) => {
    if (!sameUser) {
      return;
    }
    changeEditMode(value);
    onEditModeChange(value);
  }, [changeEditMode, onEditModeChange, sameUser]);

  const onDeleteComment = useCallback(() => {
    if (!sameUser) {
      return;
    }
    setComment(iri, '', comment.commentIri, undefined, undefined, false);
    changeDeleteMode(false);
  }, [changeDeleteMode, comment.commentIri, iri, sameUser, setComment]);

  const onSaveComment = useCallback<ReturnType<typeof useCommenter>>((...args) => {
    changeEditMode(false);
    onEditModeChange(false);
    return setComment(...args);
  }, [changeEditMode, onEditModeChange, setComment]);

  const onEditButtonClick = useCallback(() => {
    setEditModeValue(true);
    setSelection(iri);
  }, [iri, setEditModeValue, setSelection]);

  const onDeleteButtonClick = useCallback(() => {
    changeDeleteMode(true);
    setSelection(iri);
  }, [changeDeleteMode, iri, setSelection]);

  const maybeDisabled = useCallback((className: string) => {
    return className + (disabled || deleteMode ? ' disabled' : '');
  }, [disabled, deleteMode]);

  const maybeUserDisabled = useCallback((className: string) => {
    return className + (!sameUser ? ' disabled' : '');
  }, [sameUser]);

  const maybeFocusDisabled = useCallback((className: string) => {
    const extraDisabled = disabled
      || focus && focus.comment !== comment.commentIri;
    return className + (extraDisabled ? ' disabled' : '');
  }, [comment.commentIri, disabled, focus]);

  return <div className={'comment-content'}>
    <div className="misc">
      <div className={maybeDisabled('date')}>{displayUser
        ? <Translate
            defaultMessage="{date} by {user, select, true {Me} other {{user}}}"
            values={{user: comment.user === user || user, date}} />
        : comment.created.toLocaleDateString()}</div>
      {deleteMode
      && <div className="delete-buttons">
        <Button
          className="vm-confirm-button"
          onClick={onDeleteComment}>
          <Translate defaultMessage="Delete comment?" />
        </Button>
        <Button
          className="vm-cancel-button"
          onClick={() => changeDeleteMode(false)}>
          <Translate defaultMessage="Cancel" />
        </Button>
      </div>
      || <div className={maybeFocusDisabled('buttons')}>
        <Button
          className={maybeUserDisabled('edit-button')}
          tooltipMessage={intl.translate({defaultMessage: 'Edit'})}
          onClick={onEditButtonClick}>
          <SVGPencil />
        </Button>
        <Button
          className={maybeUserDisabled('delete-button')}
          tooltipMessage={intl.translate({defaultMessage: 'Delete'})}
          onClick={onDeleteButtonClick}>
          <SVGBin />
        </Button>
      </div>}
    </div>
    {editMode && <CommentEditor
      intl={intl}
      disabled={false}
      iri={iri}
      mode="edit"
      onClose={() => setEditModeValue(false)}
      onSaveComment={onSaveComment}
      comment={comment} />
    || <Fragment>
      <div className={maybeDisabled('text vm-c')} {...kd.render(comment.body || '')} />
      <CommentType
        className={maybeDisabled('')}
        category={comment.category}
        showCategories={showCategories} />
    </Fragment>}
  </div>;
}

type CommentAddComponentProps = {
  item: CommentsItem;
  addMode: boolean;
  disabled: boolean;
  disableAddMode: () => void;
  enableAddMode: () => void;
  onSaveComment: ReturnType<typeof useCommenter>;
  selectedRef?: RefObject<HTMLDivElement>;
};
function CommentAddComponent({item, addMode, disabled, disableAddMode, enableAddMode, onSaveComment, selectedRef}: CommentAddComponentProps) {
  const {maybeButtonDisabled} = usePanelFocus();
  const intl = useTranslation();

  return <div className="comment-add">
    {!addMode
    && <Button
      style={{width: '100%'}}
      className={maybeButtonDisabled('vm-toggle-button vm-comment-add-button', disabled, item.iri, 'add')}
      aria-pressed={false}
      onClick={enableAddMode}>
      <SVGBubblePlus />
      <span><Translate defaultMessage="Add comment" /></span>
    </Button>
    || <CommentEditor
      ref={selectedRef}
      intl={intl}
      disabled={disabled}
      iri={item.iri}
      mode="add"
      onSaveComment={onSaveComment}
      onClose={disableAddMode} />}
  </div>;
}

type CommentsItemComponentProps = Readonly<{
  intl: Translator;
  kd: Renderer;
  collapsed: boolean;
  disabled: boolean;
  item: CommentsItem;
  user: string;
  onEditModeChange: (value: boolean) => void;
  onHeaderClicked: (iri: string) => void;
  onToggle: ([iri]: string) => void;
  selection: string|null;
  selectedRef?: RefObject<HTMLDivElement>;
  showCategories: boolean;
}>;
export default function CommentsItemComponent(props: CommentsItemComponentProps): RNode {
  const {intl, kd, collapsed, disabled, item, user, onEditModeChange, onHeaderClicked, onToggle, selection, selectedRef, showCategories} = props;
  const setComment = useCommenter();
  const {focus} = usePanelFocus();
  const {changeFocus} = usePanelFocusUpdater();

  const addMode = focus?.item === item.iri && focus?.mode === 'add';
  const isSelected = item.iri === selection;

  const disableAddMode = useCallback(() => changeFocus('add', false, item.iri),
    [changeFocus, item.iri]);
  const enableAddMode = useCallback(() => changeFocus('add', true, item.iri),
    [changeFocus, item.iri]);

  const onEditModeHandler = useCallback((value: boolean) => {
    onEditModeChange(value);
  }, [onEditModeChange]);

  const onHeaderClickHandler = useCallback((e: Event) => {
    e.stopPropagation();
    onHeaderClicked(item.iri);
  }, [onHeaderClicked, item.iri]);

  const expandableTitle = item.comments.length > 1
    ? item.name + ` (${item.comments.length})`
    : item.name;

  return item.comments.length === 0
  && (isSelected && <div ref={selectedRef} className="comments-item" aria-current="true">
    <div className="vm-t">
      <LinkForSelect iri={selection} onClick={(e) => e.stopPropagation()}>{expandableTitle}</LinkForSelect>
    </div>
    <div className="comments-new-item-content">
      <CommentAddComponent
        item={item}
        addMode={addMode}
        disableAddMode={disableAddMode}
        enableAddMode={enableAddMode}
        disabled={disabled}
        onSaveComment={setComment}
        selectedRef={selectedRef} />
    </div>
  </div>)
  || <Accordion
    ref={selectedRef}
    className="comments-item"
    isSelected={isSelected}
    disabled={disabled}
    header={<LinkForSelect iri={item.iri} onClick={onHeaderClickHandler}>{expandableTitle}</LinkForSelect>}
    collapsed={collapsed}
    onToggle={() => onToggle(item.iri)}>
    {item.comments.map((comment) => <CommentContent
      key={comment.commentIri}
      intl={intl}
      kd={kd}
      disabled={disabled}
      iri={item.iri}
      comment={comment}
      user={user}
      displayUser={true}
      setComment={setComment}
      onEditModeChange={onEditModeHandler}
      showCategories={showCategories}
    />)}
    {isSelected && <CommentAddComponent
      item={item}
      addMode={addMode}
      disableAddMode={disableAddMode}
      enableAddMode={enableAddMode}
      disabled={disabled}
      onSaveComment={setComment}
      selectedRef={selectedRef} />}
  </Accordion>;
}
