// List item for the InclusionFilterControl component

/** @jsx h */
import {Fragment, h} from 'preact';
import {useMemo} from 'preact/hooks';
import type {RNode, TargetedEvent} from '../types';
import {useTranslation} from '../intl';

import Button from './Button';

const X = <Fragment>&#10005;</Fragment>;

type InclusionFilterItemProps = Readonly<{
  value: boolean | undefined;
  label: string;
  iri: string;
  onChange: (value: boolean | undefined, key: string) => void;
}>;
export function InclusionFilterItem(props: InclusionFilterItemProps): RNode {
  const {label, onChange, iri, value} = props;
  const intl = useTranslation();

  return useMemo(() => {
    const filterToggle = (e: TargetedEvent<HTMLButtonElement, Event>) => {
      e.preventDefault();
      onChange(!value, iri);
    };

    const filterRemove = (e: TargetedEvent<HTMLButtonElement, Event>) => {
      e.preventDefault();
      onChange(undefined, iri);
    };

    return <li>
      <Button className={value ? 'text-green' : 'text-red'} onClick={filterToggle}>
        <span>{value ? intl.translate({defaultMessage: 'Included'}) : intl.translate({defaultMessage: 'Excluded'})}</span>
      </Button>
      <span>{label}</span>
      <Button onClick={filterRemove} tooltipMessage={intl.translate({defaultMessage: 'Remove'})}><span>{X}</span></Button>
    </li>;
  }, [intl, iri, label, onChange, value]);
}
