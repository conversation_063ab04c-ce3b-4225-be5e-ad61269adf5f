// Dynamic loading of fonts required for map content
/** @jsx h */

import {h} from 'preact';

import {useMemo} from 'preact/hooks';
import {useModel} from '../context/triples';

import type {CSSProperties, Existence, RNode} from '../types';

export type FontProperties = Pick<CSSProperties, 'fontFamily'|'fontStyle'|'fontWeight'> & {resolved: boolean};

type FaceFamily = {name: string; chain?: string};
type FaceMod = {weight: 100|300|400|500|700|900; style: 'italic'|'normal'};
type FaceOrigin = {load?: boolean; loadName?: string; adobeFonts?: boolean};
type FaceInfo = FaceFamily & FaceMod & FaceOrigin;

type FaceMap = {[faceName: string]: FaceInfo | null};

const infoCache: FaceMap = {};

const ARIAL: FaceFamily = {
  name: 'Arial',
  chain: '"Liberation Sans", Arial, sans-serif',
};
const CALIBRI: FaceFamily & FaceOrigin = {
  name: '<PERSON><PERSON>ri',
  chain: '<PERSON><PERSON><PERSON>, <PERSON><PERSON>, "Noto Sans", sans-serif',
  load: true,
  loadName: '<PERSON><PERSON>',
};
const CENTURY_GOTHIC: FaceFamily & FaceOrigin = {
  name: 'Century Gothic',
  chain: '"Century Gothic", "Century Gothic Pro", "URW Gothic", Mulish, sans-serif',
  load: true,
  loadName: 'Mulish',
};
const UNIVIA_PRO: FaceFamily & FaceOrigin = {
  name: 'univia-pro',
  chain: '"univia-pro", "Exo", sans-serif',
  load: true,
  loadName: 'Exo',
};
const FUTURAPT: FaceFamily & FaceOrigin = {
  name: 'futura-pt',
  chain: '"futura-pt", "Hind", sans-serif',
  load: true,
  loadName: 'Hind',
};

const FACE_FAMILIES: {[baseFontFamily: string]: FaceFamily} = {
  'ArialMT': {...ARIAL},
  'Calibri': {...CALIBRI},
  'CenturyGothicPro': {...CENTURY_GOTHIC},
  // Special case handling here because we can't figure out how to export
  // the font to be 'UniviaPro-Regular' from illustrator
  'UniviaProRegular': {...UNIVIA_PRO},
  'UniviaPro': {...UNIVIA_PRO},
  'FuturaPT': {...FUTURAPT},
};

const FACE_MODS: {[option: string]: Partial<FaceMod>} = {
  'Black': {weight: 900},
  'Bold': {weight: 700},
  'Italic': {style: 'italic'},
  'Light': {weight: 300},
  'Regular': {style: 'normal', weight: 400},
  'Medium': {weight: 500},
};

const CUSTOM_FONTS: FaceMap = {
  'Calibri-Light': {...CALIBRI, style: 'normal', weight: 100},
};

const GOOGLE_FONT_NAMES: {[name: string]: string} = {
  'PTSans': 'PT Sans',
  'Raleway': 'Raleway',
  'Roboto': 'Roboto',
  'NotoSans': 'Noto Sans',
};

function buildFaceInfo(faceName: string): FaceInfo | null {
  const regular = FACE_MODS['Regular'] as FaceMod;

  if (faceName in FACE_FAMILIES) {
    return {...regular, ...FACE_FAMILIES[faceName]};
  }

  const regex = /^(\w+)-(Regular|(Light|Medium|Bold|Black)(Italic)?|Italic)(\w*)$/;
  const m = faceName.match(regex);
  if (m) {
    let info: FaceInfo|null = null;
    const baseFaceName = m[1] + m[m.length - 1];
    if (baseFaceName in GOOGLE_FONT_NAMES) {
      info = {...regular, load: true, name: GOOGLE_FONT_NAMES[baseFaceName]};
    } else if (baseFaceName in FACE_FAMILIES) {
      info = {...regular, ...FACE_FAMILIES[baseFaceName]};
    }
    if (info) {
      const mods = m.slice(2, -1);
      return mods.reduce((acc, mod) => ({...acc, ...FACE_MODS[mod]}), info);
    }
  }
  return null;
}

function getFaceInfo(faceName: string): FaceInfo | null {
  if (infoCache[faceName] === undefined) {
    infoCache[faceName] = CUSTOM_FONTS[faceName] || buildFaceInfo(faceName);
  }
  return infoCache[faceName];
}

const SANS_CHAIN = ARIAL.chain;

export function resolveFont(faceName: string): FontProperties {
  const info = getFaceInfo(faceName);
  if (!info) {
    return {fontFamily: SANS_CHAIN, resolved: false};
  }
  return {
    fontFamily: info.chain || `"${info.name}", sans-serif`,
    fontStyle: info.style,
    fontWeight: info.weight,
    resolved: true,
  };
}

function describeVariations(keys: string[]): string {
  const [ital, wght] = keys.reduce((acc, s) => [
    acc[0] || s.indexOf('1') === 0,
    acc[1] || s.slice(2) !== '400',
  ], [false, false]);
  if (!ital && !wght) {
    return '';
  }
  // Order doesn't matter, just want stable output
  keys.sort();
  if (!wght) {
    return ':ital@' + keys.map(s => s.slice(0, 1)).join(';');
  }
  if (!ital) {
    return ':wght@' + keys.map(s => s.slice(2)).join(';');
  }
  return ':ital,wght@' + keys.join(';');
}

function encode(s: string): string {
  return s.split(' ').map(encodeURIComponent).join('+');
}

function toFamilyParams(families: [string, Existence][]): string[] {
  return families.map(([name, used]) => `family=${encode(name)}${describeVariations(Object.keys(used))}`);
}

export default function LoadFonts(): RNode | null {
  const mm = useModel();

  return useMemo(() => {
    const fontsUsed: {[name: string]: Existence} = {};
    for (const fontAlias in mm.allLabelFonts()) {
      // Treat aliases as a whitelist for now - Illustrator mangles all names
      const font = getFaceInfo(fontAlias);
      if (font?.load) {
        const {name, loadName, style, weight} = font;
        // Could encode this info in bits, but string keys are simple.
        (fontsUsed[loadName || name] ??= {})[`${+(style === 'italic')},${weight}`] = true;
      }
    }

    const params = toFamilyParams(Object.entries(fontsUsed));
    return params.length ? <style>{
      `@import url('https://fonts.googleapis.com/css2?${params.join('&')}&display=swap');`
    }</style> : null;
  }, [mm]);
}
