// Linked expandable display of platform software licenses
/** @jsx h */

import {h} from 'preact';
import {useState} from 'preact/hooks';
import {useMarkdown} from '../context/mark';

import type {RNode} from '../types';

type State = {type: 'start'} | {type: 'pending'} | {type: 'failed'; err: Error} | {type: 'loaded'; md: string};

const LICENSE_URL = '/static/js/LICENSE.md';

export default function PlatformLicense(): RNode {
  const kd = useMarkdown();
  const [state, setState] = useState<State>({type: 'start'});

  const onClick = state.type === 'start'
    ? (e: Event) => {
        e.preventDefault();
        setState({type: 'pending'});
        fetch(LICENSE_URL)
          .then(r => r.ok ? r.text() : Promise.reject(new Error('http status ' + r.statusText)))
          .then(md => setState({type: 'loaded', md}), err => setState({type: 'failed', err}));
      }
    : state.type === 'pending'
      ? (e: Event) => {
          e.preventDefault();
        }
      : undefined;

  // TODO: There's an argument the default text here should be translated.
  // But is technical details, and intro can't have multiple langs either yet.
  return state.type === 'loaded'
    ? <div
        className="vm-c vm-license"
        {...kd.render(state.md)} />
    : <p className="vm-d">Portions of the platform are free software <a href={LICENSE_URL} {...{onClick}}>used under license</a>.{state.type === 'failed' ? ` ${state.err}` : null}</p>;
}
