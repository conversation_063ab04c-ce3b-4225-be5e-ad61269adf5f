// Button component for navigating in selected item history

/** @jsx h */
import {h} from 'preact';
import {useCallback, useMemo} from 'preact/hooks';

import {useHistoryNavigation, useHistoryQuery} from '../context/selectionhistory';
import SVGArrowWide from '../svg/SVGArrowWide';
import LinkForSelect from './LinkForSelect';

import type {Hint, RNode} from '../types';

export type HistoryNavigationButtonProps = {
  title: string;
  direction: 'forwards'|'backwards';
  hint?: Hint;
};
export default function HistoryNavigationButton({title, direction, hint}: HistoryNavigationButtonProps): RNode {
  const {moveBackwards, moveForwards, head} = useHistoryNavigation();
  const {describeRecentHistory} = useHistoryQuery();
  const history = useMemo(() => describeRecentHistory(), [describeRecentHistory]);

  const forwards = direction === 'forwards';
  const disabled = (forwards && head > history.length - 2) || (!forwards && head < 1);
  const iri = !disabled ? history[forwards ? head + 1 : head - 1].iri : null;

  const className = [
    'vm-toggle-button',
    forwards ? 'flip' : '',
  ].filter(Boolean).join(' ');

  const skipHistory = true;

  const clickHandler = useCallback((e: Event) => {
    e.preventDefault();
    if (forwards) {
      moveForwards();
    } else {
      moveBackwards();
    }
  }, [forwards, moveBackwards, moveForwards]);

  return <LinkForSelect {...{iri: iri || '', skipHistory, className, disabled, title, hint, onClick: clickHandler}}>
    <SVGArrowWide />
  </LinkForSelect>;
}
