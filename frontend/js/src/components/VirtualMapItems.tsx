// A component that renders only visible items on the map
/** @jsx h */

import {Fragment, h} from 'preact';
import {useMemo} from 'preact/hooks';

import RBush from 'rbush';

import {useMap} from '../context/ol';

import type {Extent} from 'ol/extent';
import type {BBox} from 'rbush';

import type {ViewportTarget} from '../context/ol';
import type {RNode} from '../types';

function padExtent(extent: number[], pad: number[]) {
  return extent.map((e, i) => i >> 1
    ? e - (pad[i] || 0)
    : e + (pad[i] || 0));
}

function pointToBBox([x, y]: number[]): BBox {
  return {minX: x, minY: y, maxX: x, maxY: y};
}

function rectToBBox([minX, minY, maxX, maxY]: number[]): BBox {
  return {minX, minY, maxX, maxY};
}

function coordsToBBox(coords: number[]) {
  if (coords.length === 2) {
    return pointToBBox(coords);
  }
  return rectToBBox(coords);
}

export type Props = {
  items: {node: RNode|null; coords: number[]}[];
  targetString: string|null;
  padding?: Extent;
};

export default function VirtualMapItems(props: Props): RNode {
  const {items, targetString, padding} = props;
  const {map: _, state} = useMap();
  const targetJsonString = state.target ? JSON.stringify(state.target) : targetString;

  const tree = useMemo(() => {
    const tree = new RBush<BBox & {index: number; node: RNode|null}>();
    tree.load(items.map(({node, coords}, index) => (
      {...coordsToBBox(coords), index, node}
    )));
    return tree;
  }, [items]);

  const visibleItems = useMemo(() => {
    if (targetJsonString) {
      const {extent, scale}: ViewportTarget = JSON.parse(targetJsonString);
      const paddedExtent = padding ? padExtent(extent, padding.map(x => x / scale)) : extent;

      return tree.search(rectToBBox(paddedExtent))
        .sort((a, b) => a.index - b.index)
        .map(i => i.node);
    }
    return [];
  }, [tree, targetJsonString, padding]);

  return <Fragment>
    {visibleItems}
  </Fragment>;
}
