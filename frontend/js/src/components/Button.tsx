// Basic Button component wrapper.
/** @jsx h */

import {h} from 'preact';
import {forwardRef} from 'preact/compat';

import type {Attrs, ForwardRef, RNode, RefCallback} from '../types';


type ButtonProps = Attrs['button'] & {
  tooltipMessage?: string;
  refCallback?: RefCallback<HTMLButtonElement>;
};
const Button = forwardRef((props: ButtonProps, forwardedRef: ForwardRef<HTMLButtonElement>): RNode => {
  const {children, tooltipMessage, refCallback, ...restProps} = props;
  const ref = forwardedRef || refCallback;

  return <button
    {...restProps}
    ref={ref}
    title={tooltipMessage}
    type="button"
  >
    {children}
  </button>;
});

export default Button;
