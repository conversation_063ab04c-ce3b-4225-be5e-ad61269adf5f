// A list of direct association labels unhandled in other places
/** @jsx h */
import {h} from 'preact';
import {useMemo} from 'preact/hooks';

import {useModel} from '../context/triples';
import {getLabelledRelations, sortLabelledRelations} from '../labelledrelations';
import ObjectLink from './ObjectLink';

import type {Existence, Hint, RNode} from '../types';

export type GenericRelationsProps = Readonly<{
  iri: string;
  handledPreds?: Existence;
  hint?: Hint;
}>;
export default function GenericRelations({iri, handledPreds, hint}: GenericRelationsProps): RNode|null {
  const mm = useModel();

  const generics = useMemo(() => {
    const relations = getLabelledRelations(mm, iri, handledPreds);
    sortLabelledRelations(relations, mm);
    return relations;
  }, [mm, iri, handledPreds]);

  return generics.length && <div className="vm-d">{
    generics.map(({label, other, iri, defaultOtherLabel}, i) => <p key={i}>
      {label} {other && <ObjectLink value={other} hint={hint} relIri={iri} /> || defaultOtherLabel()}
    </p>)
  }</div> || null;
}
