// Toggles between map view and list view

/** @jsx h */
import {Fragment, h} from 'preact';
import {useCallback, useMemo, useState} from 'preact/hooks';

import {useListContent, useListContentUpdaters} from '../context/page';
import {useNavigation, usePlace} from '../context/place';
import {useScreenProperties} from '../context/screen';
import useFilteredItems from '../hooks/useFilteredItems';
import useGroupedItems from '../hooks/useGroupedItems';
import {Translate, useTranslation} from '../intl';
import SVGArrowEnd from '../svg/SVGArrowEnd';
import SVGArrowWide from '../svg/SVGArrowWide';
import SVGPin from '../svg/SVGPin';
import SVGSearch from '../svg/SVGSearch';
import Button from './Button';
import ItemListContent from './ItemList';

import type {GroupedItems} from '../hooks/useGroupedItems';
import type {RNode, TargetedEvent} from '../types';
import type {ListHint, ListViewItem} from './ItemList';

const itemsPerPageOptions = [25, 50, 100, 200];
type Page = {
  current: number;
};

/**
 * Focuses the first tababble focusable element in the list content.
 *
 * @param elementRef High enough level container
 */
function focusList(elementRef: {current: HTMLElement | null}) {
  elementRef.current?.querySelector<HTMLElement>('.vm-full-items-content [tabindex="0"]')?.focus();
};

/**
 * Flattens grouped and ungrouped items into a single array for pagination.
 *
 * Converts a structured list of grouped and ungrouped items into a linear array,
 * preserving order while marking group headers. This ensures smooth pagination.
 *
 * @param {GroupedItems} groupedItems - The structured input data.
 * @returns {Array} A single array containing all items in order.
 */
function flattenItems(groupedItems: GroupedItems): ListViewItem[] {
  const flattened: ListViewItem[] = [];

  groupedItems.groups.forEach(({initial, items}) => {
    flattened.push({...initial, groupIri: initial.iri, firstInGroup: true});
    flattened.push(...items.map(i => ({...i, groupIri: initial.iri})));
  });

  if (groupedItems.ungrouped.length) {
    const [first, ...rest] = groupedItems.ungrouped;
    flattened.push({...first, firstInGroup: true});
    flattened.push(...rest.map(i => ({...i})));
  }

  return flattened;
};

/**
 * Mutates the current page index in a paginated list based on the provided selection and flattened items.
 *
 * @param {Page} page - The page object whose `current` property is updated.
 * @param {string|null} selection - The selected item's IRI, or `null` if no selection is made.
 * @param {ListViewItem[]} flattenedItems - The full list of items in a flattened structure.
 * @param {number} itemsPerPage - The number of items displayed per page.
 *
 * @returns {void}
 *
 * @note `useMemo` is used here to mutate the `page.current` property directly.
 *       This prevents unnecessary state updates in `useEffect`, which would trigger
 *       re-renders and disrupt the natural flow of updates.
 */
function useMutateCurrentPage(page: Page, selection: string|null, flattenedItems: ListViewItem[], itemsPerPage: number): void {
  useMemo(() => {
    page.current = 0;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [flattenedItems]);

  useMemo(() => {
    if (selection) {
      const selectedIndex = flattenedItems.findIndex(item => item.iri === selection);
      if (selectedIndex !== -1) {
        const newPage = Math.floor(selectedIndex / itemsPerPage);
        page.current = newPage;
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selection, itemsPerPage, flattenedItems]);
}

export type FullListProps = Readonly<{
  lang: string;
}>;

export function FullItemList(props: FullListProps): RNode {
  const [listExpand, setListExpand] = useState(false);
  const {lang} = props;
  const {items, length} = useFilteredItems();
  const groupedItems = useGroupedItems(items, props.lang, false);
  const {isLargeScreen} = useScreenProperties();
  const [itemsPerPage, setItemsPerPage] = useState(itemsPerPageOptions[1]);
  const {selection} = usePlace();
  const intl = useTranslation();
  const {triggerRef} = useListContent();
  const {setElementRef} = useListContentUpdaters();
  const [listHint] = useState<ListHint>({name: 'initial'});
  const [page, setPage] = useState<Page>({current: 0});

  const flattenedItems = useMemo(() => flattenItems(groupedItems), [groupedItems]);

  useMutateCurrentPage(page, selection, flattenedItems, itemsPerPage);

  const totalPages = Math.ceil(flattenedItems.length / itemsPerPage);
  const startIndex = page.current * itemsPerPage;
  const paginatedItems = flattenedItems.slice(startIndex, startIndex + itemsPerPage);
  const hasGroups = flattenedItems.some(item => item.groupIri);

  const handleItemsPerPageChange = (event: TargetedEvent<HTMLSelectElement>) => {
    const newItemsPerPage = parseInt(event.currentTarget.value, 10);
    setItemsPerPage(newItemsPerPage);
    setPage({current: 0});
  };

  const goToPrevPage = useCallback(() => {
    setPage({current: Math.max(0, page.current - 1)});
  }, [page]);

  const goToNextPage = useCallback(() => {
    setPage({current: Math.min(totalPages - 1, page.current + 1)});
  }, [page, totalPages]);

  const setFocusHint = useCallback(() => {
    listHint.name = 'kbNav';
  }, [listHint]);

  const onKeyDown = useCallback((e: KeyboardEvent) => {
    e.stopPropagation();
    if (e.key === 'ArrowLeft') {
      e.preventDefault();
      goToPrevPage();
      setFocusHint();
    } else if (e.key === 'ArrowRight') {
      e.preventDefault();
      goToNextPage();
      setFocusHint();
    } else if (e.key === 'Esc' || e.key === 'Escape') {
      e.preventDefault();
      triggerRef.current?.focus();
    }
  }, [goToNextPage, goToPrevPage, setFocusHint, triggerRef]);

  const listTitle = intl.translate({defaultMessage: 'List View'});

  return <section
    ref={setElementRef}
    className="vm-full-items-container"
    onKeyDown={onKeyDown}
    aria-label={listTitle}>
    <div className={'vm-full-items' + (listExpand ? ' expand' : '')}>
      <div className="vm-full-items-header">
        <div>
          <SVGSearch />
          <span>{listTitle} (<span data-testid="list-view-items-count">{length}</span>)</span>
        </div>
        <Button
          className="vm-collapse-button"
          aria-expanded={listExpand}
          data-testid={listExpand ? 'list-view-collapse-all-button' : 'list-view-expand-all-button'}
          onClick={() => setListExpand(v => !v)}
        >
          {!listExpand
            ? <Translate defaultMessage="Expand all" />
            : <Translate defaultMessage="Collapse all" />}
        </Button>
      </div>
      {length > 0 && <div className="vm-full-items-content">
        <ItemListContent
          items={paginatedItems}
          allowKeyDownPropagate={true}
          hasGroups={hasGroups}
          listHint={listHint}
          expand={listExpand ? 'all' : 'none'}
          lang={lang}
        />
      </div> || <p className="vm-aside">
        <Translate defaultMessage="No items. Change view or filters to explore." />
      </p> }
      {flattenedItems.length > itemsPerPageOptions[0] && <div className="vm-pagination">
        {/* Arrow buttons group */}
        <div className="vm-pagination-buttons-group">
          <Button
            onClick={() => {
              setPage({current: 0});
            }}
            tooltipMessage={intl.translate({defaultMessage: 'First page'})}
            type="button"
            data-testid="list-view-pagination-first-page"
            className="vm-toggle-button"
            disabled={page.current === 0}
          >
            <SVGArrowEnd />
          </Button>
          <Button
            onClick={() => {
              goToPrevPage();
            }}
            tooltipMessage={intl.translate({defaultMessage: 'Previous page'})}
            type="button"
            data-testid="list-view-pagination-previous-page"
            className="vm-toggle-button"
            disabled={page.current === 0}
          >
            <SVGArrowWide />
          </Button>
          <div
            aria-live="polite">
            {isLargeScreen && <Fragment><Translate defaultMessage="Page" />&nbsp;</Fragment>}
            <span data-testid="list-view-pagination-current-page">{page.current + 1}</span>
            &nbsp;
            <Translate defaultMessage="of" />
            &nbsp;
            <span data-testid="list-view-pagination-total-pages">{totalPages}</span>
          </div>
          <Button
            onClick={() => {
              goToNextPage();
            }}
            tooltipMessage={intl.translate({defaultMessage: 'Next page'})}
            type="button"
            data-testid="list-view-pagination-next-page"
            className="vm-toggle-button"
            disabled={page.current === totalPages - 1}
          >
            <SVGArrowWide className="vm-svg flip" />
          </Button>
          <Button
            onClick={() => {
              setPage({current: totalPages - 1});
            }}
            tooltipMessage={intl.translate({defaultMessage: 'Last page'})}
            type="button"
            data-testid="list-view-pagination-last-page"
            className="vm-toggle-button"
            disabled={page.current === totalPages - 1}
          >
            <SVGArrowEnd className="vm-svg flip" />
          </Button>
        </div>
        {/* Items Per Page Selector */}
        <div className="vm-items-per-page">
          <label htmlFor="itemsPerPage">
            {isLargeScreen ? <Translate defaultMessage="Items per page" /> : <Translate defaultMessage="Items" />}
          </label>
          <select
            value={itemsPerPage}
            onChange={e => {
              handleItemsPerPageChange(e);
            }}
            data-testid="list-view-items-dropdown"
            aria-label={intl.translate({defaultMessage: 'Items per page'})}
          >
            {itemsPerPageOptions.map(size => (
              <option key={size} value={size}>
                {size}
              </option>
            ))}
          </select>
        </div>
      </div>}
    </div>
  </section>;
}

export function MapListToggle(): RNode {
  const {clearHint} = useNavigation();
  const intl = useTranslation();
  const {open: isListOpen, elementRef: listRef} = useListContent();
  const {toggle, setTriggerRef} = useListContentUpdaters();

  const onKeyDown = (e: KeyboardEvent) => {
    e.stopPropagation();
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      focusList(listRef);
    }
  };

  return <Button
    ref={setTriggerRef}
    id="list-view-toggle"
    tooltipMessage={intl.translate({defaultMessage: 'List View'})}
    className="vm-toggle-button"
    aria-pressed={isListOpen}
    onKeyDown={onKeyDown}
    onClick={() => {
      toggle();
      clearHint();
    }}>
    {isListOpen ? <SVGPin /> : <SVGSearch />}
  </Button>;
}
