// Link to set both view and selection
/** @jsx h */

import {h} from 'preact';
import {useCallback} from 'preact/hooks';

import {useNavigation} from '../context/place';

import Link from './Link';

import type {AnchorAttrs, Hint, RNode, TargetedEvent} from '../types';

export type Props = Readonly<Omit<AnchorAttrs, 'onClick'|'href'> & {
  select: string;
  view: string;
  disabled?: boolean;
  hint?: Hint;
  skipHistory?: boolean;
  onClick?: (e: TargetedEvent<HTMLAnchorElement, Event>) => void;
}>;

export default function LinkForViewAndSelect(props: Props): RNode {
  const {hint, select, view, skipHistory, onClick, ...remainingProps} = props;
  const {hrefForSelection, hrefForView, setViewAndSelection} = useNavigation();

  const clickHandler = useCallback((e: TargetedEvent<HTMLAnchorElement, Event>) => {
    const select = e.currentTarget.getAttribute('data-select');
    const view = e.currentTarget.getAttribute('data-view');
    if (select && view) {
      e.preventDefault();
      setViewAndSelection(view, select, hint, skipHistory);
    }
    if (onClick) {
      onClick(e);
    }
  }, [hint, onClick, setViewAndSelection, skipHistory]);

  return <Link
    {...remainingProps}
    data-select={select}
    data-view={view}
    href={hrefForView(view) + hrefForSelection(select)}
    onClick={clickHandler}
  />;
}
