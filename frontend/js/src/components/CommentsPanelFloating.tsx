// Component for displaying comments on map

/** @jsx h */

import {Fragment, h} from 'preact';
import {useCallback, useEffect, useMemo, useRef, useState} from 'preact/hooks';

import {useCommentCategories, useComments} from '../context/annotation';
import {usePanelFocus, usePanelFocusUpdater} from '../context/comments';
import {useMarkdown} from '../context/mark';
import {PANEL_LABELS, Panel} from '../context/panels';
import {usePlace} from '../context/place';
import {useModel} from '../context/triples';
import {useUser} from '../context/user';
import {animateScroll} from '../functions/animateScroll';
import usePrevious from '../hooks/usePrevious';
import {Translate, useTranslation} from '../intl';
import {localeInsensitive} from '../sort';
import SVGBubbleText from '../svg/SVGBubbleText';
import SVGInfo from '../svg/SVGInfo';

import Button from './Button';
import CommentsItemComponent from './CommentsItem';
import PanelHeader from './shared/PanelHeader';

import type {Comment, CommentCategory} from '../context/annotation';
import type {MapModel} from '../rdfmodels';
import type {Existence, RNode} from '../types';

export type ExtendedComment = Omit<Comment, 'category'> & {category?: CommentCategory};
export type CommentsItem = {
  iri: string;
  name: string;
  comments: ExtendedComment[];
};

function buildItems(
  categoriesByIri: {[iri: string]: CommentCategory},
  commentsByItem: {[iri: string]: Comment[]},
  selection: string|null,
  mm: MapModel): CommentsItem[] {
  let selectionHasComments = false;
  const items = Object.entries(commentsByItem).reduce<CommentsItem[]>((acc, [iri, comments]) => {
    if (comments.length) {
      acc.push({
        iri,
        name: mm.nameOf(iri),
        comments: comments.map(c => ({...c, category: c.category ? categoriesByIri[c.category] : undefined})),
      });

      if (iri === selection) {
        selectionHasComments = true;
      }
    }

    return acc;
  }, []);

  if (selection && !selectionHasComments) {
    items.push({
      iri: selection,
      name: mm.nameOf(selection),
      comments: [],
    });
  }
  items.sort((a, b) => localeInsensitive(a.name, b.name));
  return items;
}

function toExistence(items: CommentsItem[]): Existence {
  return items.reduce<Existence>((acc, item) => {
    acc[item.iri] = true; return acc;
  }, {});
}

export default function CommentsPanelFloating():RNode|null {
  const intl = useTranslation();
  const kd = useMarkdown();
  const mm = useModel();
  const commentsByItem = useComments();
  const categories = useCommentCategories();
  const {selection} = usePlace();
  const prevSelection = usePrevious(selection);
  const user = useUser();
  const {focus} = usePanelFocus();
  const {clearFocus} = usePanelFocusUpdater();
  const commentsBodyRef = useRef<HTMLDivElement>(null);
  const selectedRef = useRef<HTMLDivElement|null>(null);
  const scroll = useRef(false);
  const [disabled, setDisabled] = useState(false);
  const [expansion, setExpansion] = useState<Existence>({});

  const addMode = focus?.item && focus?.mode === 'add';

  const categoriesByIri = useMemo(() => categories.reduce<{[iri: string]: CommentCategory}>((acc, c) => {
    acc[c.iri] = c;
    return acc;
  }, {}), [categories]);

  const items = useMemo(() => buildItems(categoriesByIri, commentsByItem, selection, mm),
    [categoriesByIri, commentsByItem, mm, selection]);

  const onItemHeaderClickHandler = useCallback((iri: string) => {
    scroll.current = true;
    if (!expansion[iri]) {
      setExpansion((prev) => ({...prev, [iri]: true}));
    }
  }, [expansion, setExpansion]);

  useEffect(() => {
    if (!selection) {
      selectedRef.current = null;
      if (prevSelection && !commentsByItem[prevSelection]?.length) {
        setExpansion((prev) => {
          const {[prevSelection]: _, ...next} = prev;
          return next;
        });
      }
    } else {
      if (prevSelection !== selection) {
        scroll.current = true;
        setExpansion((prev) => ({...prev, [selection]: true}));
      }
    }
  }, [commentsByItem, prevSelection, selection]);

  useEffect(() => {
    if (scroll.current) {
      scroll.current = false;
      if (commentsBodyRef?.current && selectedRef?.current) {
        animateScroll({
          element: commentsBodyRef.current,
          targetPosition: selectedRef.current.offsetTop - commentsBodyRef.current.offsetTop,
          duration: 1000,
          stopAtApproximation: true,
        });
      }
    }
  });

  useEffect(() => clearFocus, [clearFocus]);
  useEffect(() => {
    if (prevSelection !== selection) {
      setDisabled(false);
    }
  }, [selection, prevSelection]);

  const toggleCollapsible = useCallback((iri: string) => {
    setExpansion((prev) => {
      const next = {...prev};
      if (!next[iri]) {
        next[iri] = true;
      } else if (focus?.item !== iri) {
        delete next[iri];
      }
      return next;
    });
  }, [focus]);
  const toggleCollapsibleAll = useCallback((collapsed: boolean) => {
    setExpansion(collapsed
      ? focus?.item
        ? {[focus.item]: true}
        : {}
      : toExistence(items));
  }, [items, focus]);

  const shouldExpandAll = Object.keys(expansion).length === 0;
  const count = items.reduce((acc, item) => acc + item.comments.length, 0);
  const hasComments = count > 0;

  const headerTitle = intl.translate({defaultMessage: 'Comments'}) + (count > 0 ? ` (${count})` : '');

  return <section className="vm-float-block comments" aria-label={PANEL_LABELS[Panel.Comments]}>
    <PanelHeader
      panel={Panel.Comments}
      icon={<SVGBubbleText />}
      title={headerTitle}
      customControls={<Fragment>
        {hasComments && <Button
          className={'vm-collapse-button' + (disabled || addMode ? ' disabled' : '')}
          aria-expanded={!shouldExpandAll}
          data-testid={shouldExpandAll ? 'comments-expand-all-button' : 'comments-collapse-all-button'}
          onClick={() => toggleCollapsibleAll(!shouldExpandAll)}>
          {shouldExpandAll
            ? <Translate defaultMessage="Expand all" />
            : <Translate defaultMessage="Collapse all" />}
        </Button>}
      </Fragment>}
    />
    <div
      ref={commentsBodyRef}
      className="comments-body">
      {items.map((item) => <CommentsItemComponent
        key={item.iri}
        intl={intl}
        kd={kd}
        disabled={disabled}
        collapsed={!expansion[item.iri]}
        item={item}
        user={user}
        onEditModeChange={setDisabled}
        onHeaderClicked={onItemHeaderClickHandler}
        onToggle={toggleCollapsible}
        selection={selection}
        showCategories={categories.length > 1}
        selectedRef={selection === item.iri ? selectedRef : undefined} />)}
      {!selection && !hasComments && <div className={'vm-info-block no-comments' + (disabled ? ' disabled' : '')}>
        <Translate defaultMessage="There are no comments yet" />
      </div>}
      {!selection && <div className={'vm-info-block selection-info' + (disabled ? ' disabled' : '')}>
        <SVGInfo className="vm-svg vm-stroke-xsm" />
        <span><Translate defaultMessage="Select item to add a comment" /></span>
      </div>}
    </div>
  </section>;
}
