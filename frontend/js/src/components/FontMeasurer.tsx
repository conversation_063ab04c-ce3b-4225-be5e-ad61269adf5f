// A virtual list component that renders only visible rows with dynamic sizes
/** @jsx h */

import {h} from 'preact';
import {useEffect, useRef} from 'preact/hooks';

import type {RNode} from '../types';

function getFontSize(element: Element) {
  return parseFloat(getComputedStyle(element).fontSize);
}

type Props = Readonly<{
  fontSize: string | number;
  onResize: (fontSize: number) => void;
}>;
export default function FontMeasurer(props: Props): RNode {
  const {fontSize, onResize} = props;

  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!window.ResizeObserver) {
      return undefined;
    }

    const observer = new ResizeObserver(() => {
      if (elementRef.current) {
        onResize(getFontSize(elementRef.current));
      }
    });

    if (observer && elementRef.current) {
      observer.observe(elementRef.current);
    } else if (elementRef.current) {
      onResize(getFontSize(elementRef.current));
    } else {
      onResize(getFontSize(document.body));
    }

    return () => {
      observer.disconnect();
    };
  }, [onResize]);

  return <div ref={elementRef} style={{fontSize}}></div>;
}
