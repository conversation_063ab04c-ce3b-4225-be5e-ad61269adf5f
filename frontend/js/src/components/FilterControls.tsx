// User controls for applying filters
/** @jsx h */

import {h} from 'preact';
import {useCallback, useMemo} from 'preact/hooks';

import {useNavigation, usePlace} from '../context/place';
import {useModel} from '../context/triples';
import useClasses from '../hooks/useClasses';
import {useDebounce} from '../hooks/useDebounce';
import {Translate, useTranslation} from '../intl';
import {InclusionFilterControl} from './InclusionFilterControl';
import ClearInputButton from './shared/ClearInputButton';

import type {Plottability, RNode, TargetedEvent} from '../types';

const mapPlottability: {[v: string]: Plottability} = {
  'direct': 'direct',
  'related': 'related',
  'detached': 'detached',
};

export default function FilterControls(): RNode {
  const mm = useModel();
  const {allExternalClasses} = useClasses();
  const {setFilters} = useNavigation();
  const {filters, view} = usePlace();
  const intl = useTranslation();

  const {debouncedFunction: updateSearch, clearDebounce} = useDebounce((search: string) => {
    setFilters({...filters, search});
  }, [filters, setFilters]);

  const searchChange = useCallback((e: TargetedEvent<HTMLInputElement>) => {
    updateSearch(e.currentTarget.value);
  }, [updateSearch]);

  const onClearSearchInput = useCallback(() => {
    setFilters({...filters, search: ''});
    clearDebounce();
  }, [filters, setFilters, clearDebounce]);

  const plottabilityChange = useCallback((e: TargetedEvent<HTMLSelectElement>) => {
    const plottable = mapPlottability[e.currentTarget.value];
    setFilters({...filters, plottable});
  }, [filters, setFilters]);

  return useMemo(() => {
    const plottable = filters.plottable || 'any';

    const nameNamer = (iri: string) => mm.nameOfOrFallback(iri);
    const classNamer = (iri: string) => mm.className(iri);

    return <div className="vm-filter-controls vm-float-block-content">
      <form className="pure-form" key={view}>
        <fieldset>
          <label htmlFor="filter-text"><Translate defaultMessage="Filter to items containing text:" /></label>
          <div className="vm-clearable-input-container">
            <input
              type="search"
              id="filter-text"
              className="pure-u-1"
              placeholder={intl.translate({defaultMessage: 'Type...'})}
              value={filters.search || ''}
              onKeyDown={(e) => e.stopPropagation()}
              onChange={searchChange} />
            <ClearInputButton value={filters.search || ''} onClearInput={onClearSearchInput} />
          </div>
        </fieldset>

        <InclusionFilterControl
          id="filter-classes"
          heading={intl.translate({defaultMessage: 'Filter to only classes:'})}
          inclusion={filters.aType || {}}
          namer={classNamer}
          optionIris={allExternalClasses()}
          onChange={(i) => setFilters({...filters, aType: i})} />

        <InclusionFilterControl
          id="filter-relation"
          heading={intl.translate({defaultMessage: 'Filter to only items related to:'})}
          inclusion={filters.relation || {}}
          namer={nameNamer}
          optionIris={mm.named()}
          onChange={(i) => setFilters({...filters, relation: i})} />

        <InclusionFilterControl
          id="filter-categories"
          heading={intl.translate({defaultMessage: 'Filter to only categories:'})}
          inclusion={filters.itemKinds || {}}
          namer={nameNamer}
          optionIris={Object.keys(mm.allCategories())}
          onChange={(i) => setFilters({...filters, itemKinds: i})} />

        <fieldset className="vm-input-row">
          <label><Translate defaultMessage="Plottable:" /></label>
          <select onChange={plottabilityChange}>
            <option value="direct" selected={'direct' === plottable}><Translate defaultMessage="Appears directly on map" /></option>
            <option value="related" selected={'related' === plottable}><Translate defaultMessage="Relates to items on map" /></option>
            <option value="detached" selected={'detached' === plottable}><Translate defaultMessage="Unrelated to items on map" /></option>
            <option value="any" selected={'any' === plottable}><Translate defaultMessage="All items" /></option>
          </select>
        </fieldset>
      </form>
    </div>;
  }, [filters, view, mm, intl, searchChange, allExternalClasses, plottabilityChange, setFilters, onClearSearchInput]);
}
