// Top level component for Shared Meaning map terms application
/** @jsx h */

import {h} from 'preact';
import {Suspense} from 'preact/compat';
import {useErrorBoundary, useState} from 'preact/hooks';

import {MarkdownProvider} from '../context/mark';
import {UserProvider, resolveUser} from '../context/user';
import {getSettings} from '../functions/htmlData';
import useWanna from '../hooks/useWanna';
import {loadMapData} from '../loadmap';
import ErrorReport from './ErrorReport';
import MapTermsLive from './MapTermsLive';

import 'purecss/build/pure-min.css';
import 'style/mapterms.css';
import 'style/fonts.css';

import type {RNode} from '../types';

const DEFAULT_API_USER = '/api/user';

export default function MapTerms(): RNode {
  const [error] = useErrorBoundary();

  const [settings] = useState(() => getSettings());

  const userReader = useWanna(() => resolveUser(settings['me-url'] || DEFAULT_API_USER));
  const mapReader = useWanna(() => loadMapData(settings['api-root'], settings['map-iri']));

  if (error) {
    return <ErrorReport error={error} />;
  }

  return <MarkdownProvider>
    <Suspense fallback={null}>
      <UserProvider userReader={userReader}>
        <MapTermsLive {...{mapReader}} />
      </UserProvider>
    </Suspense>
  </MarkdownProvider>;
}
