// Setting domains filter
/** @jsx h */

import {h} from 'preact';

import {useDomains} from '../context/domains';
import {useNavigation, usePlace} from '../context/place';
import {useModel} from '../context/triples';
import {toggleGroupedInclusion} from '../functions/keyed';
import {_labelOf} from '../functions/labels';
import {IRIS} from '../rdf';
import RegionSelect from './RegionSelect';
import SearchSelect from './SearchSelect';

import type {RNode} from '../types';

type DomainSelectProp = {
  onFocus?: (e: HTMLElement) => void;
};

export default function DomainSelect({onFocus}: DomainSelectProp): RNode|null {
  const mm = useModel();
  const {setFilters} = useNavigation();
  const {filters} = usePlace();
  const domains = useDomains();

  const onSingleSelectChange = (iri: string, value: string) => {
    const prev = Object.keys(filters.domain?.[iri] || {})[0] || '';
    // only change domain selection if the new domain is different from the previous one
    if (prev !== value) {
      setFilters({
        ...filters,
        domain: toggleGroupedInclusion(filters.domain, iri, value.length ? value : prev, true),
      });
    }
  };

  return domains.length ? (
    <div className="vm-domains">
      {domains.map(({iri, selection, values}) => {
        if (iri === IRIS.VM.Region) {
          return <RegionSelect key={iri} values={values} />;
        }
        if (selection === 'multi') {
          return null;
        }
        const label = mm.className(iri);
        const value = Object.keys(filters.domain?.[iri] || {})[0];
        const items = values.map(iri => {
          return {key: iri, name: mm.nameOf(iri)};
        });
        return selection === 'single'
        && <SearchSelect
          label={label}
          onSelectChange={(val) => {
            onSingleSelectChange(iri, val);
          }}
          items={items}
          value={value}
          onSelectOpen={onFocus} />
        || null;
      })}
    </div>
  ) : null;
}
