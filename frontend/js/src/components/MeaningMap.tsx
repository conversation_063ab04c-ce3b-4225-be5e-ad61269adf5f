// Base map container and bindings
/** @jsx h */

import {Fragment, h} from 'preact';

import {MapContainer} from '../context/ol';
import {usePlaceBase} from '../context/placebase';
import {useModel} from '../context/triples';
import MapContent from './MapContent';
import ViewMapLayers from './ViewMapLayers';

import type {TileLayerOptionSubset} from '../hooks/useOlSettings';
import type {RNode} from '../types';

function MapLayer(): RNode | null {
  const mm = useModel();
  const {view} = usePlaceBase();

  const classList = ['vm-map-container'];
  if (mm.isGrayscale(view)) {
    classList.push('faded');
  }

  return <div className="vm-layer">
    <MapContainer className={classList.join(' ')} data-view-iri={view} />
  </div>;
}

export type Props = Readonly<{
  tilesSrc: string;
} & TileLayerOptionSubset>;

export default function MeaningMap(props: Props): RNode {
  const {
    extent,
    maxNativeZoom,
  } = props;
  return <Fragment>
    <MapLayer />
    <ViewMapLayers {...props} />
    <MapContent {...{extent, maxNativeZoom}} />
  </Fragment>;
}
