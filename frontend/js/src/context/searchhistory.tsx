// Provider for search history
/** @jsx h */

import {createContext, h} from 'preact';
import {useMemo, useRef, useState} from 'preact/hooks';

import useEnsuredContext from '../hooks/useEnsuredContext';
import {defaultCompare} from '../sort';
import {useModel} from './triples';

import type {MapModel} from '../rdfmodels';
import type {ComponentChildren, Existence, RNode, SearchEntry} from '../types';

const ITEMS_MAX_LENGTH = 100;
const LIMITED = true;
const LOCAL_STORAGE_KEY = 'vmSearchHistory';

type SearchEntryBase = Omit<SearchEntry, 'timestamp'>;

export type LocalStorageData = {[mapIri: string]: SearchEntry[]};

// Existence and uniqueness
export function sanitize(mm: MapModel, entries: SearchEntry[]): SearchEntry[] {
  const seen: Existence = {};
  return entries.filter(({type, value}) => {
    const first = !seen[type + value];
    seen[type + value] = true;
    return (type === 'term' || mm.subjectExists(value)) && first;
  });
}

// Snapshot the localStorage value and maintain the map's history order through mutations.
export function load(mm: MapModel, mapIri: string): SearchEntry[] {
  const storageData = JSON.parse(localStorage.getItem(LOCAL_STORAGE_KEY) || '{}');
  const result = sanitize(mm, storageData[mapIri] || []);
  result.sort(entryCompare);
  return result;
}

function save(mapIri: string, data: SearchEntry[], cache: LocalStorageData): SearchEntry[] {
  if (data.length) {
    cache[mapIri] = data;
  } else {
    delete cache[mapIri];
  }
  localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(cache));
  return cache[mapIri] || [];
}

// Later timestamp should be in front and item before term.
function entryCompare(a: SearchEntry, b: SearchEntry): number {
  return defaultCompare(b.timestamp, a.timestamp) || -(a.type === 'item') || 1;
}

function moveOrAddEntriesToFront(source: SearchEntry[], entries: SearchEntryBase[]): SearchEntry[] {
  const timestamp = new Date().toISOString();
  const result = removeEntries(source, entries);
  const refreshedEntries = entries.map<SearchEntry>(entry => ({...entry, timestamp}));
  result.unshift(...refreshedEntries);
  return result;
}

function removeEntries<T extends {value: string}>(source: SearchEntry[], entries: T[]): SearchEntry[] {
  const existing = entries.reduce<Existence>((acc, e) => {
    acc[e.value] = true;
    return acc;
  }, {});
  return source.filter(e => !existing[e.value]);
}

interface SearchHistoryMutate {
  clear(): void;
  remove(...entries: SearchEntry[]): void;
  push(...entries: Omit<SearchEntry, 'timestamp'>[]): void;
}

const SearchHistoryStateContext = createContext<SearchEntry[] | null>(null);
const SearchHistoryMutateContext = createContext<SearchHistoryMutate | null>(null);

type Props = {
  mapIri: string;
  children: ComponentChildren;
};
export function SearchHistoryProvider({mapIri, children}: Props): RNode {
  const mm = useModel();
  const storageCache = useRef<LocalStorageData>({});
  const [entries, setEntries] = useState<SearchEntry[]>(() => {
    const data = load(mm, mapIri);
    save(mapIri, data, storageCache.current);
    return data;
  });

  const mutators = useMemo(() => {
    const clear = () => {
      setEntries(prev => {
        if (prev.length) {
          return save(mapIri, [], storageCache.current);
        }
        return prev;
      });
    };

    const remove = (...entries: SearchEntry[]) => {
      setEntries(prev => {
        if (prev.length) {
          const next = removeEntries(prev, entries);
          return save(mapIri, next, storageCache.current);
        }
        return prev;
      });
    };

    const push = (...entries: Omit<SearchEntry, 'timestamp'>[]) => {
      setEntries(prev => {
        let next = moveOrAddEntriesToFront(prev, entries);
        if (LIMITED && next.length > ITEMS_MAX_LENGTH) {
          next = next.slice(0, ITEMS_MAX_LENGTH);
        }
        return save(mapIri, next, storageCache.current);
      });
    };
    return {
      clear,
      remove,
      push,
    };
  }, [mapIri]);

  return (
    <SearchHistoryStateContext.Provider value={entries}>
      <SearchHistoryMutateContext.Provider value={mutators}>
        {children}
      </SearchHistoryMutateContext.Provider>
    </SearchHistoryStateContext.Provider>
  );
}

export function useSearchHistory(): SearchEntry[] {
  return useEnsuredContext(SearchHistoryStateContext);
}

export function useSearchHistoryUpdater(): SearchHistoryMutate {
  return useEnsuredContext(SearchHistoryMutateContext);
}
