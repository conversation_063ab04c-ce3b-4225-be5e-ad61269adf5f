// Provider for panels
/** @jsx h */

import {createContext, h} from 'preact';
import {useCallback, useMemo, useState} from 'preact/hooks';

import useEnsuredContext from '../hooks/useEnsuredContext';
import {useTranslation} from '../intl';

import type {ComponentChildren, RNode} from '../types';

/**
 * Panel type enums.
 */
export enum Panel {
  About,
  Comments,
  Filters,
  MapSelector,
  Media,
  Overlay,
  RecentSearches,
  SearchResults,
  SelectedItem,
}

/**
 * Metadata for panel state management.
 *
 * @property {boolean} open -
 *    Indicates whether the panel is currently open.
 * @property {boolean} openTriggered -
 *    Meta flag indicating whether the panel opened by external action.
 *    When openTriggered is manually set to false, the action is considered
 *    done.
 * @property {boolean} elementRef -
 *    Reference to panel element. Set by {@link ContextPanelUpdaters} setElement.
 * @todo Expand with opening element to refocus on close? Do for quiet updates
 *    if needed.
 *
 * @see {@link OpenContext}
 */
export type PanelMetadata<T = HTMLElement> = {
  open: boolean;
  openTriggered: boolean;
  elementRef: {current: T|null};
} & Partial<OpenContext>;

type PanelsMetadata = {[key in Panel]: PanelMetadata};
type PanelsLabels = {[key in Panel]: string};

/**
 * Open action Context.
 *
 * @property {boolean} onClose -
 *    A callback when panel is closed.
 */
type OpenContext = {
  onClose: () => void;
};

const DEFAULT_METADATA: PanelMetadata = {
  open: false,
  openTriggered: false,
  elementRef: {current: null},
};

function copy(metadata: PanelMetadata): PanelMetadata {
  return {
    ...metadata,
    elementRef: {current: metadata.elementRef.current},
  };
}

/**
 * Labels for panels.
 *
 * @see {@link PanelsLabels}
 * @see {@link Panel}
 */
export const PANEL_LABELS: PanelsLabels = {
  [Panel.About]: '',
  [Panel.Comments]: '',
  [Panel.Filters]: '',
  [Panel.MapSelector]: '',
  [Panel.Media]: '',
  [Panel.Overlay]: '',
  [Panel.RecentSearches]: '',
  [Panel.SearchResults]: '',
  [Panel.SelectedItem]: '',
};

const defaultMetadatas: () => PanelsMetadata = () => ({
  [Panel.About]: copy(DEFAULT_METADATA),
  [Panel.Comments]: copy(DEFAULT_METADATA),
  [Panel.Filters]: copy(DEFAULT_METADATA),
  [Panel.MapSelector]: copy(DEFAULT_METADATA),
  [Panel.Media]: copy(DEFAULT_METADATA),
  [Panel.Overlay]: copy(DEFAULT_METADATA),
  [Panel.RecentSearches]: copy(DEFAULT_METADATA),
  [Panel.SearchResults]: copy(DEFAULT_METADATA),
  [Panel.SelectedItem]: copy(DEFAULT_METADATA),
});

type ContextPanelUpdaters = {
  changeOpen: (panel: Panel) => (value: boolean, context?: OpenContext) => void;
  toggleOpen: (panel: Panel) => () => void;
  quietClearOpenTrigger: (panel: Panel) => () => void;
  quietSetElementRef: (panel: Panel) => (e: HTMLElement | null) => void;
  clearAllOpen: () => void;
};

/**
 * Panel State Updaters for {@link Panel}.
 *
 * @property {(value: boolean) => void} changeOpen -
 *    A function to change the open state of a panel.
 * @property {() => void} toggleOpen -
 *    A function to toggle the open state of a panel.
 * @property {() => void} clearOpenTrigger -
 *    A function to clear the "openTriggered" state of a panel.
 * @property {() => void} setElementRef -
 *    A function to set the panel element ref.
 * @property {() => void} clearAllOpen -
 *    A function to clear all "open" states.
 */
export type PanelUpdaters = {
  changeOpen: (value: boolean, context?: OpenContext) => void;
  toggleOpen: () => void;
  clearOpenTrigger: () => void;
  setElementRef: (e: HTMLElement | null) => void;
  clearAllOpen: () => void;
};

const PanelsContext = createContext<PanelsMetadata|null>(null);
const PanelUpdaterContext = createContext<ContextPanelUpdaters|null>(null);

/**
 * Preserve metadata reference when a panel state has not changed,
 * or create a new one otherwise.
 *
 * @note preserve should reset openTriggered too?
 */
function preserveOrNextOpen(prev: PanelsMetadata, panel: Panel, openValue: boolean, context?: OpenContext): PanelsMetadata {
  return prev[panel].open === openValue ? prev : {
    ...prev,
    [panel]: {
      ...prev[panel],
      open: openValue,
      openTriggered: openValue,
      ...context,
    },
  };
}

function useTranslatePanelLabels(): void {
  const intl = useTranslation();
  useMemo(() => {
    const translations: PanelsLabels = {
      [Panel.About]: intl.translate({defaultMessage: 'About'}),
      [Panel.Comments]: intl.translate({defaultMessage: 'Comments'}),
      [Panel.Filters]: intl.translate({defaultMessage: 'Filters'}),
      [Panel.MapSelector]: intl.translate({defaultMessage: 'Map Selector'}),
      [Panel.Media]: intl.translate({defaultMessage: 'Media'}),
      [Panel.Overlay]: intl.translate({defaultMessage: 'Overlay'}),
      [Panel.RecentSearches]: intl.translate({defaultMessage: 'Recent Searches'}),
      [Panel.SearchResults]: intl.translate({defaultMessage: 'Search Results'}),
      [Panel.SelectedItem]: intl.translate({defaultMessage: 'Selected Item'}),
    };
    Object.assign(PANEL_LABELS, translations);
  }, [intl]);
}

type Props = Readonly<{
  children: ComponentChildren;
}>;

/**
 * Provider that manages panel states
 *
 * Responsible for providing panel-related state and interface for updating
 * them.
 */
export default function PanelsProvider({children}: Props): RNode {
  const [metadata, setMetadata] = useState<PanelsMetadata>(defaultMetadatas());

  useTranslatePanelLabels();

  // TODO: Sync open with other state manager contexts
  const changeOpen = useCallback((panel: Panel) => (value: boolean, context?: OpenContext) => {
    setMetadata(prev => {
      if (!value) {
        prev[panel].onClose?.();
      }
      return preserveOrNextOpen(prev, panel, value, context);
    });
  }, []);

  const toggleOpen = useCallback((panel: Panel) => (context?: OpenContext) => {
    setMetadata(prev => {
      if (prev[panel].open) {
        prev[panel].onClose?.();
      }
      return preserveOrNextOpen(prev, panel, !prev[panel].open, !prev[panel].open ? context : undefined);
    });
  }, []);

  // Quiet updates to not trigger panels metadata change.
  const quietClearOpenTrigger = useCallback((panel: Panel) => () => {
    setMetadata(prev => {
      prev[panel].openTriggered = false;
      return prev;
    });
  }, []);

  // Quiet updates to not trigger panels metadata change.
  const quietSetElementRef = useCallback((panel: Panel) => (e: HTMLElement | null) => {
    setMetadata(prev => {
      prev[panel].elementRef.current = e;
      return prev;
    });
  }, []);

  // Clear openTriggered too?
  const clearAllOpen = useCallback(() => {
    setMetadata(prev => Object.values(prev).every(b => b.open === false) ? prev : defaultMetadatas());
  }, []);

  const updater = useMemo(() => ({
    changeOpen,
    toggleOpen,
    clearAllOpen,
    quietClearOpenTrigger,
    quietSetElementRef,
  }), [changeOpen, toggleOpen, clearAllOpen, quietClearOpenTrigger, quietSetElementRef]);

  return (
    <PanelsContext.Provider value={metadata}>
      <PanelUpdaterContext.Provider value={updater}>
        {children}
      </PanelUpdaterContext.Provider>
    </PanelsContext.Provider>
  );
}

/**
 * Hook providing state for a panel.
 *
 * @param panel - Panel type.
 * @returns Metadata of a panel.
 *
 * @see {@link Panel}
 * @see {@link PanelMetadata}
 */
export function usePanel<T extends HTMLElement>(panel: Panel): PanelMetadata<T> {
  return useEnsuredContext(PanelsContext)[panel] as PanelMetadata<T>;
}

/**
 * Hook providing interface to panel updaters.
 *
 * @param {Panel} panel - Panel type.
 * @returns function for updating panel states.
 *
 * @see {@link Panel}
 * @see {@link PanelUpdaters}
 */
export function usePanelUpdaters(panel: Panel): PanelUpdaters {
  const updaters = useEnsuredContext(PanelUpdaterContext);

  return useMemo(() => ({
    changeOpen: updaters.changeOpen(panel),
    toggleOpen: updaters.toggleOpen(panel),
    clearOpenTrigger: updaters.quietClearOpenTrigger(panel),
    setElementRef: updaters.quietSetElementRef(panel),
    clearAllOpen: updaters.clearAllOpen,
  }), [updaters, panel]);
}
