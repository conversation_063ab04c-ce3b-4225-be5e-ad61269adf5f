// Provider for comments panel
/** @jsx h */

import {h} from 'preact';
import {createContext} from 'preact';
import {useCallback, useState} from 'preact/hooks';

import {useEffectAfterMount} from '../hooks/useEffectAfterMount';
import useEnsuredContext from '../hooks/useEnsuredContext';
import usePrevious from '../hooks/usePrevious';
import {useComments} from './annotation';
import {usePlace} from './place';

import type {ComponentChildren, RNode} from '../types';

export type SectionMode = 'add'|'edit'|'delete';

export type CommentsPanelFocus = {
  focus: {
    item: string;
    mode: SectionMode;
    comment?: string;
  }|null;
  maybeButtonDisabled: (className: string, disabled: boolean, item: string, mode: SectionMode, comment?: string) => string;
};
export type CommentsPanelFocusUpdate = {
  changeFocus: (mode: SectionMode, enabled: boolean, iri: string, commentIri?: string) => void;
  clearFocus: () => void;
};

const CommentsPanelFocusContext = createContext<CommentsPanelFocus|null>(null);
const CommentsPanelFocusUpdateContext = createContext<CommentsPanelFocusUpdate|null>(null);

function CommentsEffects(): null {
  const {selection} = usePlace();
  const prevSelection = usePrevious(selection);
  const commentsByItem = useComments();
  const {changeFocus, clearFocus} = usePanelFocusUpdater();

  useEffectAfterMount(() => {
    if (prevSelection && !selection) {
      clearFocus();
      return;
    }

    if (prevSelection && (selection !== prevSelection)) {
      changeFocus('add', false, prevSelection);
      changeFocus('delete', false, prevSelection);
      changeFocus('edit', false, prevSelection);
    }
  }, [changeFocus, clearFocus, commentsByItem, prevSelection, selection]);

  return null;
}

function equalFocusProps(focus: Exclude<CommentsPanelFocus['focus'], null>, item: string, mode: SectionMode, comment?: string) {
  return focus.mode === mode && focus.item === item && (!comment || focus.comment === comment);
}

type Props = Readonly<{
  children: ComponentChildren;
}>;
export default function CommentsProvider({children}: Props): RNode {
  const [focus, setFocus] = useState<CommentsPanelFocus['focus']|null>(null);

  const changeFocus = useCallback((mode: SectionMode, enabled: boolean, iri: string, commentIri?: string) => {
    setFocus(prev => {
      return enabled ? {item: iri, comment: commentIri, mode}
        : prev && !equalFocusProps(prev, iri, mode, commentIri) ? prev : null;
    });
  }, []);
  const clearFocus = useCallback(() => {
    setFocus(null);
  }, []);

  const maybeButtonDisabled = useCallback((className: string, disabled: boolean, item: string, mode: SectionMode, comment?: string) => {
    return className + (disabled || focus && !(equalFocusProps(focus, item, mode, comment))
      ? ' disabled'
      : '');
  }, [focus]);

  return (
    <CommentsPanelFocusContext.Provider value={{focus, maybeButtonDisabled}}>
      <CommentsPanelFocusUpdateContext.Provider value={{changeFocus, clearFocus}}>
        <CommentsEffects />
        {children}
      </CommentsPanelFocusUpdateContext.Provider>
    </CommentsPanelFocusContext.Provider>
  );
}

export function usePanelFocus(): CommentsPanelFocus {
  return useEnsuredContext(CommentsPanelFocusContext);
}

export function usePanelFocusUpdater(): CommentsPanelFocusUpdate {
  return useEnsuredContext(CommentsPanelFocusUpdateContext);
}
