// Provider for flexsearch index on the map
/** @jsx h */

import {createContext, h} from 'preact';
import {useMemo} from 'preact/hooks';

import {useModel} from './triples';
import {useMarkdown} from './mark';
import {buildIndex} from '../buildsearchindex';
import useEnsuredContext from '../hooks/useEnsuredContext';

import type {ComponentChildren, Existence, RNode} from '../types';

const SearchContext = createContext<SearchEngine | null>(null);

export interface SearchEngine {
  search: (text: string) => SearchResult;
}

type SubjectIRI = string;

export type IndexResultItem = {
  id: number;
  doc: {
    subjectIRI: SubjectIRI;
  };
};

export type SearchEngineResult = {
  field: string;
  result: IndexResultItem[];
};

export type SearchResult = {
  name: SubjectIRI[];
  alts: SubjectIRI[];
  keywords: SubjectIRI[];
  description: SubjectIRI[];
};

type Props = Readonly<{
  children: ComponentChildren;
  lang: string;
}>;

type Mapper<T> = (iri: string) => T;

/**
 * Gathers and processes unique search results from the search engine based on the given term.
 *
 * @template T - The type of the items returned after mapping.
 * @param {SearchEngine} engine - The search engine instance used to perform the search.
 * @param {string} term - The search term used to query the search engine.
 * @param {Mapper<T>} fn - A mapping function that processes each unique result (identified by an IRI).
 * @returns {T[]} An array of processed results of type `T`.
 */
export function gather<T>(engine: SearchEngine, term: string, fn: Mapper<T>): T[] {
  const seen: Existence = {};

  const results: T[] = [];
  const add = (iri: string) => {
    if (!(iri in seen)) {
      seen[iri] = true;
      results.push(fn(iri));
    }
  };
  const {name, alts, keywords, description} = engine.search(term);
  alts.forEach(add);
  name.forEach(add);
  keywords.forEach(add);
  description.forEach(add);
  return results;
}

/**
 * A React component that provides a search engine context for its children.
 *
 * @param {Props} props - The component properties.
 * @param {ReactNode} props.children - The child components that will have access to the search context.
 * @param {string} props.lang - The language used for building the search index.
 * @returns {RNode} A React node that wraps its children in the `SearchContext.Provider`.
 */
export function SearchProvider(props: Props): RNode {
  const {children, lang} = props;
  const mm = useModel();
  const kd = useMarkdown();
  // TODO: This may want some smarter logic and be stored in a ref. Also
  // rebuilding the index perhaps worse than waiting for kd to load?
  const index = useMemo(() => buildIndex(mm, kd, lang), [mm, kd, lang]);

  const searchEngine = useMemo(() => ({
    search: (text: string) => {
      const result : SearchResult = {
        name: [],
        alts: [],
        keywords: [],
        description: [],
      };
      const searchResults = index.search(text, {enrich: true, limit: Infinity}) as SearchEngineResult[];
      for (const field of searchResults) {
        const buildResult = (resultField : string[]) => {
          field.result.forEach((item: IndexResultItem) => {
            resultField.push(item.doc.subjectIRI);
          });
        };
        switch (field.field) {
          case 'name[]':
            buildResult(result.name);
            break;
          case 'alts[]':
            buildResult(result.alts);
            break;
          case 'keywords[]':
            buildResult(result.keywords);
            break;
          case 'description':
            buildResult(result.description);
            break;
        }
      }
      return result;
    },
  }), [index]);

  return (
    <SearchContext.Provider value={searchEngine}>
      {children}
    </SearchContext.Provider>
  );
}

export function useSearchEngine(): SearchEngine {
  return useEnsuredContext(SearchContext);
}
