// Provider for platform related states, dealing with pending states.

/** @jsx h */

import {createContext, h} from 'preact';
import {useCallback, useState} from 'preact/hooks';

import useEnsuredContext from '../hooks/useEnsuredContext';

import type {ComponentChildren, RNode} from '../types';

type State = boolean;
type PlatformStates = {
  map: State;
  base: State;
};
type PlatformStateType = keyof PlatformStates;
type PlatformStateUpdater = (updated: Partial<PlatformStates>) => void;

const PlatformStateContext = createContext<PlatformStates|null>(null);
const PlatformStateUpdateContext = createContext<PlatformStateUpdater|null>(null);

type Props = Readonly<{
  children: ComponentChildren;
}>;
export default function PlatformProvider({children}: Props): RNode {
  const [states, setStates] = useState<PlatformStates>({
    map: false,
    base: false,
  });

  const updater = useCallback((updated: Partial<PlatformStates>) => {
    setStates(prev => {
      if ((Object.keys(updated) as PlatformStateType[]).every((key) => prev[key] === updated[key])) {
        return prev;
      }
      return {...prev, ...updated};
    });
  }, []);

  return (
    <PlatformStateContext.Provider value={states}>
      <PlatformStateUpdateContext.Provider value={updater}>
        {children}
      </PlatformStateUpdateContext.Provider>
    </PlatformStateContext.Provider>
  );
}

export function usePlatformStates(): PlatformStates {
  return useEnsuredContext(PlatformStateContext);
}

export function usePlatformStatesUpdater(): PlatformStateUpdater {
  return useEnsuredContext(PlatformStateUpdateContext);
}
