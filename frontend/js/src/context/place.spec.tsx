/** @jsx h */

import {render} from '@testing-library/preact';
import {h} from 'preact';

import {ModelContext} from '../context/triples';
import {buildModel} from '../testing/model';
import {filtersGroupingsEqual, isGlobalSearch, useNavigation} from './place';
import {PlaceBaseContext, PlaceNavContext, PlaceProvider, PlaceUrlContext} from './placebase';
import {HistoryProvider} from './selectionhistory';

import type {ComponentChildren, Filters, Grouping, Hint} from '../types';

function renderWithPlace(children: ComponentChildren) {
  const base = 'http://base.test/';
  const hint: Hint = {name: 'load'};
  const url = {path: '/', search: '', hash: ''};
  const nav = () => undefined;
  const setHint = () => undefined;
  return render(
    <PlaceBaseContext.Provider value={base}>
      <PlaceUrlContext.Provider value={[url, hint]}>
        <PlaceNavContext.Provider value={[nav, setHint]}>
          <ModelContext.Provider value={buildModel([])}>
            <HistoryProvider >
              {children}
            </HistoryProvider>
          </ModelContext.Provider>
        </PlaceNavContext.Provider>
      </PlaceUrlContext.Provider>
    </PlaceBaseContext.Provider>,
  );
}

it('expect PlaceProvider to exist', () => {
  expect(PlaceProvider).toBeDefined();
});

it('expect renderWithPlace to render', () => {
  expect(renderWithPlace('test').container.textContent).toEqual('test');
});

it('returns selection', () => {
  const TestComponent = () => {
    const {hrefForSelection} = useNavigation();
    const href = hrefForSelection('http://visual-meaning.com/rdf/test');
    return <div>{href}</div>;
  };
  expect(renderWithPlace(<TestComponent />).container.textContent).toEqual('#test');
});

describe('filtersGroupingsEqual', () => {
  it('handles empty inclusion', () => {
    const a: Filters & Grouping = {aType: {}};
    const b: Filters & Grouping = {};
    expect(filtersGroupingsEqual(a, b)).toEqual(true);
  });
  it('handles empty values', () => {
    const a: Filters & Grouping = {search: '', sort: ''};
    const b: Filters & Grouping = {};
    expect(filtersGroupingsEqual(a, b)).toEqual(true);
  });
  it('handles multiple empty inclusions', () => {
    const a: Filters & Grouping = {aType: {}, itemKinds: {}, relation: {}};
    const b: Filters & Grouping = {domain: {}};
    expect(filtersGroupingsEqual(a, b)).toEqual(true);
  });
  it('handles negative', () => {
    const a: Filters & Grouping = {aType: {}};
    const b: Filters & Grouping = {aType: {something: false}};
    expect(filtersGroupingsEqual(a, b)).toEqual(false);
  });
});

describe('isGlobalSearch', () => {
  it('handles search', () => {
    const filters: Filters = {search: 'search', aType: {}};
    expect(isGlobalSearch(filters)).toEqual(true);
  });
  it('handles empty search', () => {
    const filters: Filters = {search: '', aType: {}};
    expect(isGlobalSearch(filters)).toEqual(true);
  });
  it('handles plottable', () => {
    const filters: Filters = {plottable: 'direct'};
    expect(isGlobalSearch(filters)).toEqual(false);
  });
  it('handles negative', () => {
    const filters: Filters = {search: 'search', aType: {something: true}};
    expect(isGlobalSearch(filters)).toEqual(false);
  });
});
