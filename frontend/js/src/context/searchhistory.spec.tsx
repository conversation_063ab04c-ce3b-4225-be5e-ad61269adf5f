import {buildModel} from '../testing/model';
import {load, sanitize} from './searchhistory';

import type {SearchEntry} from '../types';

describe('sanitize', () => {
  it('clears duplicate', () => {
    const model = buildModel([
      {subj: 'item1', pred: 'do', obj: 'something'},
    ]);
    const data: SearchEntry[] = [
      {'type': 'item', 'value': 'item1', 'timestamp': '1'},
      {'type': 'term', 'value': 'o', 'timestamp': '2'},
      {'type': 'item', 'value': 'item1', 'timestamp': '1'},
      {'type': 'term', 'value': 'oo', 'timestamp': '3'},
      {'type': 'term', 'value': 'o', 'timestamp': '2'},
    ];
    const expected: SearchEntry[] = [
      {'type': 'item', 'value': 'item1', 'timestamp': '1'},
      {'type': 'term', 'value': 'o', 'timestamp': '2'},
      {'type': 'term', 'value': 'oo', 'timestamp': '3'},
    ];
    expect(sanitize(model, data)).toEqual(expected);
  });
  it('clears non-existent', () => {
    const model = buildModel([
      {subj: 'item1', pred: 'do', obj: 'something'},
    ]);
    const data: SearchEntry[] = [
      {'type': 'item', 'value': 'item1', 'timestamp': '1'},
      {'type': 'item', 'value': 'item2', 'timestamp': '1'},
      {'type': 'term', 'value': 'o', 'timestamp': '2'},
    ];
    const expected: SearchEntry[] = [
      {'type': 'item', 'value': 'item1', 'timestamp': '1'},
      {'type': 'term', 'value': 'o', 'timestamp': '2'},
    ];
    expect(sanitize(model, data)).toEqual(expected);
  });
});

describe('load', () => {
  it('sorts', () => {
    const model = buildModel([
      {subj: 'item1', pred: 'do', obj: 'something'},
    ]);
    const data = `{"map1":[
      {"type": "term", "value": "item", "timestamp": "1"},
      {"type": "item", "value": "item1", "timestamp": "1"},
      {"type": "term", "value": "o", "timestamp": "2"},
      {"type": "term", "value": "oo", "timestamp": "3"}
    ]}`;
    localStorage.setItem('vmSearchHistory', data);
    const expected: SearchEntry[] = [
      {'type': 'term', 'value': 'oo', 'timestamp': '3'},
      {'type': 'term', 'value': 'o', 'timestamp': '2'},
      {'type': 'item', 'value': 'item1', 'timestamp': '1'},
      {'type': 'term', 'value': 'item', 'timestamp': '1'},
    ];
    expect(load(model, 'map1')).toEqual(expected);
  });
  it('sanitizes', () => {
    const model = buildModel([
      {subj: 'item1', pred: 'do', obj: 'something'},
    ]);
    const data = `{"map1":[
      {"type": "term", "value": "oo", "timestamp": "3"},
      {"type": "term", "value": "o", "timestamp": "2"},
      {"type": "item", "value": "item1", "timestamp": "1"},
      {"type": "term", "value": "item", "timestamp": "1"},
      {"type": "term", "value": "o", "timestamp": "2"},
      {"type": "item", "value": "item1", "timestamp": "1"}
    ]}`;
    localStorage.setItem('vmSearchHistory', data);
    const expected: SearchEntry[] = [
      {'type': 'term', 'value': 'oo', 'timestamp': '3'},
      {'type': 'term', 'value': 'o', 'timestamp': '2'},
      {'type': 'item', 'value': 'item1', 'timestamp': '1'},
      {'type': 'term', 'value': 'item', 'timestamp': '1'},
    ];
    expect(load(model, 'map1')).toEqual(expected);
  });
  it('non-existent map history', () => {
    const model = buildModel([
      {subj: 'item1', pred: 'do', obj: 'something'},
    ]);
    const data = `{"map1":[
      {"type": "term", "value": "o", "timestamp": "2"},
      {"type": "item", "value": "item1", "timestamp": "1"}
    ]}`;
    localStorage.setItem('vmSearchHistory', data);
    const expected: SearchEntry[] = [];
    expect(load(model, 'map2')).toEqual(expected);
  });
  it('empty map history', () => {
    const model = buildModel([
      {subj: 'item1', pred: 'do', obj: 'something'},
    ]);
    localStorage.setItem('vmSearchHistory', '{"map1":[]}');
    const expected: SearchEntry[] = [];
    expect(load(model, 'map1')).toEqual(expected);
  });
});
