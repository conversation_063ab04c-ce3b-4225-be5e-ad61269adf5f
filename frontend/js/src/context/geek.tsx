// Context for exposing internals to the user
/** @jsx h */

import {Fragment, createContext, h} from 'preact';
import {useContext, useState} from 'preact/hooks';

import {flagInStorage} from '../flags';

import type {ComponentChildren, RNode, StateUpdater} from '../types';

type PanelDisplay = 'norm'|'fade'|'hide';
type TilesOverride = 'none'|'base'|'leaf'|'hide';

type GeekOptions = {
  panelDisplay: PanelDisplay;
  showBounds: boolean;
  showPink: boolean;
  showAllAreas: boolean;
  showSelectionArea: boolean;
  tilesOverride: TilesOverride;
  showLenses: boolean;
  showMapTermsButton: boolean;
};

export type Geekishness = Readonly<GeekOptions & {
  panelOpen: boolean;
  setPanelOpen: StateUpdater<boolean>;

  updateShown: StateUpdater<GeekOptions>;
}>;

type StackTransformFunc = (stack: string[]) => string[];

const stackFunctions: {[k in TilesOverride]: StackTransformFunc} = Object.freeze({
  hide: _ => [],
  base: v => v.slice(0, 1),
  leaf: v => v.slice(-1),
  none: v => v,
});

export function makeStackTransform(tilesOverride: undefined|TilesOverride): StackTransformFunc {
  return stackFunctions[tilesOverride || 'none'];
}

export function useGeekery(): Geekishness | Readonly<{[k in string]?: never}> {
  const ctx = useContext(GeekContext);
  if (!flagInStorage('geek')) {
    return {};
  } else if (ctx == null) {
    throw new Error('missing provider');
  }
  return ctx;
}

const GeekContext = createContext<Geekishness|null>(null);

type Props = Readonly<{
  children: ComponentChildren;
}>;

function GeekProvider({children}: Props) {
  const [shownState, updateShown] = useState<GeekOptions>({
    panelDisplay: 'norm',
    showBounds: false,
    showPink: false,
    showAllAreas: false,
    showSelectionArea: true,
    tilesOverride: 'none',
    showLenses: true,
    showMapTermsButton: true,
  });
  const [panelOpen, setPanelOpen] = useState(false);

  return <GeekContext.Provider value={{
    ...shownState,
    panelOpen,
    setPanelOpen,
    updateShown,
  }}>
    {children}
  </GeekContext.Provider>;
}

export function MaybeGeek({children}: Props): RNode {
  if (flagInStorage('geek')) {
    return <GeekProvider>{children}</GeekProvider>;
  }
  return <Fragment>{children}</Fragment>;
}
