// Provider for current user details
/** @jsx h */

import {createContext, h} from 'preact';

import useEnsuredContext from '../hooks/useEnsuredContext';

import type {ComponentChildren, RNode} from '../types';

type UserInfo = {user: string};

type Props = {
  children: ComponentChildren;
  userReader: () => UserInfo | null;
};

const UserContext = createContext<UserInfo | null>(null);

function getAnonId(): string {
  const savedId = localStorage.getItem('vmAnonId');
  if (savedId) {
    return savedId;
  }
  let newId;
  if (window.crypto) {
    const array = new Uint8Array(12);
    crypto.getRandomValues(array);
    newId = 'anon-' + btoa(String.fromCharCode(...array));
  } else {
    newId = 'anonish-' + Math.floor(Math.random() * 89999 + 10000);
  }
  localStorage.setItem('vmAnonId', newId);
  return newId;
}

function handleApiUser(info: null | {user: {email: string|null}}): UserInfo {
  const user = info?.user.email;
  if (user) {
    return {user};
  }
  // For guest access maps, just generate an anonymous identity.
  return {user: getAnonId()};
}

function handleEasyAuthUser(info: null | {user_id: string}[]): UserInfo {
  const user = info?.[0]?.user_id;
  if (user) {
    return {user};
  }
  // When using Azure, having no user details is unacceptable, redirect
  // through the login flow again to pick up fresh creds.
  const here = location.href;
  location.href = new URL('/.auth/logout?' + new URLSearchParams({post_logout_redirect_uri: here}), here).href;
  // Value irrelevant as we'll be leaving when the page location changes above.
  return {user: 'expired'};
}

export function resolveUser(urlMe: string): Promise<UserInfo> {
  const isEasyAuth = urlMe.startsWith('/.auth/');
  return fetch(urlMe)
    .then(response => {
      if (response.ok) {
        return response.json();
      } else if (response.status !== 404) {
        console.log('failed to fetch current user');
      }
      return null;
    })
    .then(isEasyAuth ? handleEasyAuthUser : handleApiUser);
}

export function UserProvider({children, userReader}: Props): RNode {
  return <UserContext.Provider value={userReader()}>
    {children}
  </UserContext.Provider>;
}

export function useUser(): string {
  const userInfo = useEnsuredContext(UserContext);
  return userInfo.user;
}
