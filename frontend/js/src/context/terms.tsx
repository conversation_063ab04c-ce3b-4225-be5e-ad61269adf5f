// Provider terms
/** @jsx h */

import {createContext, h} from 'preact';
import {useMemo, useRef, useState} from 'preact/hooks';

import useEnsuredContext from '../hooks/useEnsuredContext';
import usePrevious from '../hooks/usePrevious';
import {modelFromData} from './triples';

import type {Triple} from '../rdf';
import type {ComponentChildren, MapData, RNode} from '../types';

export type RawTerm = {
  subj: string;
  pred: string;
  obj: string;
};

type TermsSwitch = {
  switchReshape: (_: boolean) => void;
  activeReshape: boolean;
};

const TermsContext = createContext<RawTerm[] | null>(null);
const TermsSwitchContext = createContext<TermsSwitch | null>(null);

type Props = {
  children: ComponentChildren;
  lang: string;
  mapReader: () => MapData;
};
/**
 * Triples provider.
 * Depending on which state of triples - raw or reshaped, it also provides
 * interfaces for switching between the two.
 *
 * @param props Component properties
 * @param props.mapReader Synchronous reader for map data resource.
 * @param props.lang Locale for extracting locale aware triples.
 */
export default function TermsProvider(props: Props): RNode {
  const {children, lang, mapReader} = props;
  const [activeReshape, setActiveReshape] = useState(false);
  const reshapedTermsRef = useRef<RawTerm[] | null>(null);
  const prevLang = usePrevious(lang);

  const [rawTerms, _] = useState(() => mapReader().terms.map(t => ({...t, obj: (t.obj || '') as string})));

  const terms = useMemo(() => {
    if (activeReshape) {
      if (prevLang !== lang || !reshapedTermsRef.current) {
        reshapedTermsRef.current = modelFromData(mapReader(), lang, true)
          ._store._triples.map((t: Triple) => ({subj: t.subj, pred: t.pred, obj: (t.obj || '') as string})) as RawTerm[];
      }
      return reshapedTermsRef.current;
    }
    return rawTerms;
  }, [activeReshape, rawTerms, prevLang, lang, mapReader]);

  const termsSwitch = useMemo(() => ({
    activeReshape,
    switchReshape: setActiveReshape,
  }), [activeReshape]);

  return (
    <TermsContext.Provider value={terms}>
      <TermsSwitchContext.Provider value={termsSwitch}>
        {children}
      </TermsSwitchContext.Provider>
    </TermsContext.Provider>
  );
}

export function useTerms(): RawTerm[] {
  return useEnsuredContext(TermsContext);
}

export function useTermsSwitch(): TermsSwitch {
  return useEnsuredContext(TermsSwitchContext);
}
