// Provider for internationalization
/** @jsx h */

import {h} from 'preact';
import {useCallback, useEffect, useState} from 'preact/hooks';
import {IntlProvider} from 'react-intl';

import type {MessageFormatElement} from 'react-intl';
import type {ComponentChildren, RNode} from '../types';

// TODO: Make this a type `'en' | 'ru' | ...` perhaps?
export enum SupportedLocale {
  en = 'en',
  fr = 'fr',
  pl = 'pl',
  ru = 'ru',
  zh = 'zh',
}

type Translations = {[key: string]: MessageFormatElement[]};

const resolveUrl = (lang: string) => `/static/lang/${encodeURIComponent(lang)}.json`;
function loadTranslations(locale: string) : Promise<Translations> {
  return fetch(resolveUrl(locale))
    .then(response => {
      if (response.status === 404) {
        return fetch(resolveUrl(SupportedLocale.en));
      }

      return response;
    })
    .then(response => {
      if (response.ok) {
        return response.json();
      }

      return null;
    });
}

// TODO: better logic
function negotiateLocale(
  requested: string,
  appLangs: SupportedLocale[],
  mapLangs: string[]): SupportedLocale {
  const browserLangs = navigator.languages ?? [navigator.language];

  let resolved = null;
  for (const lang of [requested, ...browserLangs]) {
    if (appLangs.includes(lang as SupportedLocale)
      && mapLangs.includes(lang)) {
      resolved = lang as SupportedLocale;
      break;
    }

    const short = lang.split('-')[0];
    if (appLangs.includes(short as SupportedLocale)
      && mapLangs.includes(short)) {
      resolved = short as SupportedLocale;
      break;
    }
  }

  return resolved ?? SupportedLocale.en;
}

export function useUserLanguage(mapLanguages: string[]): [SupportedLocale, (lang: SupportedLocale) => void] {
  const [value, setValue] = useState<SupportedLocale>(() => {
    const current = localStorage.getItem('lang') as SupportedLocale;
    const resolved = negotiateLocale(
      current || navigator.language,
      Object.values(SupportedLocale),
      mapLanguages);

    return resolved;
  });

  const set = useCallback((v: SupportedLocale) => {
    localStorage.setItem('lang', v);
    setValue(v);
  }, [setValue]);

  return [value, set];
}

type Props = Readonly<{
  children: ComponentChildren;
  lang: string;
}>;
export function TranslationProvider(props: Props): RNode|null {
  const {children, lang} = props;
  const [translations, setTranslations] = useState<Translations|undefined>(undefined);

  useEffect(() => {
    loadTranslations(lang)
      .then(t => setTranslations(t));
  }, [lang]);

  if (translations === undefined) {
    return null;
  }

  return <IntlProvider messages={translations} locale={lang} defaultLocale="en">
    {children}
  </IntlProvider>;
}
