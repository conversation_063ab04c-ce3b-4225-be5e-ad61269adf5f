// Provider for some states
/** @jsx h */

import {createContext, h} from 'preact';
import {useCallback, useMemo, useState} from 'preact/hooks';

import useEnsuredContext from '../hooks/useEnsuredContext';

import type {ComponentChildren, RNode} from '../types';

/**
 * Metadata for list content state management.
 *
 * @property {boolean} open -
 *    Indicates whether the list content is currently open.
 * @property {boolean} triggerRef -
 *    Reference to trigger to change list content state.
 *    Set by {@link ListContentUpdaters} setTriggerRef.
 * @property {boolean} elementRef -
 *    Reference to list content element.
 *    Set by {@link ListContentUpdaters} setElement.
 */
export type ListContentMetadata<T = HTMLElement> = {
  open: boolean;
  elementRef: {current: T|null};
  triggerRef: {current: T|null};
};

const DEFAULT_LIST_CONTENT_METADATA: ListContentMetadata = {
  open: false,
  elementRef: {current: null},
  triggerRef: {current: null},
};

/**
 * List Content Updaters.
 *
 * @property {(value: boolean) => void} changeOpen -
 *    A function to change the open state of a list content.
 * @property {() => void} toggleOpen -
 *    A function to toggle the open state of a list content.
 * @property {() => void} setElementRef -
 *    A function to set the list content element ref.
 */
interface ListContentUpdaters {
  toggle: () => void;
  setElementRef: (e: HTMLElement | null) => void;
  setTriggerRef: (e: HTMLElement | null) => void;
}

const StatesContext = createContext<ListContentMetadata | null>(null);
const StatesMutateContext = createContext<ListContentUpdaters | null>(null);

type Props = {
  children: ComponentChildren;
};
/**
 * Provider that manages page states. Currently only deals with list content state.
 *
 * Responsible for providing page-related state and interface for updating
 * them.
 */
export function PageProvider({children}: Props): RNode {
  // TODO: Some familiar naming here, but need to meaningfully extract and
  // unify list content and panels metadata behaviour
  const [listContentMetadata, setListContentMetadata] = useState<ListContentMetadata>(DEFAULT_LIST_CONTENT_METADATA);

  // Quiet updates to not trigger list content metadata change.
  const quietSetElementRef = useCallback((e: HTMLElement | null) => {
    setListContentMetadata(prev => {
      prev.elementRef.current = e;
      return prev;
    });
  }, []);

  // Quiet updates to not trigger list content metadata change.
  const quietSetTriggerElementRef = useCallback((e: HTMLElement | null) => {
    setListContentMetadata(prev => {
      prev.triggerRef.current = e;
      return prev;
    });
  }, []);

  const toggle = useCallback(() => {
    setListContentMetadata(prev => {
      return {
        ...prev,
        open: !prev.open,
      };
    });
  }, []);

  const updaters = useMemo(() => ({
    toggle,
    setElementRef: quietSetElementRef,
    setTriggerRef: quietSetTriggerElementRef,
  }), [toggle, quietSetElementRef, quietSetTriggerElementRef]);

  return (
    <StatesContext.Provider value={listContentMetadata}>
      <StatesMutateContext.Provider value={updaters}>
        {children}
      </StatesMutateContext.Provider>
    </StatesContext.Provider>
  );
}

export function useListContent(): ListContentMetadata {
  return useEnsuredContext(StatesContext);
}

export function useListContentUpdaters(): ListContentUpdaters {
  return useEnsuredContext(StatesMutateContext);
}
