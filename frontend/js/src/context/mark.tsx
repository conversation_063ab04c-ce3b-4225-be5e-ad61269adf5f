// Provider for markdown formatter
/** @jsx h */

import {createContext, h} from 'preact';
import {useContext, useEffect, useState} from 'preact/hooks';

import {plugin as markedPlugin} from 'markdown-it-marked';

import type MarkdownIt from 'markdown-it';
import type {ComponentChildren, RNode} from '../types';
import {plainTextPlugin} from '../functions/plainTextPlugin';

type OnlyText = {children: ComponentChildren; dangerouslySetInnerHTML?: never};
type OnlyHtml = {children?: never; dangerouslySetInnerHTML: {__html: string}};
export type MarkdownContent = OnlyText | OnlyHtml;

export interface Renderer {
  inline: (text: string, env?: unknown) => MarkdownContent;
  render: (text: string, env?: unknown) => MarkdownContent;
  plaintext: (text: string) => string;
}

const MarkdownContext = createContext<Renderer | undefined>(undefined);

const passthroughRenderer = {
  inline: (s: string) => ({children: s}),
  render: (s: string) => ({children: <p style={{whiteSpace: 'pre-line'}}>{s}</p>}),
  plaintext: (_: string) => '',
};



// Bad and wrong detection of relative urls (can do better)
function isRelative(url: string): boolean {
  return /^[/#]/.test(url);
}

function makeRenderer(markdownIt: typeof MarkdownIt): Renderer {
  const markBlock = markdownIt({breaks: true, html: true});
  // Override target attr in markdown, per documentation:
  // <https://github.com/markdown-it/markdown-it/blob/master/docs/architecture.md>
  const defaultRender = markBlock.renderer.rules.link_open || function _defaultRender(tokens, idx, options, env, self) {
    return self.renderToken(tokens, idx, options);
  };
  markBlock.renderer.rules.link_open = function _linkTargetBlank(tokens, idx, options, env, self) {
    const token = tokens[idx];
    const href = token.attrs?.[token.attrIndex('href')]?.[1];
    if (href && !isRelative(href)) {
      // Demanding rel="noopener" is probably redundant now browsers are more sensible
      // but some markup checking tools will complain on bare target="_blank" links.
      token.attrPush(['target', '_blank']);
    }
    return defaultRender(tokens, idx, options, env, self);
  };
  markBlock.use(markedPlugin);
  const markInline = markdownIt({});
  markInline.disable(['entity', 'image', 'link']).use(markedPlugin);
  const markPlainText = markdownIt().use(plainTextPlugin);
  return {
    inline: (s, e) => ({dangerouslySetInnerHTML: {__html: markInline.renderInline(s, e)}}),
    render: (s, e) => ({dangerouslySetInnerHTML: {__html: markBlock.render(s, e)}}),
    plaintext: (text) => {
      markPlainText.render(text);
      // @ts-expect-error see https://github.com/wavesheep/markdown-it-plain-text?tab=readme-ov-file#typescript
      return markPlainText.plainText;
    },
  };
}

export function MarkdownProvider({children}: {children: ComponentChildren}): RNode {
  const [renderer, setRenderer] = useState<Renderer>(passthroughRenderer);
  useEffect(() => {
    import(/* webpackChunkName: "md" */'markdown-it')
      .then(({default: markdownIt}) => {
        setRenderer(makeRenderer(markdownIt));
      });
  }, []);
  return <MarkdownContext.Provider value={renderer}>
    {children}
  </MarkdownContext.Provider>;
}

export function useMarkdown(): Renderer {
  const renderer = useContext(MarkdownContext);
  if (renderer == null) {
    throw new Error('useMarkdown must be used within MarkdownProvider');
  }
  return renderer;
}
