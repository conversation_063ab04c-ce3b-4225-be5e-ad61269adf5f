// Provider for browser location to state mapping context
/** @jsx h */

import {createContext, h} from 'preact';
import {useMemo, useState} from 'preact/hooks';

import useEnsuredContext from '../hooks/useEnsuredContext';

import type {ComponentChildren, RNode, StateUpdater} from '../types';

const HISTORY_MAX_LENGTH = 5;
const INFINITE = true;

export type HistoryStack = HistoryStackItem[];
export type HistoryStackItem = {
  iri: string;
  view: string|null;
};

interface HistoryQuery {
  describeRecentHistory(): HistoryStack;
  hasHistory: boolean;
}

interface HistoryUpdate {
  clearHistory(): void;
  pushSelection(selectionIri: string, viewIri: string|null): void;
}

interface HistoryNavigation {
  head: number;
  moveBackwards(): void;
  moveForwards(): void;
}

const HistoryStateContext = createContext<{stack: HistoryStack; head: number}|null>(null);
const HistoryMutateContext = createContext<{replaceStack: StateUpdater<HistoryStack>; setHead: StateUpdater<number>} | null>(null);

export function HistoryProvider({children}: {children: ComponentChildren}): RNode {
  const [stack, replaceStack] = useState<HistoryStack>([]);
  const [head, setHead] = useState<number>(-1);

  return (
    <HistoryStateContext.Provider value={{stack, head}}>
      <HistoryMutateContext.Provider value={{replaceStack, setHead}}>
        {children}
      </HistoryMutateContext.Provider>
    </HistoryStateContext.Provider>
  );
}

export function useHistoryQuery(): HistoryQuery {
  const {stack} = useEnsuredContext(HistoryStateContext);

  return useMemo(() => ({
    describeRecentHistory: () => {
      return [...stack];
    },
    hasHistory: !!(stack && stack.length),
  }), [stack]);
}

function getStack(iri: string, view: string|null, head: number, stack: HistoryStack, setHead: (index: number) => void): HistoryStack {
  if (head > -1 && stack[head].iri === iri && stack[head].view === view) {
    return stack;
  }

  if (head > -1 && head < stack.length - 1) {
    setHead(head + 1);
    return [...stack.slice(0, head + 1), {iri, view}];
  }

  const next = INFINITE || stack.length !== HISTORY_MAX_LENGTH
    ? [...stack, {iri, view}]
    : [...stack.slice(1, stack.length), {iri, view}];
  setHead(next.length - 1);
  return next;
}

export function useHistoryUpdate(): HistoryUpdate {
  const {head} = useEnsuredContext(HistoryStateContext);
  const {replaceStack, setHead} = useEnsuredContext(HistoryMutateContext);

  return useMemo(() => ({
    clearHistory: () => {
      replaceStack([]);
      setHead(-1);
    },
    pushSelection: (iri, view) => {
      replaceStack((prev) => getStack(iri, view, head, prev, setHead));
    },
  }), [head, replaceStack, setHead]);
}

export function useHistoryNavigation(): HistoryNavigation {
  const {head, stack} = useEnsuredContext(HistoryStateContext);
  const {setHead} = useEnsuredContext(HistoryMutateContext);

  return useMemo(() => ({
    head,
    moveBackwards: () => {
      setHead(prev => prev > 0 ? prev - 1 : prev);
    },
    moveForwards: () => {
      setHead(prev => prev < stack.length - 1 ? prev + 1 : prev);
    },
  }), [head, setHead, stack.length]);
}
