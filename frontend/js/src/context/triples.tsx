// Provider for model triples 'database'

import {createContext} from 'preact';

import {TripleStore} from '../triplestore';
import {MapModel} from '../rdfmodels';
import {reshapeModel} from '../reshape';
import useEnsuredContext from '../hooks/useEnsuredContext';

import type {MapData, Term} from '../types';
import {IRIS} from '../rdf';

export function getMapLanguages(mapData: MapData) : string[] {
  const langs = mapData.terms.find(x => x.subj === mapData.iri && x.pred === IRIS.VM.supportedLanguages);
  return langs
    ? JSON.parse(langs.obj as string)
    : [];
}

function removeLangTag(s: string): unknown {
  try {
    return JSON.parse(s.slice(0, -3));
  } catch (_e) {
    return s.slice(1, -4);
  }
}

export function modelFromData(mapData: MapData, lang: string, forceReshape: boolean = false): MapModel {
  const terms = mapData.terms;

  const model = new MapModel(new TripleStore(), mapData.iri);
  // Inserting data after as the store is not shared and doesn't need cloning
  const otherLang = new RegExp(`[}"]@(?:(${lang})|[a-z]{2})$`);
  const t = terms.reduce<Term[]>((acc, t) => {
    const obj = t.obj as string;
    const match = otherLang.exec(obj);
    if (!match) {
      // No language tag
      acc.push(t);
    } else if (match[1]) {
      // Tag that matches current language
      acc.push({subj: t.subj, pred: t.pred, obj: removeLangTag(obj)});
    }
    return acc;
  }, []);

  model._store.putManyRawTriples(t);

  reshapeModel(model, mapData, forceReshape);
  return model;
}

export const ModelContext = createContext<MapModel | null>(null);

export function useModel(): MapModel {
  return useEnsuredContext(ModelContext);
}
