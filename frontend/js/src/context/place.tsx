// Context hooks related to PlaceProvider

import {useCallback, useContext, useEffect, useMemo} from 'preact/hooks';

import {inclusionsEqual, uInclusionsEqual} from '../functions/keyed';
import useEnsuredContext from '../hooks/useEnsuredContext';
import {IRIS, PREFIXES} from '../rdf';
import {useDomains} from './domains';
import {
  PlaceBaseContext,
  PlaceNavContext,
  PlaceUrlContext,
  fromQualified,
  selectionFromHash,
  toQualified,
  usePlaceBase,
  viewFromPath,
} from './placebase';
import {useHistoryUpdate} from './selectionhistory';
import {useModel} from './triples';

import type {Url} from '../hooks/useUrl';
import type {Filters, Grouping, Hint, Inclusion, Keyed, Plottability} from '../types';

const DOMAIN_PARAM_PREFIX = 'domain';

interface Navigation {
  clearHint(): void;
  hrefForSelection(view: string|null): string;
  hrefForView(view: string|null): string;
  hrefForFilters(filtersString: string): string;
  setFilters(filters: Filters, hint?: Hint, forcePush?: boolean): void;
  setSelection(selected: string|null, hint?: Hint, skipHistory?: boolean): void;
  setView(view: string|null, hint?: Hint): void;
  setViewAndSelection(view: string|null, selected: string|null, hint?: Hint, skipHistory?: boolean): void;
  setViewSelectionAndFilters(view: string|null, selected: string|null, filters: Filters, hint?: Hint, skipHistory?: boolean): void;
  setRelativeUrl(url: string, hint?: Hint) : void;
}

export type Place = {
  filters: Filters;
  grouping: Grouping;
  domains: string[];
  // Unlike view, lenslocation is used to fetch lens related properties, including those requiring domain awareness.
  lensLocation: string|null;
  hint: Hint;
} & PlaceBase;

export type PlaceBase = {
  view: string | null;
  selection: string | null;
};

export function PlaceInitialLoad(): null {
  const nav = useEnsuredContext(PlaceNavContext)[0];
  const url = useEnsuredContext(PlaceUrlContext)[0];
  const {pushSelection} = useHistoryUpdate();
  const aliasForView = useAliasedViews();
  const getDefaultFilters = useDefaultFilters();
  const isOfType = useTypeChecker();
  const domains = useDomains();

  useEffect(() => {
    const view = aliasForView(viewFromPath(url.path));
    const selection = selectionFromHash(url.hash);
    let search = url.search;
    if (selection) {
      pushSelection(selection, null);
    }
    if (view) {
      const defaults = getDefaultFilters(view);
      const filters = filtersFromSearch(url.search, defaults);
      domains.forEach(({iri, selection, values}) => {
        if (selection === 'single' && !filters.domain?.[iri]) {
          ((filters.domain ??= {})[iri] ??= {})[values[0]] = true;
        }
      });
      const isViewMap = isOfType(view, IRIS.VM.Map);
      search = filtersToSearch(filters, filters, defaults, isViewMap);
    }

    nav(() => ({
      search,
      hash: hrefFromIri(selection),
      path: hrefFromIri(view),
      replace: true,
    }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // TODO: Somewhere need to have logic for 'url changed underneath us' from navigation
  // that was not triggered by calling nav() which should setHint('ext') - this may need
  // larger refactor to make into sanity.

  return null;
}

export function useNavigation() : Navigation {
  const base = useContext(PlaceBaseContext);
  const [nav, setHint] = useEnsuredContext(PlaceNavContext);
  const aliasForView = useAliasedViews();
  const defaultFiltersForView = useDefaultFilters();
  const isOfType = useTypeChecker();
  const {pushSelection} = useHistoryUpdate();

  return useMemo(() => ({
    hrefForSelection(iri) {
      return '#' + hrefFromIri(iri);
    },
    hrefForView(iri) {
      return base + hrefFromIri(iri);
    },
    hrefForFilters(filtersString) {
      return filtersString ? `?${filtersString }` : '';
    },
    clearHint() {
      setHint({name: 'ext'});
    },
    setFilters(filters, hint = {name: 'filter'}, forcePush = false) {
      setHint(hint);
      // TODO: keep hash but clear later if not present in new search set?
      // TODO: replace state if it's one 'current' form value that's changed?
      nav(url => {
        const view = viewFromPath(url.path);
        const isViewMap = isOfType(view, IRIS.VM.Map);
        const defaults = defaultFiltersForView(view);
        const oldGrouping = filtersFromSearch(url.search, defaults);
        return {path: url.path, search: filtersToSearch(filters, oldGrouping, defaults, isViewMap), hash: url.hash, replace: !forcePush && !!url.search};
      });
    },
    setSelection(iri, hint = {name: 'ext'}, skipHistory = false) {
      setHint(hint);
      nav(prev => {
        const hash = hrefFromIri(iri);
        if (!skipHistory && iri && hash !== prev.hash) {
          pushSelection(iri, null);
        }
        return {...prev, hash, replace: true};
      });
    },
    setView(iri, hint = {name: 'ext'}) {
      setHint(hint);
      const path = hrefFromIri(aliasForView(iri));
      nav(prev => {
        return {path, search: buildSearch(isOfType, defaultFiltersForView, prev, {path}, hint), hash: '', replace: false};
      });
    },
    setViewAndSelection(view, selected, hint = {name: 'ext'}, skipHistory = false) {
      setHint(hint);
      const path = hrefFromIri(aliasForView(view));
      nav(prev => {
        const hash = hrefFromIri(selected);
        if (!skipHistory && selected && hash !== prev.hash) {
          pushSelection(selected, null);
        }
        // If the path (view) has not changed, preserve current search
        if (path === prev.path) {
          return {...prev, hash, replace: true};
        }
        // Otherwise replace non-global filters with the defaults for new view
        return {path, search: buildSearch(isOfType, defaultFiltersForView, prev, {path}), hash, replace: false};
      });
    },
    setViewSelectionAndFilters(view, selected, filters, hint = {name: 'ext'}, skipHistory = false) {
      setHint(hint);
      const path = hrefFromIri(aliasForView(view));
      nav(prev => {
        const hash = hrefFromIri(selected);
        const isViewMap = isOfType(view, IRIS.VM.Map);
        const defaults = defaultFiltersForView(view);
        const oldGrouping = filtersFromSearch(prev.search, defaults);
        if (!skipHistory && selected && hash !== prev.hash) {
          pushSelection(selected, null);
        }
        // Same as setFilters but new hash
        if (path === prev.path) {
          return {path, search: filtersToSearch(filters, oldGrouping, defaults, isViewMap), hash, replace: !!prev.search};
        }
        // Build with new filters
        const newSearch = filtersToSearch(filters, oldGrouping, defaults, isViewMap);
        const newUrl = {path, search: newSearch};
        return {path, search: buildSearch(isOfType, defaultFiltersForView, prev, newUrl), hash, replace: false};
      });
    },
    // Assumes that url starts with the base url, e.g. /maps/<map>/<path>
    setRelativeUrl(url, hint = {name: 'ext'}) {
      setHint(hint);
      const full = new URL(url, location as unknown as URL);
      if (!full.pathname.startsWith(base)) {
        // TODO: Shouldn't really get here, but let's just navigate away if we do.
        window.location.href = full.href;
        return;
      }
      const path = full.pathname.slice(base.length);
      const search = full.search.slice(1);
      const hash = full.hash.slice(1);
      nav(prev => {
        return {path, search: buildSearch(isOfType, defaultFiltersForView, prev, {path, search}), hash, replace: false};
      });
    },
  }), [aliasForView, base, defaultFiltersForView, nav, pushSelection, setHint, isOfType]);
}

function buildSearch(isOfType: ReturnType<typeof useTypeChecker>, defaultFiltersForView: ReturnType<typeof useDefaultFilters>, prev: Url, next: Partial<Url>, hint = {name: 'ext'}): string {
  const nextView = viewFromPath(next.path || '');
  const isNextMap = isOfType(nextView, IRIS.VM.Map);

  // preserve filters when next view is map and the operation is not a view reset
  const preserveFilters = isNextMap && hint['name'] !== 'reset';

  const prevDefaults = defaultFiltersForView(viewFromPath(prev.path || ''));
  const prevSearch = preserveFilters
    ? filtersFromSearch(prev.search || '', prevDefaults)
    : {domain: filtersFromSearch(prev.search, prevDefaults).domain};

  const nextDefaults = defaultFiltersForView(nextView);
  const nextSearch = filtersFromSearch(next.search || '', preserveFilters ? {} : nextDefaults);
  return filtersToSearch({...prevSearch, ...nextSearch}, nextDefaults, nextDefaults, isNextMap);
}

function stringToIriInclusion(value: string): Inclusion {
  if (value === '') {
    return {};
  }
  const qnames = value.split(' ');
  return qnames.reduce<Inclusion>((arr, qname) => {
    let v = true;
    if (qname.startsWith('!')) {
      qname = qname.slice(1);
      v = false;
    }
    // TODO: how are we handling invalid inputs?
    arr[fromQualified(qname)] = v;
    return arr;
  }, {});
}

function iriInclusionAsString(group: undefined|Inclusion): string {
  if (group) {
    const keys = Object.keys(group);
    keys.sort((a, b) => a.localeCompare(b));
    return keys.map(iri => {
      const qname = toQualified(iri);
      if (group[iri] === false) {
        return '!' + qname;
      }
      return qname;
    }).join(' ');
  }
  return '';
}

function plottabilityAsString(value: Plottability|undefined): string {
  if (value) {
    return value;
  }
  return 'any';
}

function uFilterInclusionsEqual<T>(a: Keyed<T>|undefined, b: Keyed<T>|undefined): boolean {
  if (!a || Object.keys(a).length === 0) {
    return !b || Object.keys(b).length === 0;
  }
  return !!b && inclusionsEqual(a, b);
}

function uFilterValuesEqual(a: Plottability|string|undefined, b: Plottability|string|undefined): boolean {
  return !a ? !b : !!b && a === b;
}

function filtersEqual(a: Filters, b: Filters): boolean {
  return uFilterInclusionsEqual(a.aType, b.aType)
    && uFilterInclusionsEqual(a.itemKinds, b.itemKinds)
    && uFilterValuesEqual(a.plottable, b.plottable)
    && uFilterInclusionsEqual(a.domain, b.domain)
    && uFilterInclusionsEqual(a.relation, b.relation)
    && uFilterValuesEqual(a.search, b.search);
}

function groupingsEqual(a: Grouping, b: Grouping): boolean {
  return uFilterValuesEqual(a.method, b.method)
    && uFilterValuesEqual(a.sort, b.sort);
}

export function filtersGroupingsEqual(a: Filters & Grouping, b: Filters & Grouping): boolean {
  return filtersEqual(a, b) && groupingsEqual(a, b);
}

// Converts filters and grouping to URL params, hiding the default filters
function filtersToSearch(filters: Filters, grouping: Grouping, defaults: Filters & Grouping, forMap: boolean): string {
  const params = new URLSearchParams();
  // always display plottable for maps
  if (forMap || filters.plottable !== defaults.plottable) {
    params.append('plottable', plottabilityAsString(filters.plottable));
  }
  if (filters.search) {
    params.append('s', filters.search);
  }
  if (!uInclusionsEqual(filters.aType, defaults.aType)) {
    params.append('a', iriInclusionAsString(filters.aType));
  }
  if (!uInclusionsEqual(filters.itemKinds, defaults.itemKinds)) {
    params.append('kind', iriInclusionAsString(filters.itemKinds));
  }
  if (!uInclusionsEqual(filters.relation, defaults.relation)) {
    params.append('rel', iriInclusionAsString(filters.relation));
  }
  if (!uInclusionsEqual(filters.domain, defaults.domain)) {
    Object.entries(filters.domain || {}).forEach(([key, value]) => {
      params.append(`${DOMAIN_PARAM_PREFIX}-` + toQualified(key), iriInclusionAsString(value));
    });
  }
  if (grouping.method !== defaults.method) {
    params.append('group', grouping.method || '');
  }
  return params.toString();
}

function domainKeys(params: URLSearchParams): string[] {
  return [...params.keys()].reduce<string[]>((keys, key) => {
    const pattern = new RegExp(`^${DOMAIN_PARAM_PREFIX}-(.+)`);
    const m = key.match(pattern);
    if (m && m[1]) {
      keys.push(m[1]);
    }
    return keys;
  }, []);
}

function filtersFromSearch(search: string, initial: Filters & Grouping): Filters & Grouping {
  const filters = {...initial};
  // One level for domain would be fine, as we replace its keys,
  // but a fast deepcopy would be preferable in the long run.
  if (initial.domain) {
    filters.domain = {...initial.domain};
  }
  const params = new URLSearchParams(search);
  switch (params.get('plottable')) {
    case 'direct':
      filters.plottable = 'direct';
      break;
    case 'related':
    case 't':
      filters.plottable = 'related';
      break;
    case 'detached':
    case 'f':
      filters.plottable = 'detached';
      break;
    case 'any':
    case 'm':
      delete filters.plottable;
      break;
  }
  const a = params.get('a');
  if (a != null) {
    // TODO: Limit to just types present in the model or reset?
    filters.aType = stringToIriInclusion(a);
  }
  const kind = params.get('kind');
  if (kind != null) {
    // TODO: Also limit to just kinds present in the model or reset?
    filters.itemKinds = stringToIriInclusion(kind);
  }
  const rel = params.get('rel');
  if (rel != null) {
    filters.relation = stringToIriInclusion(rel);
  }
  domainKeys(params).forEach(key => {
    const value = params.get(`${DOMAIN_PARAM_PREFIX}-` + key);
    if (value) {
      filters.domain ??= {};
      filters.domain[fromQualified(key)] = stringToIriInclusion(value);
    }
  });
  const s = params.get('s');
  if (s) {
    filters.search = s;
  }
  const group = params.get('group');
  if (group) {
    filters.method = group;
  }
  const sort = params.get('sort');
  if (sort) {
    filters.sort = sort;
  }
  return filters;
}

function domainsFromFilters(filters: Filters) : string[] {
  const appliedDomains: string[] = [];
  if (filters && filters.domain !== undefined) {
    // trying to use reduce here produced some weird behaviour
    Object.values(filters.domain).forEach(subdict => {
      Object.keys(subdict).forEach(domain => {
        if (subdict[domain]) {
          appliedDomains.push(domain);
        }
      });
    });
  }
  return appliedDomains.sort();
}

export function usePlace(): Place {
  const [url, hint] = useEnsuredContext(PlaceUrlContext);
  const defaultFiltersForView = useDefaultFilters();
  const {view, selection} = usePlaceBase();

  const {filters, grouping, domains} = useMemo(() => {
    const {method, sort, ...filters} = filtersFromSearch(url.search, defaultFiltersForView(view));
    const grouping = {method, sort};
    const domains = domainsFromFilters(filters);
    return {filters, grouping, domains};
  }, [url.search, defaultFiltersForView, view]);

  const lensLocation = [view].concat(domains).join(',') || null;

  return {
    filters,
    grouping,
    domains,
    lensLocation,
    selection,
    view,
    hint,
  };
}

export function useGetSelection(): () => string|null {
  return useCallback(() => selectionFromHash(location.hash.slice(1)), []);
}

export function useFiltersToString(filters: Filters, newView: string|null): string {
  const [url, _] = useEnsuredContext(PlaceUrlContext);
  const defaultFiltersForView = useDefaultFilters();
  const isOfType = useTypeChecker();
  const aliasForView = useAliasedViews();

  return useMemo(() => {
    const isViewMap = isOfType(newView, IRIS.VM.Map);
    const defaults = defaultFiltersForView(newView);
    const path = hrefFromIri(aliasForView(newView));
    const grouping = filtersFromSearch(url.search, defaults);
    const search = filtersToSearch(filters, grouping, defaults, isViewMap);
    const newUrl = {path, search};
    return buildSearch(isOfType, defaultFiltersForView, url, newUrl);
  }, [aliasForView, defaultFiltersForView, filters, isOfType, url, newView]);
}

export function useMatchingView(name: string): [boolean, string] {
  const {view} = usePlace();
  const expected = PREFIXES.vm + name;
  return [view === expected, expected];
}

export function useViewUrlMatcher(): (url: string) => boolean {
  const mm = useModel();
  const {view} = usePlace();
  const base = useContext(PlaceBaseContext);

  return useCallback((url: string) => {
    // might contain params or hash
    const urlObj = new URL(url, location as unknown as URL);
    if (!urlObj.pathname.startsWith(base)) {
      return false;
    }
    const name = urlObj.pathname.slice(base.length);
    const other = PREFIXES.vm + name;

    const otherOrigin = other && (mm.ofStoryOf(other) as null|string) || other || null;
    const viewOrigin = view && (mm.ofStoryOf(view) as null|string) || view || null; // relatively cheap
    return viewOrigin === otherOrigin;
  }, [base, mm, view]);
}

let getDefaults: null|((iri: string|null) => Filters & Grouping) = null;

export function useDefaultFilters(): (iri: string|null) => Filters & Grouping {
  const mm = useModel();
  const isOfType = useTypeChecker();
  if (!getDefaults) {
    // TODO: There's nothing particularly special about these types, but
    //       they get their own views in the EoM model. Need to derive a
    //       more general rule for plottable so they can be omitted here.
    const knownClasses = mm._classChains();
    const aType = [IRIS.VM.ContentItem, IRIS.VM.Expression].reduce<Inclusion>((acc, classIri) => {
      if (classIri in knownClasses) {
        acc[classIri] = false;
      }
      return acc;
    }, {});

    const defaults: Filters & Grouping = {
      aType,
      plottable: 'related',
    };
    const filtersForView: {[iri: string]: Filters} = {};
    [...mm.maps(), ...mm.views(), ...mm.storypoints()].forEach((iri: string) => {
      const query = mm.filtersForView(iri);
      if (query) {
        filtersForView[iri] = filtersFromSearch(query, {});
      }
    });
    getDefaults = iri => iri && filtersForView[iri]
      || isOfType(iri, IRIS.VM.Map) && {plottable: 'direct'}
      || defaults;
  }
  return getDefaults;
}

let getAliasedViews: null|((iri: string|null) => string|null) = null;

export function useAliasedViews(): (iri: string|null) => string|null {
  const mm = useModel();
  if (!getAliasedViews) {
    const defaultView = mm.defaultView();
    getAliasedViews = iri => iri == null ? defaultView : iri;
  }
  return getAliasedViews;
}

type FiltersRestore = {
  isResettable: boolean;
  reset: () => void;
};
// Resets all Filters except domain.
export function useFiltersPartialReset(): FiltersRestore {
  const {view, filters, grouping} = usePlace();
  const {setFilters} = useNavigation();
  const getDefaultFilters = useDefaultFilters();

  const isResettable = useMemo(() => {
    const defaults = getDefaultFilters(view);
    const {domain: _d, ...resettableDefaults} = defaults;
    const {domain: _f, ...resettableFilterGrouping} = {...filters, ...grouping};

    return !filtersGroupingsEqual(resettableDefaults, resettableFilterGrouping);
  }, [getDefaultFilters, grouping, filters, view]);

  const reset = useCallback(() => {
    const {domain: _d, ...resettableDefaults} = getDefaultFilters(view);
    const domain = filters.domain;
    setFilters({...resettableDefaults, ...(domain ? {domain} : {})});
  }, [getDefaultFilters, setFilters, filters, view]);

  return {
    isResettable,
    reset,
  };
}

function useTypeChecker(): (iri: string|null, type: string) => boolean {
  const mm = useModel();
  return (iri, type) => !!iri && mm.classOf(iri) === type;
}

export function hrefFromIri(iri: string|null): string {
  return iri == null ? '' : toQualified(iri);
}

export function isGlobalSearch(filters: Filters): boolean {
  return filtersEqual(filters, {search: filters.search});
}
