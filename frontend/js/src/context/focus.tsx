// Provider for focus points on map
/** @jsx h */
import {createContext, h} from 'preact';
import {useCallback, useMemo, useState} from 'preact/hooks';

import useEnsuredContext from '../hooks/useEnsuredContext';

import type {ComponentChildren, RNode, StateUpdater} from '../types';

export type Highlight = {
  mapIris: string[];
};

const HighlightContext = createContext<Highlight | null>(null);
const HighlightUpdaterContext = createContext<StateUpdater<Highlight>| null>(null);

export function FocusProvider({children}: {children: ComponentChildren}):RNode {
  const [highlight, setHighlight] = useState<Highlight>({mapIris: []});

  return (
    <HighlightContext.Provider value={highlight}>
      <HighlightUpdaterContext.Provider value={setHighlight}>
        {children}
      </HighlightUpdaterContext.Provider>
    </HighlightContext.Provider>
  );
}

export function useHighlightedItems(): {mapIris: string[]} {
  const highlight = useEnsuredContext(HighlightContext);

  return useMemo(() => {
    return {
      mapIris: highlight.mapIris,
    };
  }, [highlight.mapIris]);
}

export function useHighlighter(): {setMapHighlights: (value: string[]) => void; resetMapHighlights: () => void} {
  const setHighlight = useEnsuredContext(HighlightUpdaterContext);

  const setMapHighlights = useCallback((value: string[]) => {
    setHighlight(highlight => JSON.stringify(highlight.mapIris) === JSON.stringify(value) ? highlight : {highlight, mapIris: value});
  }, [setHighlight]);

  return useMemo(() => {
    return {
      setMapHighlights,
      resetMapHighlights: () => setMapHighlights([]),
    };
  }, [setMapHighlights]);
}
