// Provider for map assets

import {createContext} from 'preact';

import {IRIS} from '../rdf';

import type {MapData} from '../types';

const oldDefaultPath = 'https://opatlas-live.s3.amazonaws.com/petcare-pl/181023_v3/assets/';

export interface Assets {
  fromFilename: (filename: string | null) => (string | null);
  BRIGHT_ICON: {[type: string]: string};
  CONTENTTYPE_ICON: {[type: string]: string};
  CAPTYPE_ICON: {[type: string]: string};
  TICK_ICON: (string | null)[];
}

function assetsFromBasePath(basePath: string): Assets {
  return {
    fromFilename(filename) {
      if (filename == null || filename.startsWith('https:')) {
        // TODO: Instead return a default icon?
        return filename;
      }
      return new URL(filename, basePath).href;
    },
    CONTENTTYPE_ICON: {
      [IRIS.VM.CONTENTTYPE.image]: basePath + 'type_image_40.png',
      [IRIS.VM.CONTENTTYPE.video]: basePath + 'type_movingimage_40.png',
    },
    BRIGHT_ICON: {
      [IRIS.VM.CAPTYPE.social]: basePath + 'bright_social_icon_40.png',
      [IRIS.VM.CAPTYPE.sharedFin]: basePath + 'bright_shared_financial_icon_40.png',
      [IRIS.VM.CAPTYPE.human]: basePath + 'bright_human_icon_40.png',
      [IRIS.VM.CAPTYPE.environ]: basePath + 'bright_environmental_icon_40.png',
      'unspecified': basePath + 'bright_unspecified_icon_40.png',
    },
    CAPTYPE_ICON: {
      [IRIS.VM.CAPTYPE.social]: basePath + 'social_icon_40.png',
      [IRIS.VM.CAPTYPE.sharedFin]: basePath + 'shared_financial_icon_40.png',
      [IRIS.VM.CAPTYPE.human]: basePath + 'human_icon_40.png',
      [IRIS.VM.CAPTYPE.environ]: basePath + 'environmental_icon_40.png',
    },
    TICK_ICON: [
      null,
      basePath + 'tick_40.png',
      basePath + 'tick2_40.png',
    ],
  };
}


export function assetsFromModelData(mapData: MapData | null): Assets {
  return assetsFromBasePath(
    mapData?.['tiles-src']?.replace(/(\/tiles)?\/[^/]+$/, '/assets/')
      ?? oldDefaultPath);
}

export const AssetsContext = createContext<Assets>({} as Assets);
