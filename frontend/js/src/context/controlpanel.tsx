// Provider for control panel
/** @jsx h */

import {createContext, h} from 'preact';
import {useCallback, useEffect, useState} from 'preact/hooks';

import useEnsuredContext from '../hooks/useEnsuredContext';

import {Panel, usePanelUpdaters} from './panels';
import {usePlace} from './place';

import type {ComponentChildren, RNode} from '../types';

export type SearchControl = {search: string; setSearch: (_: string) => void};
const SearchControlContext = createContext<SearchControl|null>(null);

type SearchControlProps = {
  children: ComponentChildren;
};
function SearchControlProvider({children}: SearchControlProps): RNode {
  const [search, setSearch] = useState('');

  const {changeOpen: changeRecentOpen} = usePanelUpdaters(Panel.RecentSearches);
  const {changeOpen: changeResultsOpen} = usePanelUpdaters(Panel.SearchResults);

  const setSearchExtended = useCallback((s: string) => {
    setSearch(prev => {
      if (prev === s) {
        return prev;
      }
      const empty = !s;
      changeResultsOpen(!empty);
      changeRecentOpen(empty);
      return s;
    });
  }, [changeRecentOpen, changeResultsOpen]);

  return (
    <SearchControlContext.Provider value={{search, setSearch: setSearchExtended}}>
      {children}
    </SearchControlContext.Provider>
  );
}

export type ControlPanelControl = 'filter'|'search'|'burger'|'none';
export type ControlPanelUpdate = {
  setControl: (state: ControlPanelControl, value: boolean) => void;
  toggleControl: (state: ControlPanelControl) => void; // TODO: combine with setControl
  resetControl: () => void;
};

function ControlPanelEffects(): null {
  const {resetControl} = useControlPanelUpdater();
  const {hint, selection} = usePlace();

  const hasSelection = !!selection;
  useEffect(() => {
    const selectionHint = () => hint.name === 'map'
      || hint.name === 'itemList'
      || hint.name === 'search';
    if (hasSelection && selectionHint()) {
      resetControl();
    }
  }, [hasSelection, hint, resetControl]);

  return null;
}

const ControlPanelContext = createContext<ControlPanelControl|null>(null);
const ControlPanelUpdateContext = createContext<ControlPanelUpdate|null>(null);

export function ControlPanelProvider(props: {children: ComponentChildren}): RNode {
  const [controlActive, setControlActive] = useState<ControlPanelControl>('none');

  const setControl = useCallback((control: ControlPanelControl, value: boolean) => {
    setControlActive(prev => value ? control : prev === control ? 'none' : prev);
  }, [setControlActive]);

  const toggleControl = useCallback((control: ControlPanelControl) => {
    setControlActive(prev => prev === 'none' || prev !== control ? control : 'none');
  }, [setControlActive]);

  const resetControl = useCallback(() => {
    setControlActive('none');
  }, [setControlActive]);

  return (
    <ControlPanelContext.Provider value={controlActive}>
      <ControlPanelUpdateContext.Provider value={{setControl, toggleControl, resetControl}}>
        <ControlPanelEffects />
        <SearchControlProvider>
          {props.children}
        </SearchControlProvider>
      </ControlPanelUpdateContext.Provider>
    </ControlPanelContext.Provider>
  );
}

export function useSearchControl(): SearchControl {
  return useEnsuredContext(SearchControlContext);
}

export function useControlPanelActiveControl(): ControlPanelControl {
  return useEnsuredContext(ControlPanelContext);
}

export function useControlPanelUpdater(): ControlPanelUpdate {
  return useEnsuredContext(ControlPanelUpdateContext);
}
