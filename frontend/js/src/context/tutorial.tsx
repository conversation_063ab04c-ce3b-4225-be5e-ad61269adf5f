/** @jsx h */
import {createContext, h} from 'preact';
import {useCallback, useEffect, useMemo, useState} from 'preact/hooks';

import useEnsuredContext from '../hooks/useEnsuredContext';
import {useTranslation} from '../intl';
import {usePlatformStates} from './platform';
import {useScreenProperties} from './screen';

import type {TutorialStep} from '../components/intro-tutorial';
import type {ComponentChildren, RNode} from '../types';

interface TutorialContextType {
  showTutorial: boolean;
  forceTutorial: boolean;
  validSteps: TutorialStep[];
  startTutorial: () => void;
  completeTutorial: () => void;
}

const TutorialContext = createContext<TutorialContextType | null>(null);

interface TutorialProviderProps {
  children: ComponentChildren;
}

/**
 * Provider component that manages the application's tutorial state and steps.
 *
 * This component:
 * - Defines and manages the tutorial steps
 * - Tracks which steps are valid based on element visibility
 * - <PERSON><PERSON> tutorial completion state
 * - Filters out elements with zero dimensions
 * - Extracts accessibility labels for screen readers
 * - Provides context values for tutorial state and actions
 *
 * @param {TutorialProviderProps} props - The component props
 * @param {ComponentChildren} props.children - Child components that will have access to the tutorial context
 * @returns {RNode} The provider component with its children
 */
export function TutorialProvider({children}: TutorialProviderProps): RNode {
  // State for tutorial visibility and control
  const [showTutorial, setShowTutorial] = useState(true);
  const [forceTutorial, setForceTutorial] = useState(false);
  const [validSteps, setValidSteps] = useState<TutorialStep[]>([]);
  const {isLargeScreen} = useScreenProperties();
  const intl = useTranslation();
  const {base: baseReady} = usePlatformStates();

  // Define tutorial steps
  const tutorialSteps:TutorialStep[] = useMemo(() => [
    {elementId: 'main-menu-button', title: 'Main Menu', description: intl.translate({defaultMessage: 'Use the main menu button to expand or collapse the options. Here you will find various viewing options or curated insights.'})},
    {elementId: 'home-button', title: 'Home', description: intl.translate({defaultMessage: 'Use the home button to return to home page with a list of all the mapping projects available to you.'})},
    {elementId: 'about-button', title: 'About', description: intl.translate({defaultMessage: 'Use the about button to view more information about this project or return to this tutorial.'})},
    {elementId: 'filter-toggles-container', title: 'Quick Filters', description: intl.translate({defaultMessage: 'Use the quick filter buttons to toggle on or off the depicted classes or categories from the filters. You can filter for many other types of things in the filter panel.'})},
    {elementId: 'comments-toggle-button', title: 'Comments', description: intl.translate({defaultMessage: 'Use the comments button to open the panel where you can add comments to a selected item and see what you’ve commented. You cannot see other users’ comments.'})},
    {elementId: 'map-selector-button', title: 'Map Switcher', description: intl.translate({defaultMessage: 'Use the map switcher to change to a different map, while keeping your current filter settings. See the world through another lens!'})},
    {elementId: 'selected-item-panel', title: 'Selected Item', description: intl.translate({defaultMessage: 'Use the selected item panel to get more information about the currently selected item.'}), pushUpMobile: 130},
    {elementId: 'search-input', title: 'Search', description: intl.translate({defaultMessage: 'Use the search bar to type in terms to find things.'})},
    {elementId: 'filter-panel-button', title: 'Filters', description: intl.translate({defaultMessage: 'Use the filter panel button to open and close filtering options. Filters allow you to change what you see on the map, on the list view, and in search results.'})},
    {elementId: 'reset-filters-button', title: 'Restore Filters', description: intl.translate({defaultMessage: 'Use the filter restore button to return to the default filters.'})},
    {elementId: 'list-view-toggle', title: 'List View', description: intl.translate({defaultMessage: 'Use the list view button to open or close a list of all items from the map model under the current filter settings.'})},
  ], [intl]);

  // Function to attach IDs to zoom controls
  const attachZoomControlIds = useCallback(() => {
    const zoomControls = document.querySelectorAll('.ol-zoom.custom.vm-float-block');
    if (zoomControls.length > 0) {
      // Attach ID to zoom in button
      const zoomInButton = zoomControls[0].querySelector('.ol-zoom-in');
      if (zoomInButton) {
        zoomInButton.id = 'zoom-in-control';
      }
      // Attach ID to zoom out button
      const zoomOutButton = zoomControls[0].querySelector('.ol-zoom-out');
      if (zoomOutButton) {
        zoomOutButton.id = 'zoom-out-control';
      }
    }
  }, []);

  // Add zoom control steps to tutorial steps
  const allTutorialSteps = useMemo(() => [
    ...tutorialSteps,
    ...(isLargeScreen ? [
      {elementId: 'zoom-in-control', title: 'Zoom In', description: intl.translate({defaultMessage: 'Use this button to zoom in on the map.'})},
      {elementId: 'zoom-out-control', title: 'Zoom Out', description: intl.translate({defaultMessage: 'Use this button to zoom out from the map.'})},
    ] : []),
  ], [tutorialSteps, isLargeScreen, intl]);

  // Function to update valid tutorial steps and extract aria labels
  const updateValidSteps = useCallback(() => {
    // The baseReady doesn't guarantee that skeleton disappears but it is a trigger.
    // Due to skeleton rendering happening before the tutorial context update,
    // the selector should be resolved correctly for real platform components.
    if (!baseReady) {
      return;
    }

    attachZoomControlIds();
    const valid: TutorialStep[] = allTutorialSteps.filter(step => {
      const element = document.getElementById(step.elementId);
      const rect = element?.getBoundingClientRect();

      // Filter out elements non existing elements or elements with zero width or height
      if (!element || !rect || rect.width === 0 || rect.height === 0) {
        return false;
      }

      // Extract aria labels for valid elements that will be announced by screenreader
      if (!step.ariaLabel) {
        // Get accessibility label in order of priority (stops at first found)
        const accessibilityLabel = element.getAttribute('aria-label')
                                || element.getAttribute('tooltipMessage')
                                || element.getAttribute('title')
                                || element.getAttribute('placeholder');

        if (accessibilityLabel) {
          step.ariaLabel = accessibilityLabel.trim();
        }
      }

      return true;
    });
    setValidSteps(valid);
  }, [allTutorialSteps, attachZoomControlIds, baseReady]);

  // Initial setup of valid steps
  useEffect(() => {
    updateValidSteps();
  }, [isLargeScreen, updateValidSteps]);

  // Action to start the tutorial
  const startTutorial = useCallback(() => {
    updateValidSteps();
    setShowTutorial(true);
    setForceTutorial(true);
  }, [updateValidSteps]);

  // Action to complete the tutorial
  const completeTutorial = useCallback(() => {
    setShowTutorial(false);
    setForceTutorial(false);
  }, []);


  const contextValue = useMemo(() => ({
    showTutorial,
    forceTutorial,
    validSteps,
    startTutorial,
    completeTutorial,
  }), [showTutorial, forceTutorial, validSteps, startTutorial, completeTutorial]);

  return (
    <TutorialContext.Provider value={contextValue}>
      {children}
    </TutorialContext.Provider>
  );
}

export const useTutorial = (): TutorialContextType => {
  return useEnsuredContext(TutorialContext);
};
