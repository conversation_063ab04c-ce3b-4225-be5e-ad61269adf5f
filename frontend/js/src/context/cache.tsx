// Provides a cache context for hooks
/** @jsx h */

import {Fragment, createContext, h} from 'preact';
import useEnsuredContext from '../hooks/useEnsuredContext';

import type {ComponentChildren} from 'preact';
import type {Context, RNode} from '../types';

type HookInfo<T> = {
  context: Context<T|null>;
  hook: () => T;
};

const hooks: HookInfo<unknown>[] = [];

export function registerCachedHook<T>(hook: () => T): () => T {
  const context = createContext<T|null>(null);
  hooks.push({
    context: context as Context<unknown|null>,
    hook,
  });
  return () => {
    return useEnsuredContext(context);
  };
}

type Props = {
  children: ComponentChildren;
};
export default function CachedHooksProvider({children}: Props): RNode {
  // TODO: Too much bullying of typescript to accept this reducing operation
  return hooks.reduce<RNode>((children, {context, hook}) => (
    h(context.Provider, {value: hook(), children}) as RNode
  ), h(Fragment, null, children) as RNode);
}
