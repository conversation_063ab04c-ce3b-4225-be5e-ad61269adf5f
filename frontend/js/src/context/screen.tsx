// Provides properties describing the device screen
/** @jsx h */

import {createContext, h} from 'preact';
import {useEffect, useMemo, useRef, useState} from 'preact/hooks';

import useEnsuredContext from '../hooks/useEnsuredContext';

import type {ComponentChildren, RNode} from '../types';

/**
 * Represents screen-related properties.
 *
 * @property {boolean} isLargeScreen - Indicates whether the screen is considered large.
 */
export type ScreenProperties = {
  isLargeScreen: boolean;
};

const ScreenPropertiesContext = createContext<ScreenProperties | null>(null);

type Props = {
  children: ComponentChildren;
};
/**
 * Provides screen size properties to its children via context.
 *
 * @param {ComponentChildren} props.children Children components.
 */
export function ScreenProvider({children}: Props): RNode {
  const largeScreenMql = useRef(window.matchMedia('(min-width: 56em)'));
  const [isLargeScreen, setIsLargeScreen] = useState(largeScreenMql.current.matches);

  useEffect(() => {
    const mql = largeScreenMql.current;
    const handler = () => {
      setIsLargeScreen(mql.matches);
    };
    mql.addEventListener('change', handler);

    return () => {
      mql.removeEventListener('change', handler);
    };
  }, []);

  const properties = useMemo(() => ({
    isLargeScreen,
  }), [isLargeScreen]);

  return (
    <ScreenPropertiesContext.Provider value={properties}>
      {children}
    </ScreenPropertiesContext.Provider>
  );
}

/**
 * Custom hook to access screen properties from context.
 *
 * @returns {ScreenProperties} The current screen properties.
 */
export function useScreenProperties(): ScreenProperties {
  return useEnsuredContext(ScreenPropertiesContext);
}
