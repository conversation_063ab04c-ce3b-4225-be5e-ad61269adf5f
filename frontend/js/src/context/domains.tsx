// Provider for domains
/** @jsx h */

import {createContext, h} from 'preact';
import {useMemo} from 'preact/hooks';

import useEnsuredContext from '../hooks/useEnsuredContext';
import {localeInsensitive} from '../sort';
import {useModel} from './triples';

import type {MapModel} from '../rdfmodels';
import type {ComponentChildren, RNode} from '../types';

export type Domain = {
  iri: string;
  selection: 'single' | 'multi';
  values: string[];
};

const DomainsContext = createContext<Domain[] | null>(null);

function byNameOf(mm: MapModel): (a: string, b: string) => number {
  return (iriA: string, iriB: string) => localeInsensitive(mm.nameOf(iriA), mm.nameOf(iriB));
}

export function DomainsProvider({children}: {children: ComponentChildren}):RNode {
  const mm = useModel();

  const domains = useMemo(() => {
    const byName = byNameOf(mm);
    const domains = mm.domains();
    return Object.keys(domains).map<Domain>(iri => {
      const values = [...(domains[iri].values || [])].sort(byName);
      return {
        iri,
        selection: mm.selectionModeOf(iri),
        values,
      };
    });
  }, [mm]);

  return (
    <DomainsContext.Provider value={domains}>
      {children}
    </DomainsContext.Provider>
  );
}

export function useDomains(): Domain[] {
  return useEnsuredContext(DomainsContext);
}
