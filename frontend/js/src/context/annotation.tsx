// Provider for user annotations on the map
/** @jsx h */

import {createContext, h} from 'preact';
import {useCallback, useEffect, useMemo, useReducer, useState} from 'preact/hooks';

import {useModel} from './triples';
import {useUser} from './user';
import useAnnotationApi from '../hooks/useAnnotationApi';
import useEnsuredContext from '../hooks/useEnsuredContext';
import usePrevious from '../hooks/usePrevious';

import type {ComponentChildren, RNode, StateUpdater} from '../types';
import type {MapModel} from '../rdfmodels';

export type VoteSettings = Readonly<{
  most: number;
  total: number;
}>;

export type ItemAnnotationSummary = Readonly<{
  comments: Comment[];
  votes: number;
}>;

export type Comment = {
  user: string;
  body: string;
  commentIri: string;
  created: Date;
  category?: string;
};

type SidebarExpandState = 'default' | 'expand';
type LoadState = 'initial'|'loaded';
interface Annotations {
  byItem: {[iri: string]: ItemAnnotationSummary};
  loadState: LoadState;
  mapIRI: string;
  settings: VoteSettings;
  loader: Promise<unknown>;
}

type Actions = (
  {type: 'load'} |
  {type: 'pending'; loader: Promise<unknown>} |
  {type: 'setComment'; iri: string; body: string; commentIri?: string; date?: Date; category?: string; skipDelete?: boolean} |
  {type: 'toggleVote'; iri: string});

type Dispatch = (action: Actions) => void;

type SidebarState = {
  expandState: SidebarExpandState;
  setExpandState: StateUpdater<SidebarExpandState>;
  activeItem: string|null;
  setActiveItem: StateUpdater<string|null>;
};

type Props = Readonly<{
  children: ComponentChildren;
  mapIRI: string;
  lang: string;
}>;

const AnnotationContext = createContext<Annotations | null>(null);
const AnnotationActContext = createContext<Dispatch | null>(null);
const AnnotationSidebarState = createContext<SidebarState | null>(null);

const defaultSettings: VoteSettings = {most: 2, total: 20};

function totalVotes(annotations: Annotations): number {
  return Object.values(annotations.byItem).reduce(
    (acc, anno) => acc + anno.votes || 0, 0);
}

function setVotes(mm: MapModel, creator: string, iri: string, votes: number): void {
  const timestamp = new Date();
  mm.updateVotes(iri, {creator, timestamp, votes});
}

export function AnnotationProvider(props: Props): RNode {
  const mm = useModel();
  const user = useUser();
  const {get} = useAnnotationApi(props.mapIRI);

  const previousMm = usePrevious(mm);
  useEffect(() => {
    if (previousMm) {
      const triples = previousMm.annotationTriples();
      mm._store.putMany(triples);
    }
  }, [mm._store, previousMm, props.lang]);

  const reducer = useCallback((state: Annotations, action: Actions): Annotations => {
    switch (action.type) {
      case 'load': {
        return {...state, byItem: mm.annotationDetails(), loadState: 'loaded'};
      }
      case 'toggleVote': {
        if (state.loadState === 'initial') {
          break;
        }

        const {[action.iri]: item, ...byItem} = state.byItem;
        const votes = totalVotes(state) >= state.settings.total ? 0 : (
          ((item?.votes || 0) + 1) % (state.settings.most + 1));
        setVotes(mm, user, action.iri, votes);
        if (votes) {
          byItem[action.iri] = {...item, votes};
        } else {
          byItem[action.iri] = {...item, votes: 0};
        }
        return {...state, byItem};
      }
      case 'setComment': {
        if (state.loadState === 'initial') {
          break;
        }

        mm.updateComment(action.iri, action.skipDelete, {
          creator: user,
          timestamp: action.date,
          commentIri: action.commentIri,
          body: action.body,
          category: action.category,
        });

        const {[action.iri]: _, ...byItem} = state.byItem;
        byItem[action.iri] = mm.annotationsFor(action.iri);

        return {...state, byItem};
      }
    }

    return state;
  }, [mm, user]);

  const [state, dispatch] = useReducer(reducer, 'initial', (loadState: LoadState) => ({
    byItem: {},
    loadState,
    mapIRI: props.mapIRI,
    settings: defaultSettings,
    loader: get(user).then(() => {
      dispatch({type: 'load'});
    }),
  }));

  const [expandState, setExpandState] = useState<SidebarExpandState>('default');
  const [activeItem, setActiveItem] = useState<string|null>(null);

  return (
    <AnnotationContext.Provider value={state}>
      <AnnotationActContext.Provider value={dispatch}>
        <AnnotationSidebarState.Provider value={{expandState, setExpandState, activeItem, setActiveItem}}>
          {props.children}
        </AnnotationSidebarState.Provider>
      </AnnotationActContext.Provider>
    </AnnotationContext.Provider>
  );
}

export function useVoting(): Dispatch {
  return useEnsuredContext(AnnotationActContext);
}

export function useVoteToggle() : (iri: string) => Promise<void> {
  const {loadState, mapIRI} = useEnsuredContext(AnnotationContext);
  const dispatch = useEnsuredContext(AnnotationActContext);
  const {put} = useAnnotationApi(mapIRI);
  const user = useUser();
  return useCallback(iri => {
    if (loadState === 'initial') {
      return Promise.resolve();
    }
    dispatch({type: 'toggleVote', iri});
    return put(user);
  }, [dispatch, loadState, put, user]);
}

export function useAnnotations(): Annotations {
  return useEnsuredContext(AnnotationContext);
}

export function useAnnotationSummary(iri: string): ItemAnnotationSummary {
  const annotations = useAnnotations();
  return annotations.byItem[iri];
}

export function useVoteCount(iri: string): number {
  const annotations = useAnnotations();
  return annotations.byItem[iri]?.votes || 0;
}

export function useVotesRemaining(): () => number {
  const annotations = useAnnotations();
  return useCallback(() => annotations.settings.total - totalVotes(annotations), [annotations]);
}

export function useGetComments(): (iri: string) => Comment[] {
  const {byItem} = useEnsuredContext(AnnotationContext);
  return useCallback((iri: string) => byItem[iri]?.comments || [], [byItem]);
}

export function useComments(): {[iri: string]: Comment[]} {
  const {byItem} = useEnsuredContext(AnnotationContext);
  return useMemo(() => Object.entries(byItem).reduce<{[iri: string]: Comment[]}>((acc, [iri, entry]) => {
    if (entry.comments?.length) {
      acc[iri] = entry.comments;
    }
    return acc;
  }, {}), [byItem]);
}

type Commenter = (
  targetIri: string,
  body: string,
  commentIri?: string,
  date?: Date,
  category?: string,
  skipDelete?: boolean) => Promise<void>;
export function useCommenter(): Commenter {
  const dispatch = useEnsuredContext(AnnotationActContext);
  const {loadState, mapIRI} = useEnsuredContext(AnnotationContext);
  const {put} = useAnnotationApi(mapIRI);
  const user = useUser();

  return useCallback((targetIri: string, body: string, commentIri?: string, date?: Date, category?: string, skipDelete?: boolean) => {
    if (loadState !== 'loaded') {
      return Promise.resolve();
    }

    dispatch({type: 'setComment', iri: targetIri, body, commentIri, date, category, skipDelete});
    return put(user);
  }, [dispatch, loadState, put, user]);
}

export function useAnnotationState(): LoadState {
  const {loadState} = useEnsuredContext(AnnotationContext);
  return loadState;
}

export function useSidebarState(): SidebarState {
  return useEnsuredContext(AnnotationSidebarState);
}

export type CommentCategory = Readonly<{
  name: string;
  iri: string;
  helpText: string;
  icon: string;
  altIcon: string;
  isMasked: boolean;
}>;
export function useCommentCategories(): CommentCategory[] {
  const mm = useModel();

  const categories = useMemo(() => mm.commentCategories(), [mm]);
  return categories;
}
