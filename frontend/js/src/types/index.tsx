// Common types, to split up when it gets too ponderous

import type * as preact from 'preact';
import type * as hooks from 'preact/hooks';

import type {Coord} from '../context/ol';

export type {Properties as CSSProperties} from 'csstype';

export type RCSSProperties = preact.h.JSX.CSSProperties;
export type ComponentChildren = preact.ComponentChildren;
export type Context<T> = preact.Context<T>;
export type Attrs = preact.JSX.IntrinsicElements;
export type AnchorAttrs = Attrs['a'];
export type SVGAttrs = Attrs['svg'];
export type RefCallback<T> = preact.RefCallback<T>;
export type RefObject<T> = preact.RefObject<T>;
export type ForwardRef<T> = preact.Ref<T>;
export type RNode<T = unknown> = preact.VNode<T>;
export type TargetedEvent<TTarget extends EventTarget, TEvent extends Event = Event> = preact.JSX.TargetedEvent<TTarget, TEvent>;
export type EventHandler<E extends preact.JSX.TargetedEvent> = preact.JSX.EventHandler<E>;
export type StateUpdater<S> = hooks.Dispatch<hooks.StateUpdater<S>>;

/**
 * Internal navigation hint for behaviours in response to location change.
 * Broadly speaking these are 'where the navigation action originates'.
 * So that a navigation from the map knows not to update the map, and from
 * the sidebar doesn't update the sidebar.
 *
 * Hints associated with selection:
 * - map - From map pane, so don't update map view area.
 * - sip - From Selected Item Panel.
 * - search - From Search Panel.
 * - itemList - From item list.
 *
 * Others:
 * - ext - (default) Other context, so should update all.
 * - load - First load of page, so update all but do not animate.
 * - filter - Filters change, should not pan.
 * - reset - Forced view reset, will update all and clear hint after.
 */
export type SelectionHintName = 'map' | 'sip' | 'search' |'itemList';
export type HintName = SelectionHintName | 'ext' | 'load' | 'filter' | 'reset';
export type Hint = {name: HintName};

export type Keyed<T> = {[iri: string]: T};

export type Existence = Keyed<true>;

export type Inclusion = Keyed<boolean>;

export type GroupedInclusion = Keyed<Inclusion>;

export type Item = {iri: string; name: string};

export type SearchEntry = {
  type: 'item'|'term';
  value: string;
  timestamp: string;
};

export type Grouping = {
  method?: string;
  sort?: string;
};

export type Plottability = 'direct' | 'related' | 'detached';

export type Filters = {
  itemKinds?: Inclusion;
  plottable?: Plottability;
  search?: string;
  aType?: Inclusion;
  relation?: Inclusion;
  domain?: GroupedInclusion;
};

export type Path = string|Coord[];
export type Poly = Path;
export type Paths = string|Path[];

export interface Term {
  subj: string;
  pred: string;
  obj: unknown;
}

export type Terms = Term[];

// From semsys.edhandlers.mapModel view
export interface MapInfo {
  'iri': string;
  'name': string;
  'tiles-src': string;
  'map-extra-css'?: string;
  'voting-enabled'?: string;
  'voting-splash'?: string;
  'welcome-splash'?: string;
}

export interface MapData extends MapInfo {
  'terms': Terms;
}

export interface Settings {
  'api-root': string;
  'me-url'?: string;
  'map-iri': string;
  'self': string;
  'placeholder-src'?: string;
  'expiry-date': string;
}
