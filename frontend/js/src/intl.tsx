// Hook and component for managing user interface language translations

/** @jsx h */

import {Fragment, h} from 'preact';
import {useMemo} from 'preact/hooks';
import {useIntl} from 'react-intl';

import type {FormatXMLElementFn, PrimitiveType} from 'intl-messageformat';
import type {ComponentChildren, RNode} from './types';

type SimpleMessageValues = {[key: string]: PrimitiveType};
type RichMessageValues = {[key: string]: PrimitiveType | RNode | FormatXMLElementFn<RNode, RNode>};
type MessageDescriptor = Readonly<{
  defaultMessage: string;
  id?: string;
  description?: string;
}>;

export function createBold(c: ComponentChildren): RNode {
  return <strong>{c}</strong>;
}
export function createEmphasis(c: ComponentChildren): RNode {
  return <em>{c}</em>;
}
export function createSpan(c: ComponentChildren): RNode {
  return <span>{c}</span>;
}
export function identity(c: ComponentChildren): RNode {
  return <Fragment>{c}</Fragment>;
}

export type Translator = Readonly<{
  translate: (message: MessageDescriptor, values?: SimpleMessageValues) => string;
}>;

export function useTranslation(): Translator {
  const intl = useIntl();
  const translate = useMemo(() => {
    return {
      translate: intl.formatMessage,
    };
  }, [intl]);

  return translate;
}

type TranslationProps = Readonly<{
  defaultMessage: string;
  id?: string;
  description?: string;
  values?: SimpleMessageValues | RichMessageValues;
}>;

export function Translate(props: TranslationProps): RNode {
  const {values, ...descriptor} = props;
  const intl = useIntl();
  return <Fragment>{intl.formatMessage(descriptor, values)}</Fragment>;
}
