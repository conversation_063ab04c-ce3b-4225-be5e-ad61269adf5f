// Helper for testing components that require Relations
/** @jsx h */

import {renderHook} from '@testing-library/preact';
import {h} from 'preact';

import {ModelContext} from '../context/triples';
import useRelations from '../hooks/useRelations';

import type {MapModel} from '../rdfmodels';
import type {ComponentChildren} from '../types';

export function buildRelationsFromModel(model: MapModel): ReturnType<typeof useRelations> {
  const wrapper = ({children}: {children: ComponentChildren}) => <ModelContext.Provider value={model}>
    {children}
  </ModelContext.Provider>;
  const {result} = renderHook(() => useRelations(), {wrapper});

  return result.current;
}
