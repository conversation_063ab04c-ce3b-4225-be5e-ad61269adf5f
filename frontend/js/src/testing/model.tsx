// Helper for testing components that require a ModelContext
/** @jsx h */

import {render} from '@testing-library/preact';
import {h} from 'preact';

import {ModelContext} from '../context/triples';
import {MapModel} from '../rdfmodels';
import {TripleStore} from '../triplestore';

import type {ComponentChildren, Term} from '../types';

export function buildModel(triples: Term[]): MapModel {
  const store = new TripleStore();
  store.putManyRawTriples(triples);
  return new MapModel(store);
}

export function renderWithModel(children: ComponentChildren, triples: Term[]): ReturnType<typeof render> {
  return render(
    <ModelContext.Provider value={buildModel(triples)}>
      {children}
    </ModelContext.Provider>,
  );
}

