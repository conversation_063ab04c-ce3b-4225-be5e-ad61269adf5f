// Hacky way to achieve flexibility for panels layout

import {useLayoutEffect} from 'preact/hooks';

import type {RefObject} from '../types';

const PANEL_SPACING = 'var(--panel-spacing)';

const PROPS_MAPPING = {
  height: 'maxHeight',
  width: 'maxWidth',
} as const;
function getSetter(prop: typeof PROPS_MAPPING[keyof typeof PROPS_MAPPING]) {
  return (element: HTMLElement|null, value: string) => {
    if (element) {
      element.style[prop] = value;
    }
  };
}

export default function useResizePanelMaxDimension(
  panel: RefObject<HTMLElement>,
  fodderRefs: RefObject<HTMLElement>[],
  prop: keyof typeof PROPS_MAPPING,
  shouldCalculate: boolean,
  fallBackSize = '100%',
): void {
  useLayoutEffect(() => {
    const setSize = getSetter(PROPS_MAPPING[prop]);

    // Note: Execution sequence in the rendering cycle might differ between Chrome and Safari.
    const observer = new ResizeObserver((entries: ResizeObserverEntry[]) => {
      const [observedElementsSize, observedElementsCount] = entries.reduce((acc, entry) => {
        const observedElementsSize = parseFloat(getComputedStyle(entry.target)[prop]);
        return [acc[0] + observedElementsSize, acc[1] + (observedElementsSize ? 1 : 0)];
      }, [0, 0]);

      const maxSize = observedElementsSize ? `calc(100% - ${observedElementsCount} * ${PANEL_SPACING} - ${observedElementsSize}px)` : '100%';

      setSize(panel.current, maxSize);
    });

    const observedElements = fodderRefs.flatMap(e => e.current ? [e.current] : []);
    if (shouldCalculate) {
      observedElements.forEach((fodder) => {
        observer.observe(fodder);
      });
    } else {
      setSize(panel.current, fallBackSize);
    }

    return () => observer.disconnect();
  });
}
