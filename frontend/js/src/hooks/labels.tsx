// Select labels for current display

import {useMemo} from 'preact/hooks';

import {BASE_LENS} from '../constants';
import {usePlace} from '../context/place';
import {useModel} from '../context/triples';
import {splitRange} from '../functions/lens';
import {IRIS} from '../rdf';
import {partitionSubj} from '../triplejump';
import {defaultCompare} from '../sort';
import {lensStack, useLensDetailStackByZoom} from './lenses';

import type {MapModel} from '../rdfmodels';
import type {Existence} from '../types';

export type LabelForViewInfo = {
  labelIRI: string;
  targetIRI: string;
  point: [number, number];
  lensDetail: string;
};

export type LabelsByTarget = {
  [targetIRI: string]: LabelForViewInfo[];
};

type LabelForViewInfoSpecific = LabelForViewInfo & {specific: boolean};
type LabelsByTargetSpecific = {[targetIRI: string]: LabelForViewInfoSpecific[]};
type LabelsByDetail = {
  [lensDetailIRI: string]: LabelsByTargetSpecific;
};

export function by_point(a: LabelForViewInfo, b: LabelForViewInfo): number {
  // Sort by latitude to stack top to bottom, then longitude, use iri to ensure stability.
  return b.point[0] - a.point[0] || a.point[1] - b.point[1] || defaultCompare(a.labelIRI, b.labelIRI);
}

/**
 * Accumulate labels for each target, that those on non-specific (base) detail
 * (e.g. vm:_detail1) should not be displayed if there is one on a specific detail.
 *
 * Used for a particular state of view.
 */
function mergeLabels(labels: LabelsByDetail, detailsStack: string[]): Readonly<LabelsByTarget> {
  const merged: LabelsByTargetSpecific = {};
  detailsStack.forEach(detail => {
    const atDetail = labels[detail];
    assignLabelsBySpecificity(merged, atDetail);
  });
  return merged;
}

// Helper function to assign according to specificity.
function assignLabelsBySpecificity(merged: LabelsByTargetSpecific, extra: LabelsByTargetSpecific) {
  for (const targetIRI in extra) {
    const existing = merged[targetIRI]?.[0].specific;
    const specific = extra[targetIRI][0].specific;
    if (existing !== specific) {
      if (existing != null && specific === false) {
        continue;
      }
      merged[targetIRI] = [];
    }
    merged[targetIRI].push(...extra[targetIRI]);
  }
}

type Obj<T> = {[key: string]: T};

export function removeLargestMax<T>(labels: Readonly<Obj<T>>): Obj<T> {
  const ranges = Object.keys(labels);
  const largestMax = ranges.reduce((max, range) => (
    Math.max(max, splitRange(range)[1])
  ), 0);

  // No zoom limit to these labels so don't need to update
  if (largestMax === Infinity) {
    return labels;
  }

  return ranges.reduce<Obj<T>>((newLabels, range) => {
    let newRange = range;
    // Modify the range to be to Infinity when unit or limit is largestMax
    if (+range === largestMax) {
      newRange = range + '-';
    } else if (range.endsWith('-' + largestMax)) {
      newRange = range.slice(0, range.indexOf('-') + 1);
    }
    newLabels[newRange] = labels[range];
    return newLabels;
  }, {});
}

function useLabelsByLensDetail(): Readonly<LabelsByDetail> {
  const mm = useModel();
  return mm._registerCache('labelsByLensDetail', (mm: MapModel) => {
    const labelsByDetail: LabelsByDetail = {};

    mm.labels().forEach((labelIRI: string) => {
      const [{
        [IRIS.VM.forThing]: targetIRI,
        [IRIS.VM.forLensDetail]: lensDetail, // eg. vm:_detail_ecosystem_1
        [IRIS.VM.atGeoPoint]: rawPoint,
      }, _] = partitionSubj(mm, labelIRI, {});

      if (lensDetail && rawPoint) {
        const labelsByTarget = (labelsByDetail[lensDetail] ??= {});
        const specific = mm.ofLensOf(lensDetail) !== BASE_LENS;
        const point = JSON.parse(rawPoint);
        (labelsByTarget[targetIRI] ??= []).push({labelIRI, point, targetIRI, lensDetail, specific});
      }
    });
    return labelsByDetail;
  });
}

/**
 * All labels that are on the lowest details of appearance on the lens stack.
 *
 * Assuming within a lens all labels will be visible at the lowest detail of that lens.
 * Used to accumulate labels and items that can appear at any state of the view.
 */
export function useLabelsAtLowestStack(): Readonly<LabelsByTarget> {
  const mm = useModel();
  const {lensLocation} = usePlace();
  const labels = useLabelsByLensDetail();

  return useMemo(() => {
    const merged: LabelsByTargetSpecific = {};
    for (const lens of lensStack(mm, lensLocation)) {
      const details = (mm.detailsOf(lens) as string[]).reverse();
      const atLowestDetail: LabelsByTargetSpecific = {};
      for (const detail of details) {
        for (const targetIRI in labels[detail]) {
          atLowestDetail[targetIRI] ??= [...labels[detail][targetIRI]];
        }
      }
      assignLabelsBySpecificity(merged, atLowestDetail);
    }
    return merged;
  }, [labels, mm, lensLocation]);
}

/**
 * Labels that are on the current lens detail stack.
 *
 * Used to derive labels and items that appear on the current state of view.
 */
export function useLabelsAtCurrentStack(): Readonly<LabelsByTarget> {
  const labelsByLensDetail = useLabelsByLensDetail();
  const detailsStack = useLensDetailStackByZoom().join('\n');

  return useMemo(() => {
    const details = detailsStack.split('\n');
    return mergeLabels(labelsByLensDetail, details);
  }, [labelsByLensDetail, detailsStack]);
}

export function useLabelledItems(): Readonly<Existence> {
  const labels = useLabelsAtLowestStack();

  return useMemo(() => (
    Object.keys(labels).reduce<Existence>((acc, targetIRI) => {
      acc[targetIRI] = true;
      return acc;
    }, {})
  ), [labels]);
}

export function useCurrentLabels(): Readonly<LabelForViewInfo>[] {
  const labels = useLabelsAtCurrentStack();

  return useMemo(() => {
    return Object.values(labels).flatMap(x => x).sort(by_point);
  }, [labels]);
}
