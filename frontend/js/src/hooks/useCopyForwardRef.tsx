// Supply local ref on a forwardRef component

import {useCallback} from 'preact/hooks';

import type {ForwardRef, RefObject} from '../types';

export default function useCopyForwardRef<P>(forwardRef: ForwardRef<P>, ref: RefObject<P>): (elem: P|null)=> void {
  return useCallback((elem: P|null) => {
    ref.current = elem;
    if (!forwardRef) {
      return;
    }
    if ('current' in forwardRef) {
      forwardRef.current = elem;
    } else {
      forwardRef(elem);
    }
  }, [forwardRef, ref]);
}
