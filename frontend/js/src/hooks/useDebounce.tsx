import {useMemo} from 'preact/hooks';

import {DEFAULT_DEBOUNCE_MS, debounce} from '../functions/debounce';

import type {Inputs} from 'preact/hooks';
import type {DebounceReturn} from '../functions/debounce';

type AnyFunction<TArgs, TReturn> = (...args: TArgs[]) => TReturn | void;

/**
 * A React hook that returns a debounced version of the provided function, along with a
 * method to cancel the debounced invocation. The debounced function will only be invoked
 * after `delayMs` milliseconds have passed since the last call. The hook also handles
 * updating the debounced function when dependencies change.
 *
 * @template TArgs - The type of arguments the debounced function accepts.
 *
 * @param {AnyFunction<TArgs, void>} func - The function to debounce. This function should accept arguments of type `TArgs` and return `void`.
 * @param {Inputs} deps - An array of dependencies that, when changed, will recreate the debounced function.
 * @param {number} [delayMs=DEFAULT_DEBOUNCE_MS] - The number of milliseconds to delay the invocation of `func`. Defaults to `DEFAULT_DEBOUNCE_MS` (400ms).
 *
 * @returns {DebounceReturn<TArgs>} An object containing:
 *   - `debouncedFunction`: The debounced version of `func`. This function accepts arguments of type `TArgs` and returns `void`.
 *   - `clearDebounce`: A function to cancel any pending invocation of `debouncedFunction`.
 *
 * @example
 * const { debouncedFunction, clearDebounce } = useDebounce(
 *   (value: string) => console.log('Debounced value:', value),
 *   [],
 *   500
 * );
 *
 * debouncedFunction('Hello'); // Will log 'Debounced value: Hello' after 500ms
 * clearDebounce(); // Cancels the pending invocation
 */
export function useDebounce<TArgs>(
  func: AnyFunction<TArgs, void>,
  deps: Inputs,
  delayMs = DEFAULT_DEBOUNCE_MS,
): DebounceReturn<TArgs> {
  return useMemo(() => {
    return debounce(func, delayMs);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [delayMs, ...deps]);
}
