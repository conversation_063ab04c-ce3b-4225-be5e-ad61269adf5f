// Skips the effect after first render

import {useEffect, useRef} from 'preact/hooks';

export function useEffectAfterMount(
  callback: () => void,
  deps: unknown[],
): void {
  const justMounted = useRef(true);
  // eslint-disable-next-line consistent-return
  useEffect(() => {
    if (!justMounted.current) {
      return callback();
    }
    justMounted.current = false;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, deps);
}
