// Focus first child on mount

import {useEffect, useRef} from 'preact/hooks';

import type {RefObject} from '../types';

export const focusables = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';

export function focusFirstChild(element: HTMLElement|null): void {
  element?.querySelector<HTMLElement>(focusables)?.focus();
}

export default function useFocusedFirstRef<T extends HTMLElement>(): RefObject<T> {
  const parentRef = useRef<T>(null);

  useEffect(() => {
    focusFirstChild(parentRef.current);
  }, []);

  return parentRef;
}
