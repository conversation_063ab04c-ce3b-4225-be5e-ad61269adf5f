import {IRIS} from '../rdf';
import {lensStack} from './lenses';
import {buildModel} from '../testing/model';
import {BASE_LENS} from '../constants';


describe('lensStack', () => {
  it('views with normal onLens and no domain awareness', () => {
    const model = buildModel([
      {subj: 'lensA', pred: IRIS.RDF.type, obj: IRIS.VM.Lens},
      {subj: 'lensB', pred: IRIS.RDF.type, obj: IRIS.VM.Lens},
      {subj: 'parentLensA', pred: IRIS.RDF.type, obj: IRIS.VM.Lens},
      {subj: 'viewA', pred: IRIS.VM.onLens, obj: 'lensA'},
      {subj: 'lensA', pred: IRIS.VM.parentLens, obj: 'parentLensA'},
      {subj: 'viewB', pred: IRIS.VM.onLens, obj: 'lensB'},
    ]);
    expect(lensStack(model, 'viewA')).toEqual(['lensA', 'parentLensA']);
    // also test that we get expected behaviour even if domain is passed
    expect(lensStack(model, 'viewB,domainA')).toEqual(['lensB']);
  });
  it('view with lensMap looks up correct lensstack based on domains', () => {
    const model = buildModel([
      {subj: 'lensDefault', pred: IRIS.RDF.type, obj: IRIS.VM.Lens},
      {subj: 'view', pred: IRIS.VM.onLensMap, obj: 'lensMap'},
      {subj: 'view', pred: IRIS.VM.onLens, obj: 'lensDefault'},
      {subj: 'lensA', pred: IRIS.RDF.type, obj: IRIS.VM.Lens},
      {subj: 'parentLensA', pred: IRIS.RDF.type, obj: IRIS.VM.Lens},
      {subj: 'lensA', pred: IRIS.VM.parentLens, obj: 'parentLensA'},
      {subj: 'lensB', pred: IRIS.RDF.type, obj: IRIS.VM.Lens},
      {subj: 'parentLensB', pred: IRIS.RDF.type, obj: IRIS.VM.Lens},
      {subj: 'lensB', pred: IRIS.VM.parentLens, obj: 'parentLensB'},
      {subj: 'lensNodeA', pred: IRIS.RDF.type, obj: IRIS.VM.LensMapNode},
      {subj: 'lensNodeA', pred: IRIS.VM.forDomain, obj: 'domainA'},
      {subj: 'lensNodeA', pred: IRIS.VM.forDomain, obj: 'domainB'},
      {subj: 'lensNodeA', pred: IRIS.VM.onLens, obj: 'lensA'},
      {subj: 'lensNodeA', pred: IRIS.VM.forLensMap, obj: 'lensMap'},
      {subj: 'lensNodeB', pred: IRIS.RDF.type, obj: IRIS.VM.LensMapNode},
      {subj: 'lensNodeB', pred: IRIS.VM.forDomain, obj: 'domainB'},
      {subj: 'lensNodeB', pred: IRIS.VM.onLens, obj: 'lensB'},
      {subj: 'lensNodeB', pred: IRIS.VM.forLensMap, obj: 'lensMap'},
    ]);
    expect(lensStack(model, 'view,domainA,domainB')).toEqual(['lensA', 'parentLensA']);
    expect(lensStack(model, 'view,domainB')).toEqual(['lensB', 'parentLensB']);
    // no matching lensNode for this location, should fall back to default lens set with onLens
    expect(lensStack(model, 'view,domainC')).toEqual(['lensDefault']);
  });
  it('return base lens if view is empty', () => {
    const model = buildModel([
      {subj: BASE_LENS, pred: IRIS.RDF.type, obj: IRIS.VM.Lens},
    ]);
    expect(lensStack(model, '')).toEqual([BASE_LENS]);
  });
});
