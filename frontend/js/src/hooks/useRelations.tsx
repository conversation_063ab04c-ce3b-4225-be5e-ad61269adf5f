// Using model item relationships without triplestore querying

import {useMemo} from 'preact/hooks';

import {useModel} from '../context/triples';
import {buildRelations, getPlaneMask, getPlaneMatch, toFirstPlane} from '../triplejump';

import {IRIS} from '../rdf';

import type {MapModel} from '../rdfmodels';

type RelationsCache = Readonly<{[iri: string]: number}>;
type FnForSome = ((iri: string) => boolean) | ((iri: string) => void);
type Get = (iri: string) => undefined|RelationsCache;
type Some = (iri: string, fn: FnForSome) => boolean;
type Orth = (iri: string) => string[];
type Match = (relations: RelationsCache, iri: string) => boolean;

export type RelationFunctions = {
  getRelations: Get;
  coreOrthogonal: Orth;
  someOffPlane: Some;
  hasAnyRelation: Match;
  hasCategoricalRelation: Match;
};

export default function useRelations(): RelationFunctions {
  const mm = useModel();

  const relations = mm._registerCache('relations', (mm: MapModel) => buildRelations(
    mm,
    [
      {[IRIS.RDF.type]: true},
      {[IRIS.VM.inRegion]: true, ...mm._categoricalProperties()},
      {[IRIS.VM.broader]: true},
      {...mm._primaryProperties()},
    ],
    {
      [IRIS.VMHE.ActivityInvolvement]: true,
      [IRIS.VM.TrustPerception]: true,
    },
  ));

  return useMemo(() => {
    // We are defining some predicates to be on certain planes, the lower
    // order should be ignored for plotting markers for instance, so define
    // which is the first that should be used for marker purposes.
    const firstCrossPlane = 1 << 3;
    // Mask to match three initial planes
    const onPlaneRelation = getPlaneMatch(4 | 2 | 1);

    const getRelations: Get = (iri) => relations[iri];

    const hasAnyRelation: Match = (rels, iri) => rels[iri] > 0;
    const catMask = getPlaneMask(2, true, false);
    const hasCategoricalRelation: Match = (rels, iri) => (rels[iri] & catMask) === catMask;

    const someOffPlane: Some = (iri, fn) => {
      const rels = relations[iri];
      if (rels) {
        for (const k in rels) {
          if (!onPlaneRelation(rels[k]) && fn(k)) {
            return true;
          }
        }
      }
      return false;
    };
    const coreOrthogonal: Orth = (iri) => {
      let results: string[] = [];
      const rels = relations[iri];
      if (rels) {
        let currentPlane = 0;
        for (const k in rels) {
          const plane = toFirstPlane(rels[k]);
          if (plane === currentPlane) {
            results.push(k);
          } else if (plane > currentPlane && plane >= firstCrossPlane) {
            currentPlane = plane;
            results = [k];
          }
        }
      }
      return results;
    };
    return {getRelations, hasAnyRelation, hasCategoricalRelation, someOffPlane, coreOrthogonal};
  }, [relations]);
}
