// Simple one-time data loading for use with Suspense

import {useState} from 'preact/hooks';

type InternalState<T> = (
  {promise: Promise<void>} |
  {error: Error} |
  {result: T}
);

export default function useWanna<T>(promiser: () => Promise<T>): () => T {
  const [reader] = useState(() => {
    let internal: InternalState<T> = {
      promise: promiser()
        .then(result => {
          internal = {result};
        }, error => {
          internal = {error};
        }),
    };

    return () => {
      if ('result' in internal) {
        return internal.result;
      }
      if ('error' in internal) {
        throw internal.error;
      }
      throw internal.promise;
    };
  });
  return reader;
}
