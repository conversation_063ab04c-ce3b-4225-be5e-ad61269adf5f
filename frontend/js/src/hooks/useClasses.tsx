// Using model item relationships without triplestore querying

import {useMemo} from 'preact/hooks';

import {useModel} from '../context/triples';
import {IRIS} from '../rdf';

type All = () => string[];
type Get = (iri: string) => undefined|Readonly<string[]>;
type Has = (iri: string) => boolean;

export type ClassFunctions = {
  allClasses: All;
  allExternalClasses: All;
  getClassChain: Get;
  hasClass: Has;
};

export default function useClasses(): ClassFunctions {
  const mm = useModel();

  return useMemo(() => {
    const classCache = mm._classChains();

    const allClasses: All = () => Object.keys(classCache);
    const allExternalClasses: All = () => Object.keys(classCache).filter(
      cls => cls !== IRIS.VM.Label);
    const getClassChain: Get = (iri) => classCache[iri];
    const hasClass: Has = (iri) => classCache[iri] != null;
    return {allClasses, allExternalClasses, getClassChain, hasClass};
  }, [mm]);
}

