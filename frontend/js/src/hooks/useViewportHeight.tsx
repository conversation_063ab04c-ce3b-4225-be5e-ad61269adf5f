import {useEffect} from 'preact/hooks';

import {useDebounce} from './useDebounce';

/**
 * Adjusts the viewport height by calculating it based on `visualViewport` or `innerHeight`.
 * Sets the calculated height as a custom CSS variable `--vh` to be used for responsive designs.
 * This function ensures correct behavior across browsers, especially on Safari.
 *
 * @param {Event} _ - The event that triggered the viewport height adjustment.
 */
const adjustViewportHeight = (_: Event) => {
  let viewportHeight = window.visualViewport?.height || window.innerHeight;

  // If we're on Safari and visualViewport.height is not providing the correct value
  if (navigator.userAgent.includes('Safari') && !navigator.userAgent.includes('Chrome')) {
    viewportHeight = window.innerHeight; // Use innerHeight as a fallback
  }

  const vh = viewportHeight * 0.01;
  document.documentElement.style.setProperty('--vh', `${vh}px`);
};

/**
 * Custom hook that adjusts the viewport height and sets a CSS variable `--vh` to represent 1% of the current viewport height.
 *
 * This hook listens for `resize` and `visualViewport` resize events, ensuring the `--vh` value dynamically updates in response
 * to changes in the viewport height, including when mobile virtual keyboards appear or disappear.
 * This allows elements styled with `--vh` to correctly adjust their height, ensuring the layout remains consistent across different devices and viewport changes.
 *
 * @param {number} [debounceDelay=50] - The delay in milliseconds for debouncing the viewport height adjustment.
 * @returns {void}
 */
export const useViewportHeight = (debounceDelay: number = 50): void => {
  const {debouncedFunction: debouncedAdjustViewportHeight} = useDebounce(adjustViewportHeight, [], debounceDelay);

  useEffect(() => {
    debouncedAdjustViewportHeight(); // Initial call to adjust on mount

    window.addEventListener('resize', debouncedAdjustViewportHeight);
    window.visualViewport?.addEventListener('resize', debouncedAdjustViewportHeight);

    return () => {
      window.removeEventListener('resize', debouncedAdjustViewportHeight);
      window.visualViewport?.removeEventListener('resize', debouncedAdjustViewportHeight);
    };
  }, [debouncedAdjustViewportHeight]);
};
