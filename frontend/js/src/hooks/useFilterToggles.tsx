/**
 * Interfaces functions for filters as togglable.
 * Filter toggling requires changing relevant filters to suit the an environment it is serving,
 * e.g. aType + plotability=related for items to appear on map.
 */

import {useCallback, useMemo} from 'preact/hooks';

import {
  useDefaultFilters,
  useFiltersPartialReset,
  useNavigation,
  usePlace,
} from '../context/place';
import {uInclusionsEqual} from '../functions/keyed';

import type {Filters, Inclusion} from '../types';

export interface FilterToggles {
  /**
   * Reset the filters. Currently resets to default of view regardless of toggles state.
   */
  reset(): void;

  /**
   * Checks if filters can be reset. Currently checks difference against default filters
   * regardless of toggles state.
   */
  isResettable: boolean;

  /**
   * Checks the toggle state of filter.
   */
  checkFilter(type: InclusionFilterType, iri: string): boolean;

  /**
   * Switch filter on/off. Different type might need extra changes.
   */
  toggleFilter(type: InclusionFilterType, iri: string): void;
}

type InclusionFilters = Pick<Filters, 'aType'|'itemKinds'|'relation'>;
type InclusionFilterType = keyof InclusionFilters;
function isInclusionFilterType(input: string): input is InclusionFilterType {
  return ['aType', 'itemKinds', 'relation'].includes(input);
}

// For now, ontology only allows category instance and classes.
const toggleInclusions: InclusionFilterType[] = ['aType', 'itemKinds'];

function toggleInclusionsEqual(a: Filters, b: Filters): boolean {
  return toggleInclusions.every(k => uInclusionsEqual(a[k], b[k]));
}

function cloneFilters(filters: Filters): Filters {
  const clone = {...filters};
  (Object.keys(filters) as (keyof Filters)[]).forEach(key => {
    if (isInclusionFilterType(key)) {
      clone[key] = {...filters[key]};
    }
  });
  return clone;
}

function removeFilters(type: InclusionFilterType, iris: string[], filters: Filters) {
  const inclusion = iris.reduce<Inclusion>((acc, iri) => {
    if (acc[iri]) {
      delete acc[iri];
    }
    return acc;
  }, {...filters[type]});
  if (Object.keys(inclusion).length) {
    filters[type] = inclusion;
  } else {
    delete filters[type];
  }
}

function addFilters(type: InclusionFilterType, iris: string[], filters: Filters) {
  iris.reduce<Inclusion>((acc, iri) => {
    acc[iri] = true;
    return acc;
  }, filters[type] ??= {});
}

function checkInclusionFilter(type: InclusionFilterType, iri: string, filters: Filters) {
  return !!filters[type]?.[iri];
}

export default function useFilterToggles(): FilterToggles {
  const {filters, view} = usePlace();
  const {setFilters} = useNavigation();
  const {isResettable, reset} = useFiltersPartialReset();
  const getDefaultFilters = useDefaultFilters();

  const defaultFilters = useMemo(() => getDefaultFilters(view), [view, getDefaultFilters]);

  const checkFilter = useCallback((type: InclusionFilterType, iri: string) => {
    if (isInclusionFilterType(type)) {
      return checkInclusionFilter(type, iri, filters) && filters.plottable === 'related';
    }
    return false;
  }, [filters]);

  const toggleFilter = useCallback((type: InclusionFilterType, iri: string) => {
    const next = cloneFilters(filters);
    if (checkFilter(type, iri)) {
      removeFilters(type, [iri], next);
      if (toggleInclusionsEqual(defaultFilters, next)) {
        next.plottable = defaultFilters.plottable;
      }
    } else {
      addFilters(type, [iri], next);
      next.plottable = 'related';
    }

    setFilters(next);
  }, [checkFilter, defaultFilters, filters, setFilters]);

  return {
    reset,
    isResettable,
    checkFilter,
    toggleFilter,
  };
}
