// Helper to make context handling a little neater

import {useContext} from 'preact/hooks';

import type {Context} from '../types';

/**
 * A hook that safely accesses React context values with built-in null checking.
 *
 * This hook wraps the standard useContext hook but adds a runtime check to ensure
 * that the context value is not null or undefined. If the context value is null
 * (which typically means the component is being rendered outside of its required
 * provider), this hook will throw an error with a helpful message.
 *
 * @template T - The type of the context value (excluding null)
 * @param {Context<T|null>} context - The React context object to access
 * @returns {T} The non-null context value
 * @throws {Error} If the context value is null or undefined (missing provider)
 */
export default function useEnsuredContext<T>(context: Context<T|null>): T {
  const t = useContext<T|null>(context);
  if (t == null) {
    throw new Error('Context value is null - this component must be used within its corresponding provider');
  }
  return t;
}

/* Attempt at fancy, does not quite work
 *
function useEnsuredContext<T>(context: Context<T|null>): T {
  const t = useContext<T|null>(context);
  const r = useRef<ReturnType<typeof setTimeout>|null>(null);
  if (t == null) {
    throw new Promise((resolve, reject) => {
      r.current = setTimeout(() => {
        reject(Error('missing provider'));
      }, 1000);
    });
  }
  if (r.current) {
    clearTimeout(r.current);
    r.current = null;
  }
  return t;
}
 */
