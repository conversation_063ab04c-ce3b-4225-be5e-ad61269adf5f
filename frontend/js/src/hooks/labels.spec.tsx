import {removeLargestMax} from './labels';

describe('removeLargestMax', () => {
  it('handles 0', () => {
    const labels = {'0': 'zero'};
    expect(removeLargestMax(labels)).toEqual({'0-': 'zero'});
  });
  it('handles 0-1', () => {
    const labels = {'0-1': 'zero-one'};
    expect(removeLargestMax(labels)).toEqual({'0-': 'zero-one'});
  });
  it('handles 0, 1', () => {
    const labels = {'0': 'zero', '1': 'one'};
    expect(removeLargestMax(labels)).toEqual({'0': 'zero', '1-': 'one'});
  });
  it('handles 0, 1-', () => {
    const labels = {'0': 'zero', '1-': 'one-inf'};
    expect(removeLargestMax(labels)).toEqual(labels);
  });
  it('handles 0-1, 1', () => {
    const labels = {'0-1': 'zero-one', '1': 'one'};
    expect(removeLargestMax(labels)).toEqual({'0-': 'zero-one', '1-': 'one'});
  });
  it('handles 0, 1, 2', () => {
    const labels = {'0': 'zero', '1': 'one', '2': 'two'};
    expect(removeLargestMax(labels)).toEqual({'0': 'zero', '1': 'one', '2-': 'two'});
  });
});
