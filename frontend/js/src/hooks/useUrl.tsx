// Track and manage browser history and current url
//
// Generally inspired by useLocation in wouter but beyond location.pathname

import {useCallback, useEffect, useRef, useState} from 'preact/hooks';

export type Url = Readonly<{path: string; search: string; hash: string}>;
export type UrlResolver = (prevUrl: Url) => Url & {replace: boolean};
export type UrlNavigate = (resolve: UrlResolver) => void;

export function currentUrl(base = '', loc = location): Url {
  return {
    path: loc.pathname.slice(base.length),
    search: loc.search.slice(1),
    hash: loc.hash.slice(1),
  };
}

export function equalUrl(a: Url, b: Url): boolean {
  return a.path === b.path && a.search === b.search && a.hash === b.hash;
}

export default function useUrl(basePath: string): [Url, UrlNavigate] {
  const [url, setUrl] = useState(() => currentUrl(basePath));
  const prev = useRef(url);

  useEffect(() => {
    const update = () => {
      const next = currentUrl(basePath);
      if (!equalUrl(prev.current, next)) {
        setUrl((prev.current = next));
      }
    };
    addEventListener('popstate', update);
    update();
    return () => {
      removeEventListener('popstate', update);
    };
  }, [basePath]);

  const navigate = useCallback((resolve: UrlResolver) => {
    const next = resolve(prev.current);
    if (equalUrl(prev.current, next)) {
      return;
    }
    const eventKind = next.replace ? 'replaceState' : 'pushState';
    const search = next.search && '?' + next.search;
    const hash = next.hash && '#' + next.hash;
    history[eventKind](0, '', basePath + next.path + search + hash);
    setUrl((prev.current = next));
  }, [basePath]);

  return [url, navigate];
}
