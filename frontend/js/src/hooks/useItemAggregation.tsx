// A polyforest of labelled things linked by a child to parent relation with a given predicate.
/** @jsx h */

import {useContext, useMemo} from 'preact/hooks';

import {AssetsContext} from '../context/assets';
import {usePlace} from '../context/place';
import {useModel} from '../context/triples';
import flag from '../flags';
import {toBfs} from '../functions/graph';
import {iconFor} from '../icons';
import {IRIS} from '../rdf';
import {useLabelledItems} from './labels';
import useFilteredItems from './useFilteredItems';
import useRelations from './useRelations';

import type {Assets} from '../context/assets';
import type {ParentChildrenGraph} from '../functions/graph';
import type {MapModel} from '../rdfmodels';
import type {Existence, Keyed} from '../types';

export type TargetItems = {
  [targetIri: string]: Existence;
};

export type ItemAggregationValue = {[icon: string]: number};
export type ItemAggregation = {[targetIri: string]: ItemAggregationValue};

function buildForest(mm: MapModel,
  relPredicate: string,
  getRelations: ReturnType<typeof useRelations>['getRelations'],
  labelled: Keyed<unknown>): ParentChildrenGraph {
  const forest: ParentChildrenGraph = {};
  for (const thing in labelled) {
    forest[thing] = Object.keys(getRelations(thing) || {})
      .filter(iri => mm._getField(iri, relPredicate) === thing && iri !== thing && iri in labelled);
  }
  return forest;
}

function useAggregationTargetsBy(relPredicate: string)
  : {targetItems: TargetItems; forest: ParentChildrenGraph; bfs: string[]} {
  const mm = useModel();
  const {getRelations, coreOrthogonal} = useRelations();
  const {items} = useFilteredItems();
  const labelled = useLabelledItems();
  const {lensLocation} = usePlace();
  const shouldAggregate = flag(mm, 'markerAggregation');

  const {forest, bfs} = useMemo((): {forest: ParentChildrenGraph; bfs: string[]} => {
    if (!shouldAggregate) {
      return {forest: {}, bfs: []};
    }
    let tmp: ReturnType<typeof buildForest>;
    // Hidden view dependency for labelled. Probably unnecessary.
    return mm._registerCache(relPredicate + '-rels', () => ({}))[String(lensLocation)]
      ??= {forest: tmp = buildForest(mm, relPredicate, getRelations, labelled), bfs: toBfs(tmp)};
  }, [shouldAggregate, mm, relPredicate, lensLocation, getRelations, labelled]);

  // Populating items markers to a separate "pointer" object
  // due to slow items[iri] look up during traverse.
  const targetItems = useMemo(() => {
    const result: TargetItems = {};
    if (!shouldAggregate) {
      return {};
    }
    for (const iri in items) {
      const {name} = items[iri];
      if (name && !(iri in labelled)) {
        coreOrthogonal(iri).forEach(relIRI => {
          (result[relIRI] ??= {})[iri] = true;
        });
      }
    }
    return result;
  }, [coreOrthogonal, items, labelled, shouldAggregate]);
  return {forest, bfs, targetItems};
}

function calculateAggregationValue(mm: MapModel, assets: Assets, descendants: Existence): ItemAggregationValue {
  const byIcon: ItemAggregationValue = {};
  for (const iri in descendants) {
    const icon = iconFor(mm, assets, iri, mm.classOf(iri)) || 'null';
    byIcon[icon] ??= 0;
    byIcon[icon]++;
  }
  return byIcon;
}

function buildAggregation(mm: MapModel, assets: Assets, items: TargetItems,
  forest: ParentChildrenGraph, bfs: string[], forLabels: Keyed<unknown>): ItemAggregation {
  // Accumulate descendants
  const descendants: TargetItems = {};
  for (let i = bfs.length - 1; i >= 0; --i) {
    const iri = bfs[i];
    forest[iri]?.forEach(childIri => {
      if (!(childIri in forLabels)) {
        descendants[iri] = {...descendants[iri], ...items[childIri], ...descendants[childIri]};
      }
    });
  }

  const result: ItemAggregation = {};
  for (const iri in descendants) {
    result[iri] = calculateAggregationValue(mm, assets, descendants[iri]);
  }
  return result;
}

export default function useItemAggregation(forLabels: Keyed<unknown>): ItemAggregation {
  const mm = useModel();
  const assets = useContext(AssetsContext);
  const {targetItems, forest, bfs} = useAggregationTargetsBy(IRIS.VM.broader); //TODO: conditional hook

  return useMemo(() => {
    return buildAggregation(mm, assets, targetItems, forest, bfs, forLabels);
  }, [assets, bfs, forest, forLabels, mm, targetItems]);
}
