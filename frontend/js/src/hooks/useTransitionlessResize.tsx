import {useEffect} from 'preact/hooks';

export default function useTransitionlessResize(className = 'no-transitions', duration = 100): void {
  useEffect(() => {
    let timer: number|null = 0;

    const handler = () => {
      if (timer) {
        clearTimeout(timer);
        timer = null;
      } else {
        document.body.classList.add(className);
      }

      timer = window.setTimeout(() => {
        document.body.classList.remove(className);
        timer = null;
      }, duration);
    };

    window.addEventListener('resize', handler);
    return () => window.removeEventListener('resize', handler);
  }, [className, duration]);
}
