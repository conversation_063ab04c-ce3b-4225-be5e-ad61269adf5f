// Wrapper for function with conditional execution.

import {useCallback} from 'preact/hooks';

import type {Inputs} from 'preact/hooks';

type AnyFunction<TArgs extends unknown[], TReturn> = (...args: TArgs) => TReturn | void;

function execute<TArgs extends unknown[], TReturn>(func: AnyFunction<TArgs, TReturn>, ready: boolean): AnyFunction<TArgs, void> {
  return (...args) => {
    if (ready) {
      func(...args);
    }
  };
}

export function useDefer<TArgs extends unknown[], TReturn>(func: AnyFunction<TArgs, TReturn>, ready: boolean, deps: Inputs)
  : AnyFunction<TArgs, void> {
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const deferedFunc = useCallback(
    execute(func, ready),
    [func, ready, ...deps],
  );

  return deferedFunc;
}
