// Function for getting the common base lens of lenses or lens details.
// A base lens is a base lens of itself.

import {useCallback} from 'preact/hooks';

import {useModel} from '../context/triples';

export default function useCommonBaseLens(): (...lenses: string[]) => string|null {
  const mm = useModel();

  return useCallback((...lenses: string[]) => {
    const parent = lenses.length && mm.baseLensOf(lenses[0]) || null;
    const hasCommonBase = lenses.every(lens => parent === mm.baseLensOf(lens));
    return hasCommonBase ? parent : null;
  }, [mm]);
}
