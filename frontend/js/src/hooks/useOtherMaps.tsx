// Function to return all other maps that the thing exists on

import {useCallback} from 'preact/hooks';

import {usePlace} from '../context/place';
import {useModel} from '../context/triples';
import {IRIS} from '../rdf';
import {useFilterOfInterest} from './useFilterOfInterest';
import useRelations from './useRelations';

export default function useOtherMaps(): (iri: string) => {iri: string; name: string}[] {
  const mm = useModel();
  const {view} = usePlace();
  const {coreOrthogonal} = useRelations();
  const filterOfInterest = useFilterOfInterest();
  const isMap = view && mm.classOf(view) === IRIS.VM.Map;

  return useCallback((iri: string) => {
    if (!isMap) {
      return [];
    }
    // TODO: This logic won't return any matches for labels/markers on maps with domain-aware lenses.
    // This is probably desirable and we currently have no projects requiring this, but still needs some thought.
    const mapsWithLabel = (mm.locationsFor(iri) as string[])
      .filter(v => v && mm.classOf(v) === IRIS.VM.Map);
    const mapsCoveringClass = mm.mapsCovering(mm.classOf(iri));
    const mapsWithMarker = coreOrthogonal(iri)
      .flatMap(parentIRI => mm.locationsFor(parentIRI).filter((v: string) => v && mm.classOf(v) === IRIS.VM.Map))
      .filter((map, index, maps) => maps.indexOf(map) === index)
      .filter(map => filterOfInterest[map]?.(iri));
    return [...mapsWithLabel, ...mapsCoveringClass, ...mapsWithMarker]
      .filter(v => v !== view)
      .filter((value, index, self) => self.indexOf(value) === index)
      .map(v => ({iri: v, name: mm.nameOf(v) || v}));
  }, [isMap, mm, coreOrthogonal, filterOfInterest, view]);
}
