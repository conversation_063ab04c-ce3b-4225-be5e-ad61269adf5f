// Get filtered individuals for placement on map
//
// [09/05/23] Includes aggregation markers

import {useMemo} from 'preact/hooks';

import {BASE_DETAIL} from '../constants';
import {usePlace} from '../context/place';
import {useModel} from '../context/triples';
import {buildLines, pathsCount} from '../functions/svgpaths';
import {defaultCompare} from '../sort';
import {useDataPathsByLens} from './geolocations';
import {useLabelledItems, useLabelsAtCurrentStack, useLabelsAtLowestStack} from './labels';
import useFilteredItems from './useFilteredItems';
import useItemAggregation from './useItemAggregation';
import useRelations from './useRelations';

import type {Coord} from '../context/ol';
import type {MapModel} from '../rdfmodels';
import type {Paths} from '../types';
import type {DataPathsByLens} from './geolocations';
import type {LabelsByTarget} from './labels';
import type {PresentItems} from './useFilteredItems';

/**
 * Lens detail used only to store items with atGeoPoint location that appear
 * on all zoom level/detail.
 *
 * It is different than vm:_detail1, vm:_detail2, etc. which are of lens
 * BASE_LENS that related to particular zoom range.
 */
const LENS_DETAIL_BASE = `${BASE_DETAIL}_base`;

type ItemInfo = {kind: 'item'; iri: string; name: string} | {kind: 'aggregation'; icon: string; count: number};
type MapItemInfo = {info: ItemInfo; point: [number, number]; origin: string; lensDetail?: string};
type MapItemsByDetail = {[lensDetail: string]: MapItemInfo[]};
type MapItemsByTarget = {[targetIri: string]: MapItemInfo[]};
type VisibleMapItems = {
  focused: MapItemsByTarget;
  all: MapItemInfo[];
};
type PathDetails = {path: Paths; lensDetail: string; origin: string; infer?: true};
type PathDetailsByIri = {[iri: string]: PathDetails[]};

function infoKey(a: MapItemInfo) {
  return a.info.kind === 'aggregation' ? a.info.icon : a.info.iri;
}

export function by_point(a: MapItemInfo, b: MapItemInfo): number {
  // Sort aggregation at bottom, latitude to stack top to bottom, then longitude, use iri to ensure stability.
  return (+(b.info.kind === 'aggregation') - +(a.info.kind === 'aggregation') || b.point[0] - a.point[0] || a.point[1] - b.point[1]
    || defaultCompare(infoKey(a), infoKey(b)));
}

function modelMapper(path: SVGPathElement): (info: ItemInfo, pointAtLength: number) => {info: ItemInfo; point: [number, number]} {
  return (info: ItemInfo, pointAtLength: number) => {
    const itemPosition = path.getPointAtLength(pointAtLength);
    return {info, point: [itemPosition.x, itemPosition.y] as Coord};
  };
}

function placeItemsOnPath(paths: Paths, related: ItemInfo[]): ({info: ItemInfo; point: [number, number]})[] {
  // TODO: remove SVG path building logic once implemented in backend
  const pathElement = document.createElementNS('http://www.w3.org/2000/svg', 'path');
  pathElement.setAttribute('d', buildLines(paths));

  const count = pathsCount(paths);
  const pathLength = pathElement.getTotalLength();
  const mapper = modelMapper(pathElement);

  const itemSpaces = related.length + 1;
  let spacing = 0;
  if (count === 1) {
    spacing = pathLength / itemSpaces;
  } else {
    spacing = itemSpaces % count === 0 ? pathLength / (itemSpaces + 1) : pathLength / itemSpaces;
  }

  return related.map((t, i) => {
    return mapper(t, spacing * (i + 1));
  });
}

// Derive a line that is under the box
function boxToDefaultPath(box: [number, number, number, number]): [Coord, Coord] {
  const horizontal = box[2] + (box[2] - box[0]) * 0.5;
  return [[horizontal, box[1]], [horizontal, box[3]]];
}

export function buildLabelPaths(mm: MapModel, forLabels: LabelsByTarget, dataPaths: DataPathsByLens): PathDetailsByIri {
  // TODO: Better way of defaulting label paths
  return Object.values(forLabels).flatMap(x => x)
    .reduce<PathDetailsByIri>(
      (acc, {labelIRI, targetIRI, lensDetail}) => {
        if (labelIRI && targetIRI) {
          const origin = targetIRI;
          const lens = mm.ofLensOf(lensDetail);
          const definedPath = dataPaths[lens]?.[targetIRI];
          if (definedPath) {
            (acc[targetIRI] ??= []).push({path: definedPath, lensDetail, origin});
          } else {
            const box = mm.parseDisplay(labelIRI)?.box;
            if (box && box.length === 4) {
              (acc[targetIRI] ??= []).push({path: [boxToDefaultPath(box)], lensDetail, origin, infer: true});
            }
          }
        }
        return acc;
      }, {});
}

function useMappedItems(forLabels: LabelsByTarget, items: PresentItems): MapItemsByDetail {
  const mm = useModel();
  const {coreOrthogonal} = useRelations();
  const labelled = useLabelledItems();
  const dataPaths = useDataPathsByLens();
  const aggregation = useItemAggregation(forLabels);

  const labelPaths = useMemo(() => buildLabelPaths(mm, forLabels, dataPaths), [mm, dataPaths, forLabels]);

  return useMemo(() => {
    const details: {[lensDetail: string]: MapItemInfo[]} = {};
    const relateds: {[iri: string]: ItemInfo[]} = {};

    // Item markers
    for (const iri in items) {
      const {name} = items[iri];
      const point = mm.geoPointOf(iri);
      if (point && name) {
        const origin = JSON.stringify(point); //normalizes arrays
        // markers at a point on map level are not on lens detail
        (details[LENS_DETAIL_BASE] ??= []).push({info: {kind: 'item', iri, name}, point, origin});
      } else if (name && !labelled[iri]) {
        // Find all items that have got locations, and dangle off each of those
        coreOrthogonal(iri).forEach(relIRI => {
          (relateds[relIRI] ??= []).push({kind: 'item', iri, name});
        });
      }
    }

    // Aggregation markers
    for (const parentIRI in aggregation) {
      (relateds[parentIRI] ??= []).push(
        ...(Object.entries(aggregation[parentIRI])
          .map<ItemInfo>(([icon, count]) => ({kind: 'aggregation', icon, count}))),
      );
    }

    // Second pass - placement
    for (const parentIRI in relateds) {
      if (parentIRI in labelPaths) {
        labelPaths[parentIRI].forEach(({path, lensDetail, origin}) => {
          const pathItems = placeItemsOnPath(path, relateds[parentIRI]);
          pathItems.forEach(item => {
            (details[lensDetail] ??= []).push({...item, lensDetail, origin});
          });
        });
      }
    }
    return details;
  }, [aggregation, items, mm, labelled, labelPaths, coreOrthogonal]);
}

function toMapItemsByTarget(itemsByDetail: MapItemsByDetail): {items: MapItemsByTarget; length: number} {
  return Object.values(itemsByDetail)
    .flatMap(x => x)
    .reduce<{items: MapItemsByTarget; length: number}>((acc, item) => {
      (acc.items[infoKey(item)] ??= []).push(item);
      acc.length++;
      return acc;
    }, {items: {}, length: 0});
}

export function useFilteredMapItemsAtLowestStack(): MapItemsByDetail {
  const labels = useLabelsAtLowestStack();
  const {items} = useFilteredItems();
  const {selection} = usePlace();
  const mm = useModel();
  const extended = useMemo(() => {
    const extended = {...items};
    if (selection && !items[selection]) {
      const name = mm.nameOfOrFallback(selection);
      extended[selection] = {name};
    }
    return extended;
  }, [items, selection, mm]);
  return useMappedItems(labels, extended);
}

export function useFilteredMapItemsAtCurrentStack(): MapItemsByDetail {
  const labels = useLabelsAtCurrentStack();
  const {items} = useFilteredItems();
  return useMappedItems(labels, items);
}

// get map items at the lowest lens detail
export function useMapItemsAtLensStack(): MapItemsByTarget {
  const byDetail = useFilteredMapItemsAtLowestStack();
  return useMemo(() => {
    return toMapItemsByTarget(byDetail).items;
  }, [byDetail]);
}

function removeMapItem(items: MapItemsByTarget, item: MapItemInfo): void {
  const key = infoKey(item);
  if (items[key]) {
    items[key] = items[key].filter(current => current.origin !== item.origin);
    if (!items[key].length) {
      delete items[key];
    }
  }
}

function useFocusedMapItems(): MapItemsByTarget {
  const {selection} = usePlace();
  const itemsAtLowestStack = useFilteredMapItemsAtLowestStack();

  return useMemo(() => {
    let focused: MapItemsByTarget = {};
    const {items: lowest} = toMapItemsByTarget(itemsAtLowestStack);

    if (selection && lowest[selection]) {
      focused = {[selection]: lowest[selection]};
    }
    return focused;
  }, [itemsAtLowestStack, selection]);
}

export default function useMapItems(): VisibleMapItems {
  const itemsAtCurrentStack = useFilteredMapItemsAtCurrentStack();
  const focused = useFocusedMapItems();

  return useMemo(() => {
    const filteredFocused = {...focused};
    const all = Object.values(itemsAtCurrentStack).flatMap(x => x);
    all.forEach(item => {
      removeMapItem(filteredFocused, item);
    });
    all.push(...Object.values(filteredFocused).flatMap(x => x));
    all.sort(by_point);

    return {
      focused,
      all,
    };
  }, [focused, itemsAtCurrentStack]);
}
