// API interactions for handling user annotations

import {useMemo, useRef} from 'preact/hooks';

import {djangoCsrfTokenValue} from '../django';
import {useModel} from '../context/triples';
import {prepareJsonPost} from '../functions/post';

import type {PostBody} from '../functions/post';
import type {Triple} from '../rdf';

export interface Actions {
  get(user: string): Promise<unknown>;
  put(user: string): Promise<void>;
}

export type Params = Readonly<{
  mapIRI: string;
  user: string;
}>;

type Headers = {[header: string]: string};

// Minimum and maximum time-based request delays in milliseconds
// Using 30 seconds to 12 hours
const DELAY_MIN = 30000;
const DELAY_MAX = 43200000;

function buildPath(params: Params): string {
  return `/api/annotations?map=${params.mapIRI}&who=${params.user}`;
}

function buildHeaders(headers: () => Headers): Headers {
  const token = djangoCsrfTokenValue();
  if (!token) {
    console.error('missing csrf token');
    return headers();
  }
  return {
    ...headers(),
    'X-CSRFToken': token,
  };
}

function postAnnotations(params: Params, postBody: PostBody) {
  return fetch(buildPath(params), {
    method: 'POST',
    mode: 'same-origin',
    // No headers to add when sending as a form
    headers: buildHeaders(() => ({})),
    // The fetch api spec states a hard limit of 64KiB for content length
    // specifically in the case keep-alive is enabled, so must not use.
    // See also WONTFIX <https://issues.chromium.org/issues/41406116>
    keepalive: 4096 + postBody.getBlob().size < 64 * 1024,
    body: postBody.buildForm(),
  })
    .then(response => {
      if (!response.ok) {
        throw new Error('failed to save annotations');
      }
      return response.json();
    })
    .then(body => {
      postBody.finish();
      console.log('annotations recorded', body);
    });
}

function getAnnotations(params: Params) {
  return fetch(buildPath(params))
    .then(response => {
      if (response.status === 404) {
        return {terms: []};
      } else if (response.ok) {
        return response.json();
      }
      throw new Error('failed to load annotations');
    });
}

// Give ISO string from a unix timestamp chopping off precision and timezone
function timestampToISO(unix: number): string {
  return new Date(unix).toISOString().slice(0, 19);
}

// Give a truncated exponential backoff for delay in milliseconds
//
// By using from time of original attempt for the doubling is robust through
// intermediate attempts due to other factors like tab suspension.
function backoff(from: number, to?: number): number {
  const timeDiff = (to ?? Date.now()) - from;
  return Math.max(DELAY_MIN, Math.min(DELAY_MAX, timeDiff));
}

export default function useAnnotationApi(mapIRI: string): Actions {
  const mm = useModel();
  const pendingRef = useRef<(() => number)|null>(null);
  return useMemo(() => {
    const process = (response: {terms: unknown}) => {
      mm.clearAnnotations();
      mm._store.putManyRawTriples(response.terms);
    };
    const send = (user: string, postBody: PostBody) => {
      return postAnnotations({mapIRI, user}, postBody);
    };
    const cancelAndStart = (): number => {
      const cancel = pendingRef.current;
      if (cancel != null) {
        pendingRef.current = null;
        return cancel();
      }
      return Date.now();
    };

    return {
      get: (user) => {
        return getAnnotations({mapIRI, user}).then(process);
      },
      put: (user) => {
        // Preserve the initial 'now' for timestamp and backoff purposes
        const now = cancelAndStart();
        return prepareJsonPost(
          // Stamp from the first point a new annotation was recorded
          'Annotations' + timestampToISO(now),
          // Grab terms right now rather than after `DELAY` which is fine as any
          // future edit will cancel this pending request and regather triples.
          mm.annotationTriples().map(
            (t: Triple) => ({subj: t.subj, pred: t.pred, obj: t.obj})),
        ).then(postBody => {
          return new Promise(resolve => {
            const clear = () => {
              clearTimeout(timeout);
              document.removeEventListener('visibilitychange', onHide);
            };
            const giveUp = () => {
              resolve();
              postBody.finish();
              clear();
              return now;
            };
            const onIdle = () => {
              resolve(send(user, postBody).then(clear, retry));
            };
            const onHide = () => {
              if (document.visibilityState === 'hidden') {
                resolve(send(user, postBody).then(clear, retry));
              }
            };
            let timeout = setTimeout(onIdle, backoff(now));
            const retry = (error: Error) => {
              console.log('annotations failed', error);
              clearTimeout(timeout);
              timeout = setTimeout(onIdle, backoff(now));
            };
            document.addEventListener('visibilitychange', onHide);
            pendingRef.current = giveUp;
          });
        });
      },
    };
  }, [mapIRI, mm]);
}
