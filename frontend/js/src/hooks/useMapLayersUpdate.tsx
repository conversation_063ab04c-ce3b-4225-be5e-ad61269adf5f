// Logic for updating the OL map layers according to changes to the tile source stack.

import {useEffect, useLayoutEffect, useMemo, useRef} from 'preact/hooks';

import LayerGroup from 'ol/layer/Group';
import Tile from 'ol/layer/Tile';
import ImageTile from 'ol/source/ImageTile';
import {renderXYZTemplate} from 'ol/uri';

import {useMap} from '../context/ol';
import useTilesZoom from './useTilesZoom';

import type {LoaderOptions} from 'ol/source/DataTile';
import type TileSource from 'ol/source/Tile';
import type {Options as SourceOptions} from 'ol/source/Tile';
import type {BackgroundType} from '../context/ol';

const loadError = new Error('Image load failed');

/**
 * Combined makeLoaderFromTemplates and loadImage from ol/source/ImageTile.js
 * to bypass defaulting crossOrigin to 'anonymous' in base DataTileSource.js.
 */
function noCorsLoader(src: string) {
  return (z: number, x: number, y: number, options: LoaderOptions) => new Promise((resolve, reject) => {
    const image = new Image();
    image.crossOrigin = null;
    image.addEventListener('load', () => resolve(image));
    image.addEventListener('error', () => reject(loadError));
    image.src = renderXYZTemplate(src, z, x, y, options.maxY);
  });
}

type LayerBySource = {
  [source: string]: Tile<TileSource>;
};

/**
 * Z selection function that compensates resolution to zoom for midpoint
 *
 * Required so tiles transition at .5 of a zoom level rather than some other value.
 *
 * From:
 * <https://openlayers.org/en/latest/apidoc/module-ol_tilegrid_TileGrid-TileGrid.html#getZForResolution>
 */
function zDirection(value: number, high: number, low: number): number {
  return value - low * Math.sqrt(high / low);
}

export default function useMapLayersUpdate(attribution: string, maxNativeZoom: number, lens: string|null, srcStack: string, bg: BackgroundType): void {
  const {map} = useMap();
  const getTilesZoom = useTilesZoom();
  /**
   * Helper reference containing layers dealt only within this hook, and
   * for managing updates of existing and new tiles rather than full reset.
   *
   * Due to how Openlayers load tiles in memory, we would like the preserve
   * the layer references in case same sources remain between stack changes.
   */
  const layerRefs = useRef<LayerBySource>({});

  const tileLayers = useMemo(() => {
    const tileLayers = new LayerGroup({zIndex: 1});
    map.addLayer(tileLayers);
    return tileLayers;
  }, [map]);

  useEffect(() => () => {
    map.removeLayer(tileLayers);
  }, [map, tileLayers]);

  useLayoutEffect(() => {
    const layersCollection = tileLayers.getLayers();
    const stackArray = srcStack.split('\n');

    /**
     * Create or move existing tile layers to the front of layer group
     * in stack's order and remove the rest at the end.
     */
    stackArray.forEach((src, i) => {
      let tileLayer = layerRefs.current[src];
      if (!tileLayer) {
        // Zoom constraints cares about the lowest and highest level, but nothing in-between.
        const {minZoom, maxZoom} = getTilesZoom(lens, src);
        // Remove preload when we are done with old tile locations.
        const preload = /\/detail-\d\//.test(src) ? undefined : Infinity;
        const tile = new Tile({
          source: new ImageTile({
            attributions: i ? undefined : attribution,
            zDirection,
            projection: map.getView().getProjection(),
            minZoom,
            maxZoom: Math.min(maxZoom, maxNativeZoom),
            transition: 0,
            loader: noCorsLoader(src),
          } as SourceOptions),
          preload,
        });
        // If the base tile has no background settings, set it as white
        if (!i && !bg) {
          const setter = () => {
            tile.setBackground('#fff');
            map.un('loadend', setter);
          };
          map.on('loadend', setter);
        }
        tileLayer = layerRefs.current[src] = tile;
      } else {
        layersCollection.remove(tileLayer);
      }
      layersCollection.insertAt(i, tileLayer);
    });

    const stackSize = stackArray.length;

    while (stackSize < layersCollection.getLength()) {
      const tile = (layersCollection.item(stackSize) as Tile<ImageTile>);
      const src = tile.getSource()?.getKey() || '';
      delete layerRefs.current[src];
      layersCollection.remove(tile);
    }
  }, [map, attribution, maxNativeZoom, lens, srcStack, tileLayers, getTilesZoom, bg]);

  useEffect(() => {
    return () => {
      Object.values(layerRefs.current).forEach(layer => map.removeLayer(layer));
      layerRefs.current = {};
    };
  }, [map]);
}
