// Give an integer value for the current zoom level

import {useEffect, useState} from 'preact/hooks';

import {useMap} from '../context/ol';

type ZoomMapper = (zoom: number) => number;

export default function useCurrentZoomLevel(fn: ZoomMapper = Math.round): number {
  const [zoom, setZoom] = useState(0);
  const {map} = useMap();
  const view = map.getView();

  useEffect(() => {
    const onRes = () => {
      setZoom(fn(view.getZoom() || 0));
    };
    view.on('change:resolution', onRes);
    onRes();
    return () => {
      view.un('change:resolution', onRes);
    };
  }, [fn, setZoom, view]);

  return zoom;
}
