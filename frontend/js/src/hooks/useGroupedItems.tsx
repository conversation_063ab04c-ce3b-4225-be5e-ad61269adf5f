// Get filtered individuals for placement on map

import {useMemo} from 'preact/hooks';

import {usePlace} from '../context/place';
import {useModel} from '../context/triples';
import useClasses from '../hooks/useClasses';
import {IRIS} from '../rdf';
import {comparatorForLocale, defaultCompare} from '../sort';

import type {PresentItems} from './useFilteredItems';
import type {MapModel} from '../rdfmodels';
import type {Existence, Item} from '../types';

export type Group = {
  initial: Item;
  items: Item[];
  order?: number;
};

export type GroupedItems = {
  groups: Group[];
  ungrouped: Item[];
};

export type GroupedBuilder = {[key: string]: Group};

function ungrouped(): null {
  return null;
}

function groupBySingleStep(predicateMethod: keyof MapModel, typeIRI: string) {
  return function grouper(mm: MapModel, item: Item): string|null {
    const key = mm[predicateMethod](item.iri);
    if (key) {
      return key;
    }
    if (mm.classOf(item.iri) === typeIRI) {
      return item.iri;
    }
    return null;
  };
}

function groupByCategory(mm: MapModel, {iri}: Item): Existence|string|null {
  const categories = mm.categoriesOf(iri);
  if (categories == null && mm.isUsedAsCategory(iri)) {
    return iri;
  }
  return categories;
}

function groupByType(mm: MapModel, item: Item): string|null {
  const rdftype = mm.classOf(item.iri);
  if (rdftype === IRIS.OWL.Class) {
    return item.iri;
  }
  return rdftype;
}

function getGrouper(name: string) {
  switch (name) {
    case 'type':
      return groupByType;
    case 'org':
      return groupBySingleStep('worksIn', IRIS.ORG.OrganisationalUnit);
    case 'category':
      return groupByCategory;
    case 'stakeholder':
      return groupBySingleStep('ofStakeholderOf', IRIS.VM.Stakeholder);
    default:
      return ungrouped;
  }
}

function getSorter(name: string|undefined, standardCompare: ReturnType<typeof comparatorForLocale>) {
  // Close over sort functions to expose compare function
  function by_initial_name(a: Group, b: Group): number {
    return by_name(a.initial, b.initial);
  }

  function by_name(a: Item, b: Item): number {
    // Sort by name A-Z, use iri to ensure stability.
    return standardCompare(a.name, b.name) || defaultCompare(a.iri, b.iri);
  }

  function sortByOrder(a: Group, b: Group): number {
    const aOrder = a?.order;
    const bOrder = b?.order;
    if (aOrder == null && bOrder == null) {
      return by_initial_name(a, b);
    }

    if (aOrder == null) {
      return 1;
    }

    if (bOrder == null) {
      return -1;
    }

    return aOrder - bOrder || by_initial_name(a, b);
  }

  switch (name) {
    case 'order':
      return {by_name, for_group: sortByOrder};
    default:
      return {by_name, for_group: by_initial_name};
  }
}

export default function useGroupedItems(items: PresentItems, lang: string, appendSelection: boolean): GroupedItems {
  const {hasClass} = useClasses();
  const mm = useModel();
  const {grouping, selection} = usePlace();

  // TODO: Better to default via view, but many views in EoM to update.
  const fallbackGrouping = useMemo(() => {
    return hasClass(IRIS.VM.Stakeholder) ? 'stakeholder' : '';
  }, [hasClass]);

  const standardCompare = useMemo(() => comparatorForLocale(lang), [lang]);
  const selectionToAppend = appendSelection && selection || null;

  return useMemo(() => {
    const grouper = getGrouper(grouping.method || fallbackGrouping);
    const ungrouped: Item[] = [];
    const grouped: GroupedBuilder = {};
    const makeGroup = (iri: string) => ({
      initial: {iri, name: items[iri]?.name || mm.nameOf(iri) || mm.className(iri) || '?'},
      items: [],
      order: mm.orderOf(iri),
    });

    for (const iri in items) {
      const item = {iri, name: items[iri].name};
      const key = grouper(mm, item);
      if (typeof key === 'string') {
        const group = (grouped[key] ??= makeGroup(key));
        if (key !== iri) {
          group.items.push(item);
        }
      } else if (key) {
        for (const subkey in key) {
          (grouped[subkey] ??= makeGroup(subkey)).items.push(item);
          // TODO: If we don't break here, we stop guaranteeing uniqueness of items.
          //       So, for now, it's an arbitrary member, but may want to duplicate.
          break;
        }
      } else {
        ungrouped.push(item);
      }
    }
    // TODO: Push up selection-not-in-filters again to the item list components
    if (selectionToAppend && !items[selectionToAppend] && mm.subjectExists(selectionToAppend)) {
      ungrouped.push({iri: selectionToAppend!, name: mm.nameOfOrFallback(selectionToAppend)});
    }
    const {by_name, for_group} = getSorter(grouping.sort, standardCompare);
    for (const key in grouped) {
      grouped[key].items.sort(by_name);
    }
    ungrouped.sort(by_name);
    const result = {
      groups: Object.values(grouped),
      ungrouped: ungrouped.filter(item => !grouped[item.iri]),
    };
    result.groups.sort(for_group);
    return result;
  }, [selectionToAppend, fallbackGrouping, grouping, items, mm, standardCompare]);
}
