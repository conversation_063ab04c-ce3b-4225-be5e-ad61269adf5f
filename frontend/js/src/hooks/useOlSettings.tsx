// Hook to set up default Openlayers settings and override

import {useMemo} from 'preact/hooks';

import type {Coord} from '../context/ol';
import type {Extent} from 'ol/extent';

type Bounds = [Coord, Coord];
type ZoomOptions = {
  minZoom: number;
  maxZoom: number;
  zoom: number;
};
export type MapOptions = Partial<ZoomOptions & {
  attribution: string;
  bounds: Bounds;
  maxNativeZoom: number;
}>;
export type TileLayerOptionSubset = {
  attribution: string;
  extent: Extent;
  maxNativeZoom: number;
};
export type OlOptions = TileLayerOptionSubset & ZoomOptions & {
  center: Coord;
  worldExtent: Extent;
};

function mapSettings(overrideSettings: MapOptions): OlOptions {
  const {
    attribution = '',
    bounds = [[0, 0], [-256, 256]],
    maxNativeZoom = 5,
    ...remainingSettings
  } = overrideSettings;
  const pad = 64;
  const center: Coord = [
    (bounds[0][0] + bounds[1][0]) >> 1,
    (bounds[0][1] + bounds[1][1]) >> 1,
  ];
  const maxBounds = [
    [bounds[0][0] + pad, bounds[0][1] - pad],
    [bounds[1][0] - pad, bounds[1][1] + pad],
  ];

  // Default settings that everyone gets unless overridden
  return {
    // Common map settings
    minZoom: 0,
    maxZoom: maxNativeZoom + 1,

    // Attribution settings for map and base tileset
    attribution,

    // Map settings derived from bounds in general
    extent: [bounds[1][0], bounds[0][1], bounds[0][0], bounds[1][1]],
    worldExtent: [maxBounds[1][0], maxBounds[0][1], maxBounds[0][0], maxBounds[1][1]],
    zoom: 0,
    center,

    // Tile layer settings for base tilesets
    maxNativeZoom,

    ...remainingSettings,
  };
}

export default function useOlSettings(strSettings: string): OlOptions {
  return useMemo(() => {
    // settings are supplied as a string and converted to an object down here
    // this is to avoid reloading the map every time they are fetched, as strings are invariant
    const initSettings: MapOptions = JSON.parse(strSettings);
    const settings = mapSettings(initSettings || {});
    console.log('using map settings', settings);
    return settings;
  }, [strSettings]);
}
