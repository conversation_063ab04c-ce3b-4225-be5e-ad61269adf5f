// Tests for model object value linking component
/** @jsx h */

import {IRIS} from '../rdf';
import {buildModel} from '../testing/model';
import {buildRelationsFromModel} from '../testing/relations';
import {buildFetcher} from './useFilterOfInterest';

describe('buildFetcher', () => {
  it('mixed with basic setup for all filter types', () => {
    const catTriples = [
      {subj: 'categoryA', pred: IRIS.RDF.type, obj: IRIS.OWL.Class},
      {subj: 'categoryA', pred: IRIS.RDFS.subClassOf, obj: IRIS.VM.Category},
      {subj: 'hasFirstCategory', pred: IRIS.RDFS.subPropertyOf, obj: IRIS.GIST.isCategorizedBy},
      {subj: 'catA_type1', pred: IRIS.RDF.type, obj: 'categoryA'},
      {subj: 'thing1', pred: 'hasFirstCategory', obj: 'catA_type1'},
    ];
    const classTriples = [
      {subj: 'classA', pred: IRIS.RDF.type, obj: IRIS.OWL.Class},
      {subj: 'thing2', pred: IRIS.RDF.type, obj: 'classA'},
    ];
    const mapTriples = [
      {subj: 'mapA', pred: IRIS.RDF.type, obj: IRIS.VM.Map},
      {subj: 'mapA', pred: IRIS.VM.classOfInterest, obj: 'catA_type1'},
      {subj: 'mapA', pred: IRIS.VM.classOfInterest, obj: 'classA'},
    ];
    const otherTriples = [
      {subj: 'classB', pred: IRIS.RDF.type, obj: IRIS.OWL.Class},
      {subj: 'thing3', pred: IRIS.RDF.type, obj: 'classB'},
    ];
    const model = buildModel([...catTriples, ...classTriples, ...mapTriples, ...otherTriples]);
    const relations = buildRelationsFromModel(model);

    const getFilter = buildFetcher(model, relations, 'mapA');

    expect(getFilter('thing1')).toEqual({iri: 'catA_type1', type: 'itemKinds'});
    expect(getFilter('thing2')).toEqual({iri: 'classA', type: 'aType'});
    expect(getFilter('thing3')).toEqual(null);
  });

  it('no filters of interest on map, should not return filter', () => {
    // derived from basic test
    const catTriples = [
      {subj: 'categoryA', pred: IRIS.RDF.type, obj: IRIS.OWL.Class},
      {subj: 'categoryA', pred: IRIS.RDFS.subClassOf, obj: IRIS.VM.Category},
      {subj: 'hasFirstCategory', pred: IRIS.RDFS.subPropertyOf, obj: IRIS.GIST.isCategorizedBy},
      {subj: 'catA_type1', pred: IRIS.RDF.type, obj: 'categoryA'},
      {subj: 'thing1', pred: 'hasFirstCategory', obj: 'catA_type1'},
    ];
    const classTriples = [
      {subj: 'classA', pred: IRIS.RDF.type, obj: IRIS.OWL.Class},
      {subj: 'thing2', pred: IRIS.RDF.type, obj: 'classA'},
    ];
    const mapTriples = [
      {subj: 'mapA', pred: IRIS.RDF.type, obj: IRIS.VM.Map},
    ];
    const otherTriples = [
      {subj: 'classB', pred: IRIS.RDF.type, obj: IRIS.OWL.Class},
      {subj: 'thing3', pred: IRIS.RDF.type, obj: 'classB'},
    ];
    const model = buildModel([...catTriples, ...classTriples, ...mapTriples, ...otherTriples]);
    const relations = buildRelationsFromModel(model);

    const getFilter = buildFetcher(model, relations, 'mapA');

    expect(getFilter('thing1')).toEqual(null);
    expect(getFilter('thing2')).toEqual(null);
  });

  it('category instance filter of gist:Category, should category filter', () => {
    const catTriples = [
      {subj: 'hasFirstCategory', pred: IRIS.RDFS.subPropertyOf, obj: IRIS.GIST.isCategorizedBy},
      {subj: 'catA_type1', pred: IRIS.RDF.type, obj: IRIS.GIST.Category},
      {subj: 'thing1', pred: 'hasFirstCategory', obj: 'catA_type1'},
    ];
    const mapTriples = [
      {subj: 'mapA', pred: IRIS.RDF.type, obj: IRIS.VM.Map},
      {subj: 'mapA', pred: IRIS.VM.classOfInterest, obj: 'catA_type1'},
    ];
    const model = buildModel([...catTriples, ...mapTriples]);
    const relations = buildRelationsFromModel(model);

    const getFilter = buildFetcher(model, relations, 'mapA');

    expect(getFilter('thing1')).toEqual({iri: 'catA_type1', type: 'itemKinds'});
  });

  it('item of subclass of class filter, should return the class filter', () => {
    const classTriples = [
      {subj: 'classA', pred: IRIS.RDF.type, obj: IRIS.OWL.Class},
      {subj: 'classSubA', pred: IRIS.RDFS.subClassOf, obj: 'classA'},
      {subj: 'thing2', pred: IRIS.RDF.type, obj: 'classSubA'},
    ];
    const mapTriples = [
      {subj: 'mapA', pred: IRIS.RDF.type, obj: IRIS.VM.Map},
      {subj: 'mapA', pred: IRIS.VM.classOfInterest, obj: 'classA'},
    ];
    const model = buildModel([...classTriples, ...mapTriples]);
    const relations = buildRelationsFromModel(model);

    const getFilter = buildFetcher(model, relations, 'mapA');

    expect(getFilter('thing2')).toEqual({iri: 'classA', type: 'aType'});
  });

  it('item of class and category intance filter, should return first filter in model', () => {
    // derived from basic test
    const catTriples = [
      {subj: 'categoryA', pred: IRIS.RDF.type, obj: IRIS.OWL.Class},
      {subj: 'categoryA', pred: IRIS.RDFS.subClassOf, obj: IRIS.VM.Category},
      {subj: 'hasFirstCategory', pred: IRIS.RDFS.subPropertyOf, obj: IRIS.GIST.isCategorizedBy},
      {subj: 'catA_type1', pred: IRIS.RDF.type, obj: 'categoryA'},
      {subj: 'thing1', pred: 'hasFirstCategory', obj: 'catA_type1'},
    ];
    const classTriples = [
      {subj: 'classA', pred: IRIS.RDF.type, obj: IRIS.OWL.Class},
      {subj: 'thing1', pred: IRIS.RDF.type, obj: 'classA'},
    ];
    const mapTriples = [
      {subj: 'mapA', pred: IRIS.RDF.type, obj: IRIS.VM.Map},
      {subj: 'mapA', pred: IRIS.VM.classOfInterest, obj: 'catA_type1'},
      {subj: 'mapA', pred: IRIS.VM.classOfInterest, obj: 'classA'},
    ];
    const model = buildModel([...catTriples, ...classTriples, ...mapTriples]);
    const relations = buildRelationsFromModel(model);

    const getFilter = buildFetcher(model, relations, 'mapA');

    expect(getFilter('thing1')).toEqual({iri: 'catA_type1', type: 'itemKinds'});
  });
});
