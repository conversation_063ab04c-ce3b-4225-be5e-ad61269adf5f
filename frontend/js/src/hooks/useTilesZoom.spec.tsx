// Tests for tiles zoom hook

import {IRIS} from '../rdf';
import {buildModel} from '../testing/model';
import {getLensTilesZoom} from './useTilesZoom';


describe('getLensTilesZoom', () => {
  it('multiple details of same url', () => {
    const model = buildModel([
      {subj: '_a_lens', pred: IRIS.RDF.type, obj: IRIS.VM.Lens},
      {subj: '_a_lens', pred: `${IRIS.VM.detail}1`, obj: '_a_detail_1'},
      {subj: '_a_lens', pred: `${IRIS.VM.detail}2`, obj: '_a_detail_2'},
      {subj: '_a_lens', pred: `${IRIS.VM.detail}3`, obj: '_a_detail_3'},
      {subj: '_a_detail_1', pred: IRIS.RDF.type, obj: IRIS.VM.LensDetail},
      {subj: '_a_detail_2', pred: IRIS.RDF.type, obj: IRIS.VM.LensDetail},
      {subj: '_a_detail_3', pred: IRIS.RDF.type, obj: IRIS.VM.LensDetail},
      {subj: '_a_detail_1', pred: IRIS.VM.usesMapTiles, obj: 's3://_a/tiles/{z}-{x}-{y}.png#style'},
      {subj: '_a_detail_2', pred: IRIS.VM.usesMapTiles, obj: 's3://_a/tiles/{z}-{x}-{y}.png#style'},
      {subj: '_a_detail_3', pred: IRIS.VM.usesMapTiles, obj: 's3://_a/tiles/{z}-{x}-{y}.png#style'},
      {subj: '_a_detail_1', pred: IRIS.VM.zoomRange, obj: '1-2'},
      {subj: '_a_detail_2', pred: IRIS.VM.zoomRange, obj: '3'},
      {subj: '_a_detail_3', pred: IRIS.VM.zoomRange, obj: '4-6'},
    ]);
    expect(getLensTilesZoom(model, '_a_lens', 's3://_a/tiles/{z}-{x}-{y}.png')).toEqual({
      'minZoom': 1,
      'maxZoom': 6,
    });
  });

  it('multiple details of different url', () => {
    const model = buildModel([
      {subj: '_a_lens', pred: IRIS.RDF.type, obj: IRIS.VM.Lens},
      {subj: '_a_lens', pred: `${IRIS.VM.detail}1`, obj: '_a_detail_1'},
      {subj: '_a_lens', pred: `${IRIS.VM.detail}2`, obj: '_a_detail_2'},
      {subj: '_a_lens', pred: `${IRIS.VM.detail}3`, obj: '_a_detail_3'},
      {subj: '_a_detail_1', pred: IRIS.RDF.type, obj: IRIS.VM.LensDetail},
      {subj: '_a_detail_2', pred: IRIS.RDF.type, obj: IRIS.VM.LensDetail},
      {subj: '_a_detail_3', pred: IRIS.RDF.type, obj: IRIS.VM.LensDetail},
      {subj: '_a_detail_1', pred: IRIS.VM.usesMapTiles, obj: 's3://_a/tiles/1/{z}-{x}-{y}.png#style'},
      {subj: '_a_detail_2', pred: IRIS.VM.usesMapTiles, obj: 's3://_a/tiles/2/{z}-{x}-{y}.png#style'},
      {subj: '_a_detail_3', pred: IRIS.VM.usesMapTiles, obj: 's3://_a/tiles/3/{z}-{x}-{y}.png#style'},
      {subj: '_a_detail_1', pred: IRIS.VM.zoomRange, obj: '1-2'},
      {subj: '_a_detail_2', pred: IRIS.VM.zoomRange, obj: '3'},
      {subj: '_a_detail_3', pred: IRIS.VM.zoomRange, obj: '4-6'},
    ]);
    expect(getLensTilesZoom(model, '_a_lens', 's3://_a/tiles/3/{z}-{x}-{y}.png')).toEqual({
      'minZoom': 4,
      'maxZoom': 6,
    });
  });

  it('multiple details of different url and no tiles matching', () => {
    const model = buildModel([
      {subj: '_a_lens', pred: IRIS.RDF.type, obj: IRIS.VM.Lens},
      {subj: '_a_lens', pred: `${IRIS.VM.detail}1`, obj: '_a_detail_1'},
      {subj: '_a_lens', pred: `${IRIS.VM.detail}2`, obj: '_a_detail_2'},
      {subj: '_a_lens', pred: `${IRIS.VM.detail}3`, obj: '_a_detail_3'},
      {subj: '_a_detail_1', pred: IRIS.RDF.type, obj: IRIS.VM.LensDetail},
      {subj: '_a_detail_2', pred: IRIS.RDF.type, obj: IRIS.VM.LensDetail},
      {subj: '_a_detail_3', pred: IRIS.RDF.type, obj: IRIS.VM.LensDetail},
      {subj: '_a_detail_1', pred: IRIS.VM.usesMapTiles, obj: 's3://_a/tiles/1/{z}-{x}-{y}.png#style'},
      {subj: '_a_detail_2', pred: IRIS.VM.usesMapTiles, obj: 's3://_a/tiles/2/{z}-{x}-{y}.png#style'},
      {subj: '_a_detail_3', pred: IRIS.VM.usesMapTiles, obj: 's3://_a/tiles/3/{z}-{x}-{y}.png#style'},
      {subj: '_a_detail_1', pred: IRIS.VM.zoomRange, obj: '1-2'},
      {subj: '_a_detail_2', pred: IRIS.VM.zoomRange, obj: '3'},
      {subj: '_a_detail_3', pred: IRIS.VM.zoomRange, obj: '4-6'},
    ]);
    expect(getLensTilesZoom(model, '_a_lens')).toEqual({
      'minZoom': 1,
      'maxZoom': 6,
    });
  });

  it('no details', () => {
    const model = buildModel([
      {subj: '_a_lens', pred: IRIS.RDF.type, obj: IRIS.VM.Lens},
    ]);
    expect(getLensTilesZoom(model, '_a_lens')).toEqual({
      'minZoom': 0,
      'maxZoom': Infinity,
    });
  });

  it('open range detail zoom', () => {
    const model = buildModel([
      {subj: '_a_lens', pred: IRIS.RDF.type, obj: IRIS.VM.Lens},
      {subj: '_a_lens', pred: `${IRIS.VM.detail}1`, obj: '_a_detail_1'},
      {subj: '_a_detail_1', pred: IRIS.RDF.type, obj: IRIS.VM.LensDetail},
      {subj: '_a_detail_1', pred: IRIS.VM.usesMapTiles, obj: 's3://_a/tiles/1/{z}-{x}-{y}.png#style'},
      {subj: '_a_detail_1', pred: IRIS.VM.zoomRange, obj: '1-'},
    ]);
    expect(getLensTilesZoom(model, '_a_lens')).toEqual({
      'minZoom': 1,
      'maxZoom': Infinity,
    });
  });
});
