// Function to check if thing exists in current view

import {useCallback} from 'preact/hooks';

import {usePlace} from '../context/place';
import {useModel} from '../context/triples';
import {IRIS} from '../rdf';
import useRelations from './useRelations';


export default function useExistOnView(): (iri: string) => boolean {
  const mm = useModel();
  const {view, lensLocation} = usePlace();
  const {coreOrthogonal} = useRelations();
  const isMap = view && mm.classOf(view) === IRIS.VM.Map;

  return useCallback((iri: string) => {
    // bad rewrite - matching lensLocation should work for majority of maps, but edge case
    // of maps with domains used in data filtering but not in lens selection means that
    // we also need to check against view. And it's awkward to check "do either of these
    // values exist in this array?" in javascript.
    const locations = [view || 'null', lensLocation];
    const asLabel = () => locations.some(loc => mm.locationsFor(iri).includes(loc));
    const asItemOnLabel = () => locations.some(
      loc => coreOrthogonal(iri).some(parentIRI => mm.locationsFor(parentIRI).includes(loc)),
    );

    // items with vm:atGeoPoint from useFilteredMapItems()
    const asItemAtPoint = !isMap && !!mm.geoPointOf(iri);

    return !!asItemAtPoint || asLabel() || asItemOnLabel();
  }, [view, lensLocation, mm, coreOrthogonal, isMap]);
}
