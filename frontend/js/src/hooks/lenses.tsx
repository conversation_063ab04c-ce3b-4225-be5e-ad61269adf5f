// Selects lenses and detail levels on lenses.

import {useCallback, useMemo} from 'preact/hooks';

import {usePlace} from '../context/place';
import {useModel} from '../context/triples';
import {splitRange} from '../functions/lens';
import useCurrentZoomLevel from './useCurrentZoomLevel';
import {useFilteredMapItemsAtLowestStack} from './useMapItems';
import {lensFromLocation} from '../triplejump';

import type {MapModel} from '../rdfmodels';
import type {Existence} from '../types';

export function atZoom(mm: MapModel, lens: string, zoom: number): [string, number] {
  const lensDetails = mm.detailsOf(lens);
  let idx = 0;
  for (const [i, lensDetail] of lensDetails.entries()) {
    const lensZoomRange = mm.zoomRangeOf(lensDetail);
    const [min, max] = splitRange(lensZoomRange);
    if (zoom < min) {
      break;
    }
    if (zoom <= max) {
      idx = i;
    }
  }
  return [lensDetails[idx], idx];
}

export function lensStack(mm: MapModel, lensLocation: string|null): string[] {
  const lens = lensFromLocation(mm, lensLocation);
  return mm.lensStackOfLens(lens);
}

export function getlensDetailMaxZoom(mm: MapModel, lensDetail: string): number {
  const lensZoomRange = mm.zoomRangeOf(lensDetail);
  const [_, maxZoom] = splitRange(lensZoomRange);
  return maxZoom;
}

// Returns function for determining current detail level of lens by map zoom.
export function useDetailLevelByZoomOf(): (lens: string|null) => [string|null, number] {
  const mm = useModel();
  const zoom = useCurrentZoomLevel();

  return useCallback((lens: string|null) => {
    if (!lens) {
      return [null, -1];
    }
    return atZoom(mm, lens, zoom);
  }, [mm, zoom]);
}

// Returns function for determining detail level of the top most lens where
// all filtered items appear
// It is assumed that all items appear at the bottom detail level.
function useDetailLevelByItems(): () => [string|null, number] {
  const mm = useModel();
  const {lensLocation} = usePlace();

  const itemLensDetails = Object.keys(useFilteredMapItemsAtLowestStack()).join('\n');
  return useCallback(() => {
    const lensDetails = itemLensDetails.split('\n').reduce<Existence>((acc, detail) => {
      acc[detail] = true;
      return acc;
    }, {});
    const lenses: string[] = lensStack(mm, lensLocation);
    let viewLensDetails: string[] = [];
    const maxNumber = Math.max(-1, ...lenses.map((lens, idx) => {
      const details = (mm.detailsOf(lens) as string[]);
      if (!idx) {
        viewLensDetails = details;
      }
      for (let detailNumber = 0; detailNumber < details.length; detailNumber++) {
        if (lensDetails[details[detailNumber]]) {
          return detailNumber;
        }
      }
      return -1;
    }));
    return maxNumber > -1 ? [viewLensDetails[maxNumber], maxNumber] : [null, -1] ;
  }, [itemLensDetails, mm, lensLocation]);
}

// Returns current detail level of each lens in the view's lens stack.
function useLensDetailStack(detailLevelFn: () => [string|null, number]): string[] {
  const mm = useModel();
  const {lensLocation} = usePlace();
  const [_, topLensDetailNumber] = detailLevelFn();

  return useMemo(() => {
    const lenses = lensStack(mm, lensLocation);

    const details = lenses.reduce<string[]>((acc, lens) => {
      const lensDetails = mm.detailsOf(lens);
      const detailNumber = Math.min(lensDetails.length - 1, topLensDetailNumber);
      acc.push(lensDetails[detailNumber]);
      return acc;
    }, []);
    return details;
  }, [mm, topLensDetailNumber, lensLocation]);
}

export function useLensDetailStackByItems(): string[] {
  const detailLevelByItems = useDetailLevelByItems();

  return useLensDetailStack(detailLevelByItems);
}

export function useLensDetailStackByZoom(): string[] {
  const mm = useModel();
  const {lensLocation} = usePlace();
  const detailLevelByZoomOf = useDetailLevelByZoomOf();

  return useMemo(() => {
    const lenses = lensStack(mm, lensLocation);
    const details = lenses.reduce<string[]>((acc, lens) => {
      const [detail, _] = detailLevelByZoomOf(lens);
      if (detail) {
        acc.push(detail);
      }
      return acc;
    }, []);
    return details;
  }, [mm, detailLevelByZoomOf, lensLocation]);
}
