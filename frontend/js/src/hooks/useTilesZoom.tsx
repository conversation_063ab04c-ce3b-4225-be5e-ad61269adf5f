// Gets the min and max zoom of tiles on a view
// Ignores zoom range gaps if details are missing.

import {useCallback} from 'preact/hooks';

import {useModel} from '../context/triples';
import {splitRange} from '../functions/lens';

import type {MapModel} from '../rdfmodels';

export type TilesZoom = {
  minZoom: number;
  maxZoom: number;
};

export function getLensTilesZoom(mm: MapModel, lens: string, tilesBaseMatch?: string): TilesZoom {
  let minZoom: number|null = null;
  let maxZoom: number|null = null;

  const details: string[] = lens && mm.detailsOf(lens) || [];
  details.filter(detail => !tilesBaseMatch || (mm.usesMapTilesOf(detail) as string)?.startsWith(tilesBaseMatch))
    .map(detail => {
      const range = splitRange(mm.zoomRangeOf(detail));
      return range;
    })
    .forEach(([min, max]) => {
      minZoom ??= min;
      maxZoom ??= max;
      minZoom = minZoom > min ? min : minZoom;
      maxZoom = maxZoom < max ? max : maxZoom;
    });

  minZoom ??= 0;
  maxZoom ??= Infinity;
  return {minZoom, maxZoom};
}

export default function useTilesZoom(): (lens: string|null, tilesBaseMatch?: string) => TilesZoom {
  const mm = useModel();

  return useCallback((lens: string|null, tilesBaseMatch?: string) => {
    if (!lens) {
      return {minZoom: 0, maxZoom: Infinity};
    }
    return getLensTilesZoom(mm, lens, tilesBaseMatch);
  }, [mm]);
}
