// Get individuals matching current filter set

import {useMemo} from 'preact/hooks';

import {registerCachedHook} from '../context/cache';
import {usePlace} from '../context/place';
import {gather, useSearchEngine} from '../context/search';
import {useModel} from '../context/triples';
import {useLabelledItems} from './labels';
import useRelations from './useRelations';
import {buildInclusion, buildIntersection, buildRelated} from '../functions/buildFilters';
import {allNamedItems} from '../triplejump';

import type {Filters, Plottability} from '../types';
import type {MapModel} from '../rdfmodels';
import type {SearchEngine} from '../context/search';
import type {NamedResult} from '../triplejump';
import type {RelationFunctions} from './useRelations';

export type Domains = {[key: string]: {values: string[]; predicate: string}};
export type PresentItems = {[iri: string]: Readonly<{name: string}>};
export type FilterResult = {
  items: Readonly<PresentItems>;
  length: number;
  remainingSearchedItems: Readonly<PresentItems>; // Maybe not here?
};

function legacyPlottable(mm: MapModel, view: string|null, iri: string): boolean {
  return !!mm.geoPointOf(iri) && ((mm.ofStoryOf(iri) ?? view) === view);
}

function checkNoDomain(domainInclusions: Filters['domain'], mm: MapModel): ReturnType<typeof buildInclusion>|null {
  if (!domainInclusions) {
    return null;
  }
  const notChecker = Object.keys(domainInclusions).reduce<{[group: string]: ReturnType<typeof buildInclusion>}>((acc, group) => {
    const inner = buildInclusion(domainInclusions[group], false);
    if (inner) {
      acc[group] = inner;
    }
    return acc;
  }, {});
  return iri => Object.entries(mm.domains() as Domains).some(([key, {predicate}]) => {
    return notChecker[key]?.(mm._getField(iri, predicate));
  }, {});
}

/**
 * Determine if item matches the plottability filter critera in current view
 *
 * Returns true if item is outside the filterset.
 *
 * For historical and flexibility purposes, this is a multi-stage check against
 * several factors. For each item, we are determining membership of the three
 * sets defined by ``Plottability`` - 'direct' for items within ``labelled``
 * or with explicit geo locations, 'related' which includes 'direct' and also
 * everything one step removed in higher planes, and 'detached' which is all
 * other items.
 */
function excludeByPlottable(
  p: Plottability|undefined,
  mm: MapModel,
  view: string|null,
  iri: string,
  labelled: {[itemIRI: string]: unknown},
  someOffPlane: RelationFunctions['someOffPlane'],
): boolean {
  if (!p) {
    return false;
  }
  if (labelled[iri]) {
    return p === 'detached';
  }
  if (legacyPlottable(mm, view, iri)) {
    // Counting markers with explicit coords as 'direct' not only 'related'
    return p === 'detached';
  }
  if (someOffPlane(iri, k => labelled[k])) {
    return p !== 'related';
  }
  return p !== 'detached';
}

// Get the initial array of items matching a search term if provided
//
// If this returns a result that also includes {rank} then downstream sorting
// can preserve the weights determined by the search engine.
function initialItems(mm: MapModel, engine: SearchEngine, term: string|undefined): NamedResult {
  if (!term) {
    return allNamedItems(mm);
  }

  return gather(engine, term, (iri: string) => (
    {iri, name: mm.nameOf(iri)}
  ));
}

export function useFilteredItemsByTerm(term?: string): Readonly<FilterResult> {
  const mm = useModel();
  const engine = useSearchEngine();
  const {filters, view: baseView} = usePlace();
  const labelled = useLabelledItems();
  const relFuncs = useRelations();

  // TODO: not quite the right cmp here, need to work out what sharing a parent means
  const view = baseView != null && mm.ofStoryOf(baseView) || baseView;

  return useMemo(() => {
    const {getRelations, hasAnyRelation, hasCategoricalRelation, someOffPlane} = relFuncs;
    const getTypes = (iri: string) => Object.keys(mm.classesOf(iri));
    const checkNoItemKinds = filters.itemKinds && buildRelated(filters.itemKinds, getRelations, hasCategoricalRelation, false);
    const checkNotType = filters.aType && buildIntersection(filters.aType, getTypes);
    const checkNoRelations = filters.relation && buildRelated(filters.relation, getRelations, hasAnyRelation);

    const filtered: PresentItems = {};
    const rest: PresentItems = {};
    initialItems(mm, engine, term).forEach((item) => {
      const {iri} = item;
      if (excludeByPlottable(filters.plottable, mm, view, iri, labelled, someOffPlane)
        || checkNotType?.(iri)
        || checkNoDomain(filters.domain, mm)?.(iri)
        || checkNoRelations?.(iri)
        || checkNoItemKinds?.(iri)
      ) {
        rest[iri] = item;
        return;
      }
      filtered[iri] = item;
    });
    return {items: filtered, length: Object.keys(filtered).length, remainingSearchedItems: rest};
  }, [relFuncs, filters.itemKinds, filters.aType, filters.relation, filters.plottable, filters.domain, mm, engine, term, view, labelled]);
}

const useFilteredItems = registerCachedHook((): Readonly<FilterResult> => {
  const {filters} = usePlace();
  return useFilteredItemsByTerm(filters.search);
});

export default useFilteredItems;
