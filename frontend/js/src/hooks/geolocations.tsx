// Geo locations of area and data path for things on lenses.

import {useModel} from '../context/triples';
import {IRIS} from '../rdf';
import {partitionSubj} from '../triplejump';

import type {MapModel} from '../rdfmodels';
import type {Paths, Poly} from '../types';


type LensTargetLocation<T> = {
  [lens: string]: {
    [targetIri: string]: T;
  };
};
export type DataPathsByLens = LensTargetLocation<Paths>;
export type AreasByLens = LensTargetLocation<Poly>;
type LocationByLens = {
  'path': DataPathsByLens;
  'area': AreasByLens;
};

function maybeParse<T>(data: string): T|string {
  // Should all be SVG paths now, so don't parse as JSON?
  if (data[0] === 'M') {
    return data;
  }
  try {
    return JSON.parse(data) as T;
  } catch {
    return data;
  }
}

function buildGeoLocationsByLens(mm: MapModel): LocationByLens {
  const geoLocations: LocationByLens = {path: {}, area: {}};

  mm.labels().forEach((labelIRI: string) => {
    const [{
      [IRIS.VM.forThing]: targetIRI,
      [IRIS.VM.forLensDetail]: lensDetail,
      [IRIS.VM.withPoly]: geoPoly,
      [IRIS.VM.withDataPath]: dataPath,
    }, _] = partitionSubj(mm, labelIRI, {});

    const lens = mm.ofLensOf(lensDetail);
    if (lens && targetIRI && geoPoly) {
      const parsed: Poly = maybeParse(geoPoly);
      mm.lensDescendantsOrSelfFor(lens).forEach((cl: string) => {
        const locationByTarget = (geoLocations.area[cl] ??= {});
        locationByTarget[targetIRI] = parsed;
      });
    }
    if (lens && targetIRI && dataPath) {
      const parsed: Paths = maybeParse(dataPath);
      mm.lensDescendantsOrSelfFor(lens).forEach((cl: string) => {
        const locationByTarget = (geoLocations.path[cl] ??= {});
        locationByTarget[targetIRI] = parsed;
      });
    }
  });

  return geoLocations;
}

export function useDataPathsByLens(): Readonly<DataPathsByLens> {
  const mm = useModel();
  return mm._registerCache('geoLocationsByLens', buildGeoLocationsByLens)['path'];
}

export function useAreasByLens(): Readonly<AreasByLens> {
  const mm = useModel();
  return mm._registerCache('geoLocationsByLens', buildGeoLocationsByLens)['area'];
}
