// Effects to animate the map in response to state changes

import {useCallback, useEffect, useRef} from 'preact/hooks';

import {easeIn, easeOut, inAndOut} from 'ol/easing';
import {getCenter, getForViewAndSize} from 'ol/extent';
import MultiPoint from 'ol/geom/MultiPoint';
import Point from 'ol/geom/Point';

import {useHighlighter} from '../context/focus';
import {translateCoord, translateExtent, useMap, useMapStateChange} from '../context/ol';
import {usePlace} from '../context/place';
import {usePlatformStates} from '../context/platform';
import {useModel} from '../context/triples';
import {getPaddingWidestPanelLeftOffset} from '../functions/mapAnimations';
import {boundingBox} from '../functions/svgpaths';
import {useScreenProperties} from '../context/screen';
import {lensFromLocation} from '../triplejump';
import {useAreasByLens} from './geolocations';
import {useLabelsAtLowestStack} from './labels';
import {getlensDetailMaxZoom, lensStack} from './lenses';
import {useDefer} from './useDefer';
import {useMapItemsAtLensStack} from './useMapItems';

import type {Extent} from 'ol/extent';

import type {Coord, ViewportTarget} from '../context/ol';
import type {HintName} from '../types';

export const ANIMATE_DURATION = 400;

// TODO: Take from map config or derive?
export const ZOOM_MARKER = 4.5;
export const ZOOM_PLANE = 4.25;
export const ZOOM_PADDING = 15;
export const ZOOM_DOUBLE_PADDING = 30;

export const ZOOM_RESTRICTED_FLAGS: HintName[] = ['map', 'filter'];

function never(): never {
  throw new Error('unreachable code');
}

export default function useMapAnimations(extent: Extent, shouldFocus: boolean): void {
  const {map, state: {ready}} = useMap();
  const {currentlyAnimating} = useMapStateChange();
  const mm = useModel();
  const {hint, selection: reactiveSelection, view, lensLocation} = usePlace();
  const {setMapHighlights, resetMapHighlights} = useHighlighter();
  const {isLargeScreen} = useScreenProperties();
  const paddingSelectors = '.vm-story-content, .vm-float-selected-item'; // TODO: story width lost in interaction panel
  const {base} = usePlatformStates();

  // Animation logic wants access to the map content, but we don't want to
  // re-animate necessarily if these values change. There's an upcoming
  // 'Effect Event' thing to handle this case, but indirecting through ref
  // works as well for now.
  const mapContentRef = useRef<{
    areas: ReturnType<typeof useAreasByLens>;
    labels: ReturnType<typeof useLabelsAtLowestStack>;
    mapItems: ReturnType<typeof useMapItemsAtLensStack>;
    selection: string | null;
  }>();
  mapContentRef.current = {
    areas: useAreasByLens(),
    labels: useLabelsAtLowestStack(),
    mapItems: useMapItemsAtLensStack(),
    selection: reactiveSelection,
  };
  const lastMapContent = () => mapContentRef.current || never();

  const getTargetForPoint = useCallback((
    center: number[],
    targetZoom: number,
    padding?: Extent,
  ) : ViewportTarget|undefined => {
    const view = map.getView();
    const viewportSize = map.getSize() || [0, 0];
    const targetResolution = view.getResolutionForZoom(targetZoom);
    // target viewport center is the depadded target center
    if (padding) {
      const deltaX = (padding[1] - padding[3]) * targetResolution / 2;
      const deltaY = (padding[0] - padding[2]) * targetResolution / 2;
      center = [center[0] + deltaX, center[1] + deltaY];
    }
    // TODO: what if targetZoom cannot go beyond view extent?
    const targetExtent = getForViewAndSize(center, targetResolution, 0, viewportSize);
    return {extent: targetExtent, scale: 1 / targetResolution};
  }, [map]);

  const getTargetForExtent = useCallback((
    geometryExtent: Extent,
    maxZoom: number,
    padding?: Extent,
  ) : ViewportTarget|undefined => {
    // use same way to calculate targetExtent in view.fitInternal
    const view = map.getView();
    const currentZoom = view.getZoom();
    const viewportSize = map.getSize() || [0, 0];
    const minResolution = view.getResolutionForZoom(maxZoom);
    const size = padding ? [
      viewportSize[0] - padding[1] - padding[3],
      viewportSize[1] - padding[0] - padding[2],
    ] : viewportSize;
    const expectedResolution = view.getResolutionForExtent(geometryExtent, size);
    const targetResolution = Math.max(expectedResolution, minResolution);
    const targetZoom = view.getZoomForResolution(targetResolution) || 0;
    if (currentZoom && targetZoom - currentZoom <= 1) {
      return undefined;
    }
    const center = getCenter(geometryExtent);
    return getTargetForPoint(center, targetZoom, padding);
  }, [map, getTargetForPoint]);

  const zoomTo = useDefer(useCallback((point: [number, number], zoom: number, shouldAnimate: boolean = true) => {
    const view = map.getView();
    const center = translateCoord(point);
    const currentZoom = view.getZoom() || 0;
    // Max zoom set to view's max zoom as opposed to zoomToRegion's.
    // This is to deal with org map where markers appear at zoom 6.5, and max zoom 7 for view.
    const maxZoom = view.getMaxZoom();
    zoom = Math.min(zoom, maxZoom);
    const centerGeometry = new Point(center);
    const padding = getPaddingWidestPanelLeftOffset(paddingSelectors, isLargeScreen);
    if (!shouldAnimate) {
      view.fit(centerGeometry, {maxZoom: zoom, padding});
      return;
    }
    const target = getTargetForPoint(center, zoom, padding);
    if (currentZoom >= zoom) {
      const resolution = view.getResolutionForZoom(zoom);
      const deltaX = (padding[1] - padding[3]) * resolution / 2;
      const deltaY = (padding[0] - padding[2]) * resolution / 2;
      const paddedCenter = [center[0] + deltaX, center[1] + deltaY];
      view.animate(
        {duration: ANIMATE_DURATION, center: paddedCenter},
        () => currentlyAnimating(false),
      );
      view.animate(
        {duration: ANIMATE_DURATION / 2, zoom: zoom - 0.25, easing: easeIn},
        {duration: ANIMATE_DURATION / 2, zoom, easing: easeOut},
        () => currentlyAnimating(false),
      );
      currentlyAnimating(true, target);
    } else {
      // TODO: Find better way of firing animation done than setTimeout
      view.fit(centerGeometry, {duration: ANIMATE_DURATION, easing: inAndOut, maxZoom: zoom, padding, callback: () => setTimeout(() => {
        currentlyAnimating(false);
      }, ANIMATE_DURATION)});
      const target = getTargetForPoint(center, zoom, padding);
      currentlyAnimating(true, target);
    }
  }, [currentlyAnimating, isLargeScreen, map, getTargetForPoint]), base, []);

  const zoomToRegion = useDefer(useCallback((region: [number, number][], shouldAnimate: boolean = true) => {
    const view = map.getView();
    const geometry = new MultiPoint(region.map(translateCoord));
    const padding = getPaddingWidestPanelLeftOffset(paddingSelectors, isLargeScreen, ZOOM_DOUBLE_PADDING);
    // Jerk motion animation only works because the duration for the view.animate is less than the duration for the view.fit
    // If the view.fit animation fires then it finishes after the view.animate should finish,
    // and so the view.animate never happens.
    // If there's nothing for view.fit to do (i.e. we're already focused on the item) then it does nothing and view.animate fires
    const zoom = view.getZoom() || 0;
    const maxZoom = view.getMaxZoom() - 1;
    const willAnimate = shouldAnimate && zoom >= 0.25;
    view.fit(geometry, {
      duration: shouldAnimate ? ANIMATE_DURATION : 0,
      maxZoom,
      easing: inAndOut,
      padding,
      callback: () => {
        // TODO: Find better way of firing animation done than setTimeout
        if (shouldAnimate && !willAnimate) {
          setTimeout(() => {
            currentlyAnimating(false);
          }, ANIMATE_DURATION);
        }
      },
    });
    if (willAnimate) {
      view.animate(
        {duration: ANIMATE_DURATION / 2, zoom: zoom - 0.25, easing: easeIn},
        {duration: ANIMATE_DURATION / 2, zoom, easing: easeOut},
        () => currentlyAnimating(false),
      );
    }
    const geometryExtent = geometry.getExtent();
    const target = getTargetForExtent(geometryExtent, maxZoom, padding);
    currentlyAnimating(shouldAnimate, target);
  }, [currentlyAnimating, isLargeScreen, map, getTargetForExtent]), base, []);

  // This logic is tricky. When the view changes, we want to update the map
  // extent to match, animating if the hint suggests we should.
  const lastLensLocation = useRef<null|string|undefined>();
  useEffect(() => {
    if (!ready || lensLocation === lastLensLocation.current && hint.name !== 'reset') {
      return;
    }
    let newExtent = null;
    if (view != null) {
      const minPoint: Coord|null = mm.minGeoPointOf(view);
      const maxPoint: Coord|null = mm.maxGeoPointOf(view);
      if (minPoint && maxPoint) {
        newExtent = translateExtent([...minPoint, ...maxPoint]);
      }
    }
    // Reset view to default viewport if new view's base lens is different
    // to old view's lens and new view has no selection
    function resetToDefaultView() : boolean {
      // TODO: We have an mm.baseLensOf method that should be used here instead.
      const {labels, mapItems, selection} = lastMapContent();
      // do not pop
      const currentBaseLens = lensStack(mm, lensLocation).slice(-1)[0];
      const lastBaseLens = lensStack(mm, lastLensLocation.current || '').slice(-1)[0];
      const newViewHasSelection = selection && (mapItems[selection]?.length || labels[selection]);
      const isFirstLoad = lastBaseLens === undefined;
      const isNewViewStory = view != null && mm.ofStoryOf(view) != null;
      return !isFirstLoad && !isNewViewStory && !newViewHasSelection && currentBaseLens !== lastBaseLens;
    }
    if (lensLocation == null || newExtent == null && lastLensLocation.current === undefined || resetToDefaultView()) {
      newExtent = translateExtent(extent);
    }
    lastLensLocation.current = lensLocation;
    if (newExtent) {
      const olView = map.getView();
      const padding = getPaddingWidestPanelLeftOffset(paddingSelectors, isLargeScreen);
      if (olView.getAnimating()) {
        // TODO: This may cause a race if the callback happens after dispatch
        olView.cancelAnimations();
      }
      // TODO: Find better way of firing animation done than setTimeout
      olView.fit(newExtent, {duration: ANIMATE_DURATION * 2, padding, callback: () => setTimeout(() => {
        currentlyAnimating(false);
      }, ANIMATE_DURATION)});
      const target = getTargetForExtent(newExtent, olView.getMaxZoom(), padding);
      currentlyAnimating(true, target);
    }
  }, [currentlyAnimating, getTargetForExtent, extent, hint.name, ready, map, mm, view, isLargeScreen, lensLocation]);

  useEffect(() => {
    resetMapHighlights();
    const {areas, labels, mapItems} = lastMapContent();
    if (ready && reactiveSelection && !ZOOM_RESTRICTED_FLAGS.includes(hint.name)) {
      if (!mapItems[reactiveSelection]?.length) {
        if (!labels[reactiveSelection]) {
          return;
        }

        // Mix between zooming to labels with lenses in mind and some old maps
        // where only target's point and area locations exist.
        const targetLabels = labels[reactiveSelection];
        if (targetLabels.length === 1) {
          const label = targetLabels[0];

          const lens = lensFromLocation(mm, lensLocation);
          const area = areas[lens]?.[reactiveSelection] || null;
          const boundingPoints = boundingBox(area);
          if (boundingPoints) {
            zoomToRegion(boundingPoints);
            return;
          }

          // for old/test maps purpose, zoom to target's point location before area location
          const targetPoint = mm.geoPointOf(reactiveSelection);
          if (targetPoint) {
            zoomTo(targetPoint, ZOOM_MARKER);
            return;
          }

          const lensDetailZoom = getlensDetailMaxZoom(mm, label.lensDetail) || ZOOM_MARKER;
          zoomTo(label.point, lensDetailZoom);
          return;
        }

        const points = targetLabels.map(l => l.point);
        zoomToRegion(points);
        return;
      }

      const selectedItems = mapItems[reactiveSelection];
      setMapHighlights([reactiveSelection]);
      if (selectedItems?.length === 1) {
        const point = selectedItems[0].point;
        const lensDetail = selectedItems[0].lensDetail;
        const lensDetailZoom = lensDetail && getlensDetailMaxZoom(mm, lensDetail) || ZOOM_MARKER;
        zoomTo(point, lensDetailZoom);
        return;
      }
      const points = selectedItems.map(x => x.point);
      zoomToRegion(points);
    }
  }, [mm, hint, ready, reactiveSelection, resetMapHighlights, setMapHighlights, currentlyAnimating, zoomTo, zoomToRegion, lensLocation]);

  useEffect(() => {
    if (shouldFocus) {
      const view = map.getView();
      const center = view.getCenter() || [0, 0];
      const zoom = ZOOM_PLANE;
      view.animate(
        {duration: ANIMATE_DURATION, zoom, easing: inAndOut},
        () => currentlyAnimating(false),
      );
      const target = getTargetForPoint(center, zoom);
      currentlyAnimating(true, target);
    }
  }, [map, currentlyAnimating, getTargetForPoint, shouldFocus]);
}
