// Hook returning a IRI relation checker with classes of interest on views, maps or storypoints.

/** @jsx h */

import {useModel} from '../context/triples';
import useRelations from './useRelations';

import type {MapModel} from '../rdfmodels';
import type {RelationFunctions} from './useRelations';

type FilterType = 'itemKinds'|'aType';

type Checker = (iri: string) => boolean;
type FilterChecker = {
  checker: Checker;
  type: FilterType;
};

type FilterInfo = {
  iri: string;
  type: FilterType;
};
type FilterFetcher = (iri: string) => FilterInfo|null;

/**
 * Builds a FilterFetcher function for a given map or view.
 *
 * @param {MapModel} mm - The map model containing class and instance information.
 * @param {RelationFunctions} relFuncs - Functions to retrieve relations and check categorical relations.
 * @param {string|null} view - The view or map IRI to filter by, or null for default.
 * @returns {FilterFetcher} A function that takes an IRI and returns filter information or null.
 */
export function buildFetcher(mm: MapModel, relFuncs: RelationFunctions, view: string|null): FilterFetcher {
  const {getRelations, hasCategoricalRelation} = relFuncs;
  const getTypes = (iri: string) => Object.keys(mm.classesOf(iri));

  const checkers = (mm.classOfInterestOf(view) as string[])
    .reduce<{[iri: string]: FilterChecker}>((acc, classIri) => {
      acc[classIri] = mm.isCategoryInstance(classIri)
        ? {
            checker: (iri: string) => {
              const rels = getRelations(iri);
              return !!rels && hasCategoricalRelation(rels, classIri);
            },
            type: 'itemKinds',
          }
        : {
            checker: (iri: string) => getTypes(iri).includes(classIri),
            type: 'aType',
          };
      return acc;
    }, {});

  return (iri: string) => {
    for (const filterClass in checkers) {
      if (checkers[filterClass].checker(iri)) {
        return {iri: filterClass, type: checkers[filterClass].type};
      }
    }
    return null;
  };
}

/**
 * Builds a collection of FilterFetcher functions for all maps, views, and storypoints.
 *
 * @param {MapModel} mm - The map model containing the hierarchical structures.
 * @param {RelationFunctions} relFuncs - Functions to retrieve relations and check categorical relations.
 * @returns {{[mapIri: string]: FilterFetcher}} A mapping of map/view IRIs to their respective FilterFetcher functions.
 */
function buildFilterFetchers(mm: MapModel, relFuncs: RelationFunctions): {[mapIri: string]: FilterFetcher} {
  return [...mm.maps(), ...mm.views(), ...mm.storypoints()]
    .reduce<{[mapIri: string]: FilterFetcher}>((acc, map: string) => {
      acc[map] = buildFetcher(mm, relFuncs, map);
      return acc;
    }, {});
}

/**
 * Provides filter fetchers for all maps, views, and storypoints in the model.
 *
 * A filter fetcher checks if an IRI has a relation with a vm:classOfInterest
 * of the respective map and returns the filter.
 *
 * @returns {{[mapIri: string]: FilterFetcher}} A mapping of map/view IRIs to their respective FilterFetcher functions.
 */
export function useFilterOfInterest(): {[mapIri: string]: FilterFetcher} {
  const mm = useModel();
  const rels = useRelations();

  return mm._registerCache('filterOfInterest', (mm: MapModel) => buildFilterFetchers(mm, rels));
}
