// Facades around RDF triplestores imposing the languages we use
// in this project
//
// Also a 'saver' class for persisting MapModels to the server.

// TODO test everything here to hell

import {isUndefined, isNull, isString, isNumber,
        isBool, isArray, isOfClass, isStringOrNull, isFunction,
        should} from './utils';

import {BASE_LENS} from './constants';
import {Triple, IRIS, PREFIXES} from './rdf';
import {TripleStore}            from './triplestore';
import {
    buildClassChains, buildAncestorLensChains, buildLensChains,
    buildIndexProperties, buildPrimaryProperties,
    buildPropertyChains, buildSubClassForest, classesOf, partitionSubj,
    subjsMatching, locationToLensMapping, thingToLocationsMapping
} from './triplejump'

const VOTE_BODIES = [IRIS.VM.voteOne, IRIS.VM.voteTwo];

const PRED_CAPITAL_EFFECTS = {
  [IRIS.VM.aidsCapitalType]: true,
  [IRIS.VM.harmsCapitalType]: true,
};

export const PREF_PROPERTY = {
    [IRIS.RDF.type]: {
        [IRIS.OWL.ObjectProperty]: true,
        [IRIS.OWL.DatatypeProperty]: true,
    },
};

function coversMapping(mm) {
    return mm._store._triples.reduce((acc, t) => {
        if (t.pred === IRIS.VM.coversClass) {
            mm._chidrenClasses()[t.obj]?.forEach(cls => {
                (acc[cls] ??= []).push(t.subj);
            })
        }
        return acc;
    }, {});
}

function buildAllCategories(mm) {
    const properties = mm._categoricalProperties();
    return mm._store._triples.reduce((acc, t) => {
        if (properties[t.pred] && mm.subjectExists(t.obj)) {
            acc[t.obj] = true;
        }
        return acc;
    }, {});
}

function bidirectionalPredsBuilder(mm) {
    return mm._getEntitiesWithClass(IRIS.OWL.ObjectProperty).reduce((acc, op) => {
        if (mm._getField(op, IRIS.VM.reverseLabel)) {
            acc[op] = true;
        }
        return acc;
    }, {});
}

function tilesMapping(mm) {
    return [...mm.views(), ...mm.storypoints()].reduce((acc, view) => {
        const tiles = mm.mapTilesOf(view);
        if (tiles) {
            acc[view] = tiles;
            // Can we have view looking at the same layer and different tilesSrc?
            const layer = mm._getField(view, IRIS.VM.comment)
            if (layer) {
                acc[layer] = tiles;
            }
        }
        return acc;
    }, []);
}

function domainsMapping(mm) {
    const properties = mm._propertyChains();
    return Object.entries(properties).reduce((acc, [prop, chain]) => {
        if (chain.includes(IRIS.VM.inDomain)) {
            const cls = mm._getField(prop, IRIS.RDFS.range);
            const values = cls && mm._getEntitiesWithClass(cls);
            if (values && values.length) {
                acc[cls] = {
                    values,
                    predicate: prop,
                }
            }
        }
        return acc;
    }, {});
}

function fromBoolLike(value) {
  if (typeof value === 'boolean' || value == null) {
    return value;
  }
  if (typeof value === 'string' && /^(|false|off|no)$/i.test(value)) {
    return false;
  }
  return true;
}

//  ======================================================================
//  == MapModel
//  ==   Used to access an RDF model holding a map in a project.
//  ==   Takes a copy of the triplestore it's constructed with.
//  ==   TODO test all of this

export class MapModel {
    constructor (triples, mapIri) {
        should (isOfClass(TripleStore) (triples));
        this._mapIri = mapIri;
        this._store = triples.clone ();

        this._caches = {};

        this._subscribers   = [];  // call-me-on-change clients
        this._nextSubHandle = 0;   // each client gets a handle
    }


    //  ------------------------------------------------------------
    //  Change subscription

    // Subscribe to have 'fun()' called whenever the model is changed.
    // Returns a handle that can be used to unsubscribe
    onChange (fun) {  // Void->Void
        should (fun);
        const handle = this._nextSubHandle;
        this._subscribers.push ({ handle: handle, cb: fun });
        ++this._nextSubHandle;
        return handle;
    }

    // The reverse of the above. Provide the handle you received.
    unsubscribeOnChange (handle) {
        should (isNumber (handle));
        this._subscribers = this._subscribers.filter (s => s.handle !== handle);
    }

    _notifySubs () {
        this._caches = {};
        this._subscribers.forEach (s => setTimeout(s.cb, 0));
    }

    _registerCache (name, builder) {
        return this._caches[name] ??= builder(this);
    }

    //  ------------------------------------------------------------
    //  GET helper methods

    // Returns an entity's field value or null if none present.
    // Choice undefined if multiple fields with same pred present for subj.
    _getField (subj, pred) {
        should (isString (subj));
        should (isString (pred));
        return this._store.objWithSubjPredOrNull (subj, pred);
    }

    // Returns one or no field without restriction, even if a subj has multiple fields.
    _getPreferredField (subj, pred) {
        should (isString (subj));
        should (isString (pred));
        const triples = this._store.triplesWithSubjPred(subj, pred);
        if (triples.length > 1) {
            const mainType = PREF_PROPERTY[pred] && triples.find(({obj}) => PREF_PROPERTY[pred][obj])?.obj;
            if (mainType != null) {
                return mainType;
            }
        }
        return triples[0]?.obj || null;
    }

    // Returns an array of all IRIs with a 'type' triple giving provided class
    _getEntitiesWithClass (classIRI) {
        should (isString (classIRI));
        return this._store.triplesWithPredObj (IRIS.RDF.type, classIRI)
                   .map (t => t.subj);
    }

    // Returns true if store contains a triple for which fn returns truey
    _hasSomeTriple (fn) {
        return this._store._triples.some(fn);
    }

    _newAnnotationIri (timestamp, creator) {
        const iri = `${PREFIXES.vm}annotations-${timestamp.toISOString()}-${creator}`;
        return iri;
    }

    _predArray (subj, predPrefix) {
        should (isString (subj));
        should (isString (predPrefix));
        return this._store.triplesWithSubj(subj)
            .reduce((acc, t) => {
                if (!t.pred.startsWith(predPrefix)) {
                    return acc;
                }
                const idx = Number(t.pred.substring(predPrefix.length));
                if (isNaN(idx)) {
                    return acc;
                }
                acc.push({idx, obj: t.obj});
                return acc;
            }, [])
            .sort((a, b) => a.idx - b.idx)
            .map(x => x.obj);
    }

    _inPredArray (obj, predPrefix) {
        should (isString (obj));
        should (isString (predPrefix));
        const matches = this._store.triplesWithObj(obj)
            .filter(t => {
                if (!t.pred.startsWith(predPrefix)) {
                    return false;
                }
                const idx = Number(t.pred.substring(predPrefix.length));
                if (isNaN(idx)) {
                    return false;
                }
                return true;
            });
        should(matches.length === 1);
        return matches[0]?.subj
    }

    // Return all entities that have a name
    named () {
        return subjsMatching(this, t => t.pred === IRIS.VM.name || t.pred === IRIS.RDFS.label);
    }

    //  ------------------------------------------------------------
    //  GET all members of an RDFS class

    // Get the IRIs of all Stakeholder's
    stakeholders () { return this._getEntitiesWithClass (IRIS.VM.Stakeholder); }

    // TODO: Change interfaces to be through Expression/subclass etc
    painpoints () { return this._getEntitiesWithClass (IRIS.VM.Painpoint); }
    brightspots () { return this._getEntitiesWithClass (IRIS.VM.Brightspot); }

    views () { return this._getEntitiesWithClass (IRIS.VM.View); }

    maps () {
        return this._getEntitiesWithClass (IRIS.VM.Map)
            .filter(m => this.onLensOf (m));
    }

    mapsCovering (classIRI) {
        return this._registerCache('covers', coversMapping)[classIRI] || [];
    }

    stories () { return this._getEntitiesWithClass (IRIS.VM.story); }

    storypoints () { return this._getEntitiesWithClass (IRIS.VM.storypoint); }

    contentItems () { return this._getEntitiesWithClass (IRIS.VM.ContentItem); }

    labels () { return this._getEntitiesWithClass (IRIS.VM.Label); }

    forThings () { return this._store.triplesWithPred (IRIS.VM.forThing); }

    involvementsOf (iri) {
        return this._store.triplesWithPred (IRIS.VMHE.relatesTo).reduce ((acc, t) => {
            if (t.obj === iri) {
                acc.push(t.subj);
            }
            return acc;
        }, []);
    }

    hasInvolvement (iri) {
        return this._store.triplesWithPred (IRIS.VMHE.hasInvolvement)
            .reduce ((acc, t) => {
            if (t.obj === iri) {
                acc.push(t.subj);
            }
            return acc;
        }, []);
    }

    //  ------------------------------------------------------------
    //  GET common field values
    //    All return null if that iri has no such field

    _classChains () {
        return this._registerCache('classes', buildClassChains);
    }

    _chidrenClasses () {
        return this._registerCache('subClasses', buildSubClassForest);
    }

    _propertyChains () {
        return this._registerCache('properties', buildPropertyChains);
    }

    _primaryProperties () {
        return this._registerCache('primaryProperties', buildPrimaryProperties);
    }

    _indexProperties () {
        return this._registerCache('indexProperties', buildIndexProperties);
    }

    _lensAncestors () {
        return this._registerCache('ancestorLenses', buildAncestorLensChains);
    }

    _categoricalProperties () {
        return Object.values(this._propertyChains()).reduce((acc, chain) => {
            if (chain.includes(IRIS.GIST.isCategorizedBy)) {
                acc[chain[0]] = true;
            }
            return acc;
        }, {});
    }

    allCategories () {
        return this._registerCache('categories', buildAllCategories);
    }

    classesOf (iri, direct = false) {
      return classesOf(this, iri, direct);
    }

    classOf (iri) { 
        return this._getPreferredField(iri, IRIS.RDF.type) ;
    }

    aClassName (cls) {
        // TODO: An abuse of altLabel to smuggle lingusitc context
        const label = this._getField (cls, IRIS.SKOS.altLabel);
        if (label) {
            return label;
        }
        // TODO: This is a cheat don't want to string-interpolate here
        return 'a ' + this.className (cls);
    }

    className (cls) {
        const label = this._getField (cls, IRIS.RDFS.label);
        return label || cls.slice(cls.lastIndexOf('#') + 1 || cls.lastIndexOf('/') + 1);
    }

    classNamePlural (cls) { return this._getField (cls, IRIS.VM.plural) || this.className(cls); }

    reverseClassName (cls) {
        const label = this._getField (cls, IRIS.VM.reverseLabel);
        return label || '?';
    }

    subjectExists (iri) { return this._store.triplesWithSubj(iri).length > 0; }

    nameOf (iri) { return this._getField (iri, IRIS.VM.name) || this._getField (iri, IRIS.RDFS.label); }

    nameOfOrFallback (iri) {
        return this.nameOf(iri) || ('<' + iri.slice(iri.lastIndexOf('#') + 1 || iri.lastIndexOf('/') + 1) + '>');
    }

    descriptionOf (iri) { return this._getField (iri, IRIS.VM.description ) || this._getField (iri, IRIS.SKOS.definition ); }

    // Returns [lat,lon] array if succeeds (assuming json in model is valid)
    geoPointOf (iri) {
        const possStr = this._getField (iri, IRIS.VM.atGeoPoint);
        return possStr ? JSON.parse(possStr) : null;
    }

    // Returns [lat,lon] array if succeeds (assuming json in model is valid)
    minGeoPointOf (iri) {
        const possStr = this._getField (iri, IRIS.VM.minGeoPoint);
        return possStr ? JSON.parse(possStr) : null;
    }

    // Returns [lat,lon] array if succeeds (assuming json in model is valid)
    maxGeoPointOf (iri) {
        const possStr = this._getField (iri, IRIS.VM.maxGeoPoint);
        return possStr ? JSON.parse(possStr) : null;
    }

    orderOf (iri) {
        return this._getField (iri, IRIS.VM.order);
    }

    // Returns display properties of a label as a parsed object.
    parseDisplay (iri) {
        const rawDisplay = this._getField (iri, IRIS.VM.display);
        if (typeof rawDisplay === 'string') {
            // Decoding was not done on model load, so must parse on access.
            return JSON.parse(rawDisplay);
        }
        return rawDisplay;
    }

    // Returns all fonts referred to by labels
    allLabelFonts () {
        return this._store._triples.reduce((acc, t) => {
            if (t.pred === IRIS.VM.display) {
                // Handle both pre-decoded and undecoded display property.
                let m;
                if (typeof t.obj === 'string') {
                    if ((m = /"fontFamily":\s*"([^"]+)/.exec(t.obj))) {
                        acc[m[1]] = true;
                    }
                } else if ((m = t.obj['fontFamily'])) {
                    acc[m] = true;
                }
            } else if (t.pred === IRIS.VM.fontFamily && t.obj) {
                acc[t.obj] = true;
            }
            return acc;
        }, {});
    }

    // a list of all lens nodes on a tree of root
    lensDescendantsOrSelfFor (root) {
        return this._lensAncestors()[root] || [];
    }

    // Returns all views on lens with labels for iri
    locationsFor (iri) {
        return this._registerCache('locations', thingToLocationsMapping)[iri] || [];
    }

    // Returns tiles for layer, view or storypoints
    tilesFor (iri) {
        return this._registerCache('tiles', tilesMapping)[iri] || null;
    }

    parentView (iri) {
        // TODO: Define view relationships other than Storypoint->Story
        return this._getField (iri, IRIS.VM.ofStory);
    }

    filtersForView (iri) {
        do {
            const filters = this._getField (iri, IRIS.VM.useFilters);
            if (filters != null) {
                return filters;
            }
            iri = this.parentView (iri);
        } while (iri != null)
        return null;
    }

    classOfInterestOf (iri) {
        return iri ? this._store.triplesWithSubjPred(iri, IRIS.VM.classOfInterest).map(t => t.obj) : [];
    }

    defaultView () {
        // TODO: Unify this with model way of talking about map properties
        return this._mapIri && this._getField (this._mapIri, IRIS.VM.defaultView);
    }
    navContent () {
        return this._mapIri && this._getField (this._mapIri, IRIS.VM.navContent);
    }
    leafletMapSettings() {
        // return JSON string instead of object to avoid new ref every time we call
        return this._mapIri && this._getField (this._mapIri, IRIS.VM.leafletMapSettings) || '{}';
    }
    mapHasFlagOn (flagName) {
        return this._mapIri && fromBoolLike (this._getField (this._mapIri, PREFIXES.vm + flagName));
    }

    reversedRelationFor (iri) {
        should (iri);
        const bidirectionalPreds = this._registerCache('bidirectionalPreds', bidirectionalPredsBuilder);
        return this._store._triples.reduce((acc, t) => {
            if (t.obj === iri && bidirectionalPreds[t.pred]) {
                acc.push([t.pred, t.subj]);
            }
            return acc;
        }, []);
    }

    trustorOf (iri) { return this._getField(iri, IRIS.VM.trustor); }
    trusteeOf (iri) { return this._getField(iri, IRIS.VM.trustee); }
    trustScoreOf (iri) {
      const score = this._getField(iri, IRIS.VM.trustScore);
      return score == null ? null : +score;
    }
    importanceScoreOf (iri) { return +this._getField(iri, IRIS.VM.importanceScore); }
    trustPerceptionsRelatedTo (iri) {
        return [].concat(
            this._store.triplesWithPredObj(IRIS.VM.trustor, iri).map(x => x.subj),
            this._store.triplesWithPredObj(IRIS.VM.trustee, iri).map(x => x.subj)
        );
    }

    supportedLanguages () {
        const langs = this._mapIri && this._getField (this._mapIri, IRIS.VM.supportedLanguages);
        return langs ? JSON.parse(langs) : [];
    }

    contentItemOf (iri) { return this._getField (iri, IRIS.VM.hasContentItem); }

    emailOf (iri) { return this._getField (iri, IRIS.VM.email); }

    ofStakeholderOf (iri) { return this._getField (iri, IRIS.VM.ofStakeholder); }

    urlOf (iri) { return this._getField (iri, IRIS.VM.url); }

    invitedOnOf (iri) { return this._getField (iri, IRIS.VM.invitedOn); }

    worksIn (iri) { return this._getField (iri, IRIS.VM.worksIn); }

    hasRAGData (iri) { return !!iri && !!this._getField (iri, IRIS.VM.hasRAGData); }

    isGrayscale (iri) { return !!iri && !!this._getField (iri, IRIS.VM.isGrayscale); }

    hasScore (iri) { return this._getField (iri, IRIS.VM.hasScore); }

    hasImpact (iri) { return this._getField (iri, IRIS.VM.hasImpact); }

    opacityOf (iri) { return this._getField (iri, IRIS.VM.opacity); }

    forColorCategoriesOf (iri) { return (iri ? this._store.triplesWithSubjPred(iri, IRIS.VM.forColorCategory) : []).reduce((acc, t) => {
        (acc ??= {})[t.obj] = true;
        return acc;
    }, null);}

    cssVariableOf (iri) { return this._getField (iri, IRIS.VM.cssVariable); }

    hexCodeOf (iri) { return this._getField (iri, IRIS.VM.hexCode); }

    onLensOf (iri) { 
        if (!iri) {
            return this.subjectExists(BASE_LENS) ? BASE_LENS : null;
        }
        return this._getField (iri, IRIS.VM.onLens);
    }

    onLensMapOf (iri) { 
        if (!iri) {
            return this.subjectExists(BASE_LENS) ? BASE_LENS : null;
        }
        return this._getField (iri, IRIS.VM.onLensMap);
    }

    forLensMapOf (iri) { return this._getField (iri, IRIS.VM.forLensMap); }

    detailsOf (iri) { return this._predArray (iri, IRIS.VM.detail); }

    ofLensOf (iri) { return this._getField (iri, IRIS.VM.ofLens); }

    _lensChain(iri) {
        const cache = this._registerCache('lensChain', buildLensChains);
        // TODO: fix empty location (lensFromLocation) to not require BASE_LENS
        if (iri === BASE_LENS && !(iri in cache)) {
            return cache[iri] = this.subjectExists(BASE_LENS) ? [BASE_LENS] : [];
        }
        return cache[iri] || [];
    }

    baseLensOf (iri) {
        return this._lensChain(iri)?.[0] || null;
    }

    lensStackOf (iri) {
        const current = this.onLensOf(iri);
        return this._lensChain(current);
    }

    lensStackOfLens (iri) {
        return this._lensChain(iri);
    }

    zoomRangeOf (iri) { return this._getField (iri, IRIS.VM.zoomRange); }

    parentLensOf (iri) { return this._getField (iri, IRIS.VM.parentLens); }

    showEdgesOf (iri) { return this._getField (iri, IRIS.VM.showEdges); }

    hasGlossOf (iri) { return this._getField (iri, IRIS.VM.hasGloss); }

    primaryCategoryRelationOf (iri) {
        const categories = this._registerCache('primaryCategories', () => ({}));
        if (categories[iri] === undefined) {
            categories[iri] = this.findCategoryRelationOf(iri);
        }
        return categories[iri];
    }

    findCategoryRelationOf (iri) {
        const triple = this._store.triplesWithSubj(iri).find(({pred}) => {
            return (this._propertyChains()[pred] || []).includes(IRIS.VM.hasPrimaryCategory)
        });
        return triple && {category: triple.obj, predicate: triple.pred};
    }

    // Get the first category asset's icon of same predicate or fallback to category icon.
    categoryIconOf (iri, pred) {
        should (isString (iri));
        const asset = pred && this._store.triplesWithSubjPred(iri, IRIS.VM.hasCategoryAsset)
            .find(({obj: asset}) => this._getField(asset, IRIS.VM.forProperty) === pred)
            ?.obj;
        if (asset) {
            return this.iconOf(asset);
        }
        return this.iconOf(iri); 
    }

    categoriesOf (iri) {
        return this._store.triplesWithSubjPred(iri, IRIS.VM.hasCategory).reduce((acc, t) => {
            (acc ??= {})[t.obj] = true;
            return acc;
        }, null);
    }

    isUsedAsCategory (iri) {
        return this._hasSomeTriple(t => t.obj === iri && t.pred === IRIS.VM.hasCategory);
    }

    domains () {
        return this._registerCache('domains', domainsMapping);
    }

    locationLensMap () {
        return this._registerCache('locationLensMap', locationToLensMapping);
    }

    forDomainOf (iri) {
        return iri ? this._store.triplesWithSubjPred(iri, IRIS.VM.forDomain).map(t => t.obj) : [];
    }
    
    selectionModeOf (iri) {
        return this._getField(iri, IRIS.VM.selectionMode);
    }

    /**
     * Due to categories appearing as such only if part of a categorical relation,
     * some might not appear if not object to any.
     * This check relies on the ontological correctness that all categories are
     * subclasses of a Category class.
     */
    isCategoryInstance (iri) {
        const cls = this.classOf(iri);
        const classChain = cls && this._classChains()[cls];
        return !!classChain?.some(c => [IRIS.VM.Category, IRIS.GIST.Category].includes(c));
    }

    // Voting using annotations

    annotationTriples () {
        return this._store.triplesWithSubjOfType(IRIS.OA.Annotation);
    }

    annotationCategories () {
        return this._getEntitiesWithClass (IRIS.VM.AnnotationCategory);
    }

    annotationDetails () {
        const triples = this._store.triplesWithPred (IRIS.OA.hasTarget);
        const annotations = triples.reduce((acc, triple) => {
            const iri = triple.obj;
            if (!this.subjectExists(iri)) {
                return acc;
            }

            if (acc[iri] == null) {
              acc[iri] = {votes: 0, comments: []};
            }

            const motivation = this._getField(triple.subj, IRIS.OA.motivatedBy);
            const body = this._getField (triple.subj, IRIS.OA.hasBody);

            if (motivation === IRIS.OA.assessing) {
                acc[iri].votes += VOTE_BODIES.indexOf(body) + 1;
            }

            if (motivation === IRIS.OA.commenting) {
                const user = this._getField(triple.subj, IRIS.DCTERMS.creator);
                const created = this._getField(triple.subj, IRIS.DCTERMS.created);
                const category = this._getField(triple.subj, IRIS.VM.hasCategory);
                acc[iri].comments.push({body, commentIri: triple.subj, user, created: new Date(created), category});
            }

            return acc;
        }, {});

        Object.keys(annotations).forEach(key => {
            annotations[key].comments.sort((a, b) => a.created - b.created);
        });

        return annotations;
    }

    annotationsFor (iri) {
        const triples = this._store.triplesWithPredObj (IRIS.OA.hasTarget, iri);
        const annotations = triples.reduce((acc, triple) => {
            const motivation = this._getField(triple.subj, IRIS.OA.motivatedBy);
            const body = this._getField (triple.subj, IRIS.OA.hasBody);

            if (motivation === IRIS.OA.assessing) {
                acc.votes += VOTE_BODIES.indexOf(body) + 1;
            }

            if (motivation === IRIS.OA.commenting) {
                const user = this._getField(triple.subj, IRIS.DCTERMS.creator);
                const created = this._getField(triple.subj, IRIS.DCTERMS.created);
                const category = this._getField(triple.subj, IRIS.VM.hasCategory);
                acc.comments.push({body, commentIri: triple.subj, user, created: new Date(created), category});
            }

            return acc;
        }, {votes: 0, comments: []});

        annotations.comments.sort((a, b) => a.created - b.created);
        return annotations;
    }

    votesFor (iri) {
        const annotations = this._store.triplesWithPredObj (IRIS.OA.hasTarget, iri)
        return annotations.reduce((acc, anno) => {
            const body = this._getField (anno.subj, IRIS.OA.hasBody);
            acc += VOTE_BODIES.indexOf(body) + 1;
            return acc;
        }, 0);
    }

    updateVotes (targetIRI, {creator, timestamp, votes}) {
        should (isString (targetIRI));
        const existing = this._store.triplesWithPredObj(IRIS.OA.hasTarget, targetIRI)
            .filter(t => this._getField(t.subj, IRIS.OA.motivatedBy) === IRIS.OA.assessing);
        let annoIRI = existing.length ? existing[0].subj : null;

        if (!votes) {
            if (annoIRI) {
                this.deleteAnnotation(annoIRI);
            }
            return;
        }
        const vote = VOTE_BODIES[votes - 1];

        const changes = [
            {pred: IRIS.DCTERMS.creator, obj: creator},
            {pred: IRIS.DCTERMS.created, obj: timestamp.toISOString()},
            {pred: IRIS.OA.hasBody, obj: vote},
        ];

        if (!annoIRI) {
            annoIRI = this._newAnnotationIri(timestamp, creator);
            changes.push(
                {pred: IRIS.RDF.type, obj: IRIS.OA.Annotation},
                {pred: IRIS.OA.motivatedBy, obj: IRIS.OA.assessing},
                {pred: IRIS.OA.hasTarget, obj: targetIRI},
            );
        }
        this._updateProperties(annoIRI, changes);

        this._notifySubs ();
    }

    updateComment (targetIri, skipDelete, {creator, timestamp, commentIri, body, category}) {
        if (!skipDelete && !body && commentIri) {
            this.deleteAnnotation(commentIri);
            return {commentIri: null, created: null, user: null, body: null};
        } else if (!body && !commentIri) {
            return {commentIri: null, created: null, user: null, body: null};
        }

        const changes = [
            {pred: IRIS.DCTERMS.creator, obj: creator},
            {pred: IRIS.OA.hasBody, obj: body},
            {pred: IRIS.OA.hasTarget, obj: targetIri},
        ];

        if (!commentIri) {
            timestamp = timestamp ?? new Date();
            commentIri = this._newAnnotationIri(timestamp, creator);
            changes.push(
                {pred: IRIS.DCTERMS.created, obj: timestamp.toISOString()},
                {pred: IRIS.RDF.type, obj: IRIS.OA.Annotation},
                {pred: IRIS.OA.motivatedBy, obj: IRIS.OA.commenting},
            );
        }

        if (category) {
            // TODO: use a predicate that's distinct from the one used in model data categories
            changes.push({pred: IRIS.VM.hasCategory, obj: category});
        } else {
            changes.push({pred: IRIS.VM.hasCategory, obj: null});
        }

        this._updateProperties(commentIri, changes);

        this._notifySubs ();

        return {commentIri, created: timestamp, user: creator, body, category};
    }

    commentCategories () {
        const preds = {
            [IRIS.RDF.type]: true,
            [IRIS.VM.display]: true,
            [IRIS.VM.name]: true,
            [IRIS.VM.hasIcon]: true,
            [IRIS.VM.useMask]: true,
            [IRIS.VM.hasAltIcon]: true,
        };
        const categories = this.annotationCategories().map(iri => {
            const [dict, _] = partitionSubj(this, iri, preds, false);
            const {
                [IRIS.VM.display]: helpText,
                [IRIS.VM.name]: name,
                [IRIS.VM.hasIcon]: icon,
                [IRIS.VM.useMask]: useMask,
                [IRIS.VM.hasAltIcon]: altIcon,
              } = dict;

            return {
                name,
                iri,
                helpText,
                icon,
                altIcon,
                isMasked: !useMask || useMask.toLowerCase() === 'true' || useMask.toLowerCase() === '1'
            };
        });

        return categories;
    }

    clearAnnotations () {
      this._getEntitiesWithClass (IRIS.OA.Annotation).forEach (iri => {
        this.deleteEntity (iri);
      });
    }

    deleteAnnotation (iri) {
        should (isString (iri));
        this.deleteEntity (iri);
    }

    //  ------------------------------------------------------------
    //  GET story/storypoint related field values

    followedByOf (iri) { return this._getField (iri, IRIS.VM.followedBy); }

    ofStoryOf (iri) { return this._getField (iri, IRIS.VM.ofStory); }

    
    //  ------------------------------------------------------------
    //  SET common field values

    putUrlOf (iri, url) {
        should (isString (iri));
        should (isString (url));
        this._store.put (new Triple (iri, IRIS.VM.url, url));
        this._notifySubs ();
    }

    putInvitedOnOf (iri, str) {
        should (isString (iri));
        should (isString (str));
        this._store.put (new Triple (iri, IRIS.VM.invitedOn, str));
        this._notifySubs ();
    }

    putMinGeoPointOf (iri, coords) {
        should (isString (iri));
        
        should (isArray (coords));
        should (coords.length == 2);
        should (isNumber (coords[0]));
        should (isNumber (coords[1]));

        this._store.deleteTriplesSubjPred (iri, IRIS.VM.minGeoPoint);
        this._store.put (new Triple (iri, IRIS.VM.minGeoPoint,
                                     JSON.stringify (coords)));

        this._notifySubs ();
    }

    putMaxGeoPointOf (iri, coords) {
        should (isString (iri));
        
        should (isArray (coords));
        should (coords.length == 2);
        should (isNumber (coords[0]));
        should (isNumber (coords[1]));

        this._store.deleteTriplesSubjPred (iri, IRIS.VM.maxGeoPoint);
        this._store.put (new Triple (iri, IRIS.VM.maxGeoPoint,
                                     JSON.stringify (coords)));

        this._notifySubs ();
    }


    //  ------------------------------------------------------------
    //  GET painpoint-related field values; null if no triple present

    capitalTypeOf (iri) {
        return this._store._triples.reduce ((acc, t) => {
            if (t.subj === iri && PRED_CAPITAL_EFFECTS[t.pred]) {
                should (isNull (acc));
                acc = t.obj;
            }
            return acc;
        }, null);
    }

    uuidOf (iri) { return this._getField (iri, IRIS.VM.hasUUID); }

    //  ------------------------------------------------------------
    //  GET story/storypoint related values

    // Get first storypoint of story at 'iri', or null if has none
    firstPointOf (iri) { return this._getField (iri, IRIS.VM.firstPoint); }

    // If multiple storypoints are follwed by 'iri' (bad model), choice undefined
    storypointFollowedBy (iri) {
        should (isString (iri));
        const spts = this.storypoints ();
        const valids = this._store.triplesWithPredObj (IRIS.VM.followedBy, iri)
                           .map (t => t.subj)
                           .filter (iri => spts.includes (iri));
        return valids.length ? valids[0] : null;
    }

    // Follows the story chain to the end, returning the IRI of the last
    // storypoint. Returns null if story has no points in chain.
    lastStorypointOfStory (iri) {
        should (isString (iri));

        let curSpt  = this.firstPointOf (iri);
        if (isNull(curSpt)) return null;
        let nextSpt = this.followedByOf (curSpt);

        while (! isNull(nextSpt)) {
            curSpt  = nextSpt;
            nextSpt = this.followedByOf (nextSpt);
        }

        return curSpt;
    }

    // Returns an array of all storypoints in the story, in first to last order
    storyStorypointChain (storyIRI) {
        should (isString (storyIRI));
        const res = [];
        
        let curPt = this.firstPointOf (storyIRI);
        while (curPt) {
            res.push (curPt);
            curPt = this.followedByOf (curPt);
        }
        
        return res;
    }


    //  ------------------------------------------------------------
    //  Change story chains

    // Remove one storypoint from a chain, keeping the chain valid
    deleteStorypointKeepingChain (storypointIRI) {
        should (isString (storypointIRI));
        const prev  = this.storypointFollowedBy (storypointIRI);
        const aft   = this.followedByOf (storypointIRI);
        const story = this.ofStoryOf (storypointIRI);

        // Clean up the chain
        if (!prev && !aft) {
            this.updateStory (story, {firstPoint: null});
        } else
        if (!prev &&  aft) {
            this.updateStory (story, {firstPoint: aft});
        } else
        if ( prev && !aft) {
            this.updateStorypoint (prev, {followedBy: null});
        } else
        if ( prev &&  aft) {
            this.updateStorypoint (prev, {followedBy: aft});
        }
        
        this.deleteStorypoint (storypointIRI);
    }

    // Move a storypoint one step down the story chain
    // Throws if it's already at the end
    moveStorypointForward (storypointIRI) {
        should (isString (storypointIRI));
        
        const curNext     = this.followedByOf (storypointIRI);
        const curPrev     = this.storypointFollowedBy (storypointIRI);
        should (isString (curNext));  // can't move past end
        const curNextNext = this.followedByOf (curNext);

        if (curPrev) {  // not cur first point of chain
            this.updateStorypoint (curPrev, {followedBy: curNext});
        } else {
            const story = this.ofStoryOf (storypointIRI);
            this.updateStory (story, {firstPoint: curNext});
        }

        this.updateStorypoint (curNext,       {followedBy: storypointIRI});
        this.updateStorypoint (storypointIRI, {followedBy: curNextNext});
    }
    
    // Move a storypoint one step back through the story chain
    // Throws if it's already at the beginning
    moveStorypointBackwards (storypointIRI) {
        should (isString (storypointIRI));
        
        const curNext     = this.followedByOf (storypointIRI);
        const curPrev     = this.storypointFollowedBy (storypointIRI);
        should (isString (curPrev));  // can't move past beginning
        const curPrevPrev = this.storypointFollowedBy (curPrev);

        this.updateStorypoint (curPrev,       {followedBy: curNext});
        this.updateStorypoint (storypointIRI, {followedBy: curPrev});
        
        if (curPrevPrev) {  // not becomming the first point of story
            this.updateStorypoint (curPrevPrev, {followedBy: storypointIRI});
        } else {  // becoming first point
            const story = this.ofStoryOf (storypointIRI);
            this.updateStory (story, {firstPoint: storypointIRI});
        }
    }

    // Get custom map tiles related to storypoint or whole story
    mapTilesOf (storypointIRI) {
        should (isString (storypointIRI));
        const storypointTiles = this.usesMapTilesOf (storypointIRI);
        if (storypointTiles != null) {
            return storypointTiles;
        }
        const story = this.ofStoryOf (storypointIRI);
        return story && this.usesMapTilesOf (story);
    }

    usesMapTilesOf (iri) {
        should (isString (iri));
        return this._getField (iri, IRIS.VM.usesMapTiles);
    }

    // Give somewhat indeterminate layout property for specific issue
    aspectOf (issueIRI) {
        return this._getField (issueIRI, IRIS.VM.hasAspect);
    }

    // Give icon filename for specific issue
    iconOf (issueIRI) {
        return this._getField (issueIRI, IRIS.VM.hasIcon);
    }

    // Give all issues (arbitrary plottable items) that related to storypoint
    storyIssues (storypointIRI) {
        should (isString (storypointIRI));
        const story = this.ofStoryOf (storypointIRI);
        if (story == null) {
            return [];  // unreachable?
        }
        return this._getEntitiesWithClass (IRIS.VM.Issue)
                   .filter (iri => story === this.ofStoryOf (iri));
    }

    //  ------------------------------------------------------------
    //  GET Document related values

    documentTypeOf (iri) { return this._getField (iri, IRIS.VM.hasDocumentType); }

    canEmbedOf (iri) { return fromBoolLike (this._getField (iri, IRIS.VM.canEmbed)) }

    //  ------------------------------------------------------------
    //  GET ContentItem-realted values

    contentTypeOf (iri) { return this._getField (iri, IRIS.VM.hasContentType); }
    

    //  ------------------------------------------------------------
    //  GET multiple triple values

    // Returns list of IRI strings
    painpointsOfStakeholder (iri) {
        should (isString (iri));
        const isOfSH = (ppIRI) => {
            return this._store.contains (new Triple (
                ppIRI, IRIS.VM.ofStakeholder, iri));
        }
        return this.painpoints ()
                   .filter (iri => isOfSH(iri));
    }

    // Returns list of IRI strings. NB doesn't follow story chain, just finds
    // IRIs stating they're 'of' the provided story
    storypointsOfStory (iri) {
        should (isString (iri));
        const isOfStory = (spIRI) => {
            return this._store.contains (new Triple (
                spIRI, IRIS.VM.ofStory, iri));
        }
        return this.storypoints ()
                   .filter (iri => isOfStory(iri));
    }


    //  ------------------------------------------------------------
    //  GET entities matching predicates

    // Returns the IRI of the content item with hasUuid 'uuid', or null
    // if none exists. Choice is undefined if multiple exist.
    contentItemWithUUID (uuid) {
        should (isString (uuid));
        const cis = this.contentItems ();
        
        for (let i = 0; i < cis.length; ++i)
            if (this.uuidOf(cis[i]) === uuid) return cis[i];
        return null;
    }


    //  ------------------------------------------------------------
    //  GET voting-related values

    voters () { return this._getEntitiesWithClass (IRIS.VM.Voter); }

    campaigns () { return this._getEntitiesWithClass (IRIS.VM.Campaign); }

    votersOfCampaign (iri) {
        should (isString (iri));
        should (this.campaigns().includes(iri));
        return this._store.triplesWithSubjPred (iri, IRIS.VM.hasVoter)
                   .map (t => t.obj);
    }


    //  ------------------------------------------------------------
    //  PUT/UPDATE voting-related values

    putCampaignHasVoter (campaignIRI, voterIRI) {
        this._store.put (new Triple (campaignIRI, IRIS.VM.hasVoter, voterIRI));
    }
    

    // ------------------------------------------------------------
    // CREATE new entities in the store
    //   These find the numerically-next IRI in the normal tree for that
    //   entity type, and insert a rdf:type triple for it, then return
    //   the new IRI

    // Given a set of IRIs and a shared root they're based on, returns the
    // number that should be appended to the root to be one more than the
    // greatest curently existing. Returns 0 if list IRIs list is empty.
    // TODO change the below newXXX() methods to use this.
    _nextNumWithRoot (iris, root) {
        should (isArray  (iris));
        iris.forEach (i => should(isString(i)));
        should (isString (root));
        
        const regStr = '^' + root + '(\\d+)$';
        // Get the num from an IRI
        const getIriNum = (iri) => parseInt (iri.match(regStr) [1]);

        const nums   = iris.map (i => getIriNum(i));
        const maxNum = nums.reduce ((a,b) => (a>b ? a : b), 0);

        return maxNum + 1;
    }

    newStakeholder () {
        const shs = this.stakeholders ();

        // Extracting nums from the sh iri
        const iriNum = (iri) => parseInt (
            iri.match ('^' + PREFIXES.vm + 'stakeholders/(\\d+)$') [1]);
        
        const nums   = shs.map (s => iriNum(s));
        const maxNum = nums.reduce ((a,b) => (a>b ? a : b), 0);
        const newIRI = PREFIXES.vm + 'stakeholders/' + (maxNum+1);
        
        this._store.put (new Triple (newIRI, IRIS.RDF.type, IRIS.VM.stakeholder));
        this._notifySubs ();
        return newIRI;
    }

    // Provide the IRI of the sh you want this to be a child of
    newPainpoint (stakeholderIRI) {
        should (isString (stakeholderIRI));
        const pps = this.painpoints ();

        // Extracting nums from the pp iri
        const iriNum = (iri) => parseInt (
            iri.match ('^' + PREFIXES.vm + 'painpoints/(\\d+)$') [1]);
        
        const nums   = pps.map (s => iriNum(s));
        const maxNum = nums.reduce ((a,b) => (a>b ? a : b), 0);
        const newIRI = PREFIXES.vm + 'painpoints/' + (maxNum+1);
        
        this._store.put (new Triple (newIRI, IRIS.RDF.type, IRIS.VM.painpoint));
        this._store.put (new Triple (newIRI, IRIS.VM.ofStakeholder, stakeholderIRI));
        this._notifySubs ();
        return newIRI;
    }

    newView (viewSlug) {
        const newIRI = PREFIXES.vm + viewSlug;

        this._store.put (new Triple (newIRI, IRIS.RDF.type, IRIS.VM.View));
        this._notifySubs ();
        return newIRI;
    }

    newStory () {
        const root   = PREFIXES.vm + 'stories/';
        const stos   = this.stories ();
        const newNum = this._nextNumWithRoot (stos, root);
        const newIRI = root + newNum;

        this._store.put (new Triple (newIRI, IRIS.RDF.type, IRIS.VM.story));
        this._notifySubs ();
        return newIRI;
    }

    newStorypoint (storyIRI) {
        should (isString (storyIRI));
        const root   = PREFIXES.vm + 'storypoints/';
        const stoPts = this.storypoints ();
        const newNum = this._nextNumWithRoot (stoPts, root);
        const newIRI = root + newNum;

        this._store.put (new Triple (newIRI, IRIS.RDF.type, IRIS.VM.storypoint));
        this._store.put (new Triple (newIRI, IRIS.VM.ofStory, storyIRI));
        this._notifySubs ();
        return newIRI;
    }
        
    newContentItem () {
        const cis = this.contentItems ();

        // Extracting nums from the pp iri
        const iriNum = (iri) => parseInt (
            iri.match ('^' + PREFIXES.vm + 'contentItems/(\\d+)$') [1]);
        
        const nums   = cis.map (s => iriNum(s));
        const maxNum = nums.reduce ((a,b) => (a>b ? a : b), 0);
        const newIRI = PREFIXES.vm + 'contentItems/' + (maxNum+1);

        this._store.put (new Triple (newIRI, IRIS.RDF.type, IRIS.VM.ContentItem));
        this._notifySubs ();
        return newIRI;
    }

    newVoter () {
        const root   = PREFIXES.vm + 'voters/';
        const vs     = this.voters ();
        const newNum = this._nextNumWithRoot (vs, root);
        const newIRI = root + newNum;
                    
        this._store.put (new Triple (newIRI, IRIS.RDF.type, IRIS.VM.Voter));
        this._notifySubs ();
        return newIRI;
    }

    newCampaign () {
        const root   = PREFIXES.vm + 'campaigns/';
        const cs     = this.campaigns ();
        const newNum = this._nextNumWithRoot (cs, root);
        const newIRI = root + newNum;

        this._store.put (new Triple (newIRI, IRIS.RDF.type, IRIS.VM.Campaign));
        this._notifySubs ();
    }

    newCommentCategory (type) {
        const iri = IRIS.VM.AnnotationCategory + '/' + type;
        this._store.put (new Triple(iri, IRIS.RDF.type, IRIS.VM.AnnotationCategory));
        this._notifySubs ();
        return iri;
    }

    //  ------------------------------------------------------------
    //  PUT fields on entity instances
    //    These add or update fields on an existing entity in accordance with
    //    the provided values.
    //    For each present value to set, the model will be left with exactly one
    //    triple with that <subj,pred> pair.
    //    Missing values are unchanged, while present ones are added or updated.

    _updateProperties (iri, changes) {
        changes.forEach (ch => {
            if (isUndefined (ch.obj)) {
                // nop
            } else
            if (isNull (ch.obj)) {
                this._store.deleteTriplesSubjPred (iri, ch.pred);
            } else {
                this._store.deleteTriplesSubjPred (iri, ch.pred);
                const val = isString(ch.obj) ? ch.obj
                          : isNumber(ch.obj) ? (''+ch.obj)
                          : JSON.stringify(ch.obj);
                this._store.put (new Triple (iri, ch.pred, val));
            }
        });
    }

    updateStory (iri, {name, firstPoint}) {
        should (isString (iri));
        should (this.stories().includes (iri));

        // A change is ignored if obj is undef
        const changes = [{pred: IRIS.VM.name,       obj: name},
                         {pred: IRIS.VM.firstPoint, obj: firstPoint}];
        this._updateProperties(iri, changes);

        this._notifySubs ();
    }

    updateStorypoint (iri, {ofStory, description, followedBy, contentItem,
                            minGeoPoint, maxGeoPoint}) {
        should (isString (iri));
        should (this.storypoints().includes (iri));

        // A change is ignored if obj is undef
        const changes = [{pred: IRIS.VM.ofStory,        obj: ofStory},
                         {pred: IRIS.VM.description,    obj: description},
                         {pred: IRIS.VM.followedBy,     obj: followedBy},
                         {pred: IRIS.VM.hasContentItem, obj: contentItem}];
                         // geopoints are handled below
        this._updateProperties(iri, changes);

        // Handle minGeoPoint
        if (isNull (minGeoPoint)) {
            this._store.deleteTriplesSubjPred (iri, IRIS.VM.minGeoPoint);
        } else
        if (minGeoPoint) {
            this.putMinGeoPointOf (iri, minGeoPoint);
        }

        // Handle maxGeoPoint
        if (isNull (maxGeoPoint)) {
            this._store.deleteTriplesSubjPred (iri, IRIS.VM.maxGeoPoint);
        } else
        if (maxGeoPoint) {
            this.putMaxGeoPointOf (iri, maxGeoPoint);
        }

        this._notifySubs ();
    }

    updateVoter (iri, {name, email}) {
        should (isString (iri));
        should (this.voters().includes (iri));

        const changes = [{pred: IRIS.VM.name,  obj: name},
                         {pred: IRIS.VM.email, obj: email}];
        this._updateProperties(iri, changes);

        this._notifySubs ();
    }

    //  ------------------------------------------------------------
    //  Delete entity instances

    deletePainpoint (iri) {
        should (isString (iri));
        this.deleteEntity (iri);
        // notifySubs() is handled by deleteEntity()
    }

    // Deletes all the stakeholder's painpoints first
    deleteStakeholder (iri) {
        should (isString (iri));
        this.painpointsOfStakeholder (iri) .forEach (
            ppIRI => this.deletePainpoint(ppIRI));
        this.deleteEntity (iri);
        // notifySubs() is handled by deleteEntity()
    }

    deleteContentItem (iri) {
        should (isString (iri));
        this.deleteEntity (iri);
        // notifySubs() is handled by deleteEntity()
    }

    // Deletes the named story, and all the story points connected to it
    deleteStory (iri) {
        should (isString (iri));
        const stoPts = this.storypointsOfStory (iri);
        stoPts.forEach (sp => this.deleteStorypoint(sp));
        this.deleteEntity (iri);
        // notifySubs() is handled by deleteEntity();
    }

    // NB Doesn't change the 'followed by' chain
    deleteStorypoint (iri) {
        should (isString (iri));
        this.deleteEntity (iri);
        // notifySubs() is handled by deleteEntity()
    }

    deleteVoter (iri) {
        should (isString (iri));
        should (this.voters().includes (iri));
        this.deleteEntity (iri);
        // notifySubs() is handled by deleteEntity()
    }
    
    // Crude deletion - use one of the above methods if at all possible.
    //   Wipes out any memory of the entity in the store, deleting all
    //   triples which have it for subject, predicate, or object.
    //   Nop if no such triples exist.
    deleteEntity (iri) {
        should (isString (iri));
        this._store.deleteTriplesWithSubj (iri);
        this._store.deleteTriplesWithPred (iri);
        this._store.deleteTriplesWithObj  (iri);
        this._notifySubs ();
    }

    dedupePredicates (preds) {
        const highlanderPredicates = new Set(preds);
        const seenPredicates = new Set();
        const duplicates = this._store._triples.reduce((toRemove, t) => {
            if (highlanderPredicates.has(t.pred)) {
                if (seenPredicates.has(t.pred)) {
                    toRemove.add(t);
                } else {
                    seenPredicates.add(t.pred);
                }
            }
            return toRemove;
        }, new Set());
        this._store.deleteTriplesWithCond(t => duplicates.has(t));
    }

    deleteGeoLocationOfNonLabels () {
        this._store.deleteTriplesWithCond(({subj, pred}) => {
            if (pred === IRIS.VM.atGeoPoly || pred === IRIS.VM.withGeoPath) {
                return this.classOf(subj) !== IRIS.VM.Label;
            }
            return false;
        })
    }
}


// //  ======================================================================
// //  == MapModelSaver - persists MapModel data to the server
// //  ==
// //  ==   handlers : {onSuccess:   XMLHttpRequest->void,
// //  ==               onConflict:  XMLHttpRequest->void,
// //  ==               onError:     XMLHttpRequest->void,
// //  ==               getLastUuid: void->(String|null)}
// //  ==   Silent on http 200 response, calls onConflict on 409, and onError
// //  ==   in any other case. Clears all queued saves if any one save errors.
// //  ==   TODO document getLastUuid

// // TODO: consider merging TripleStoreSaver inside this, or otherwise changing
// // the abstractions. They were originally designed for a suite of web apps,
// // doing more general purpose RDF work, and we know what we need better now.

// export class MapModelSaver {
//     constructor (endpoint, iri, handlers) {
//         enforce (isString (endpoint));
//         enforce (isString (iri));
        
//         enforce (handlers);
//         enforce (isFunction (handlers.onSuccess));
//         enforce (isFunction (handlers.onConflict));
//         enforce (isFunction (handlers.onError));
//         enforce (isFunction (handlers.getLastUuid));
        
//         this._saver = new TripleStoreSaver ({
//             modelsEndpoint: endpoint,
//             modelIRI:       iri,
//             getLastUuid:    handlers.getLastUuid,
//             onSaveSuccess:  handlers.onSuccess,
//             onSaveError:    (xhp) => {
//                                 this._saver.clear ();
//                                   (xhp.status == 200) ? enforce (false)
//                                 : (xhp.status == 409) ? handlers.onConflict (xhp)
//                                 :                       handlers.onError    (xhp);
//                             }
//         });
//     }

//     scheduleSave (mapModel, lastUuid) {
//         enforce (isOfClass(MapModel) (mapModel));
//         this._saver.scheduleSave (mapModel._store, lastUuid);
//     }

//     // _onSaveSuccess () { console.log   ("-- SAVE SUCCESS MapModelSaver"); }
//     // _onSaveError   () { console.error ("-- SAVE ERROR MapModelSaver"); }
// }
