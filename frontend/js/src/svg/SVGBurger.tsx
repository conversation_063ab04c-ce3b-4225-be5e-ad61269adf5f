/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGBurger(props: SVGAttrs): RNode {
  return (
    <svg className="vm-svg" viewBox="0 0 24 24" {...props}>
      <path d="M3 6H21" stroke="currentColor" strokeLinecap="round" />
      <path d="M3 12H21" stroke="currentColor" strokeLinecap="round" />
      <path d="M3 18H21" stroke="currentColor" strokeLinecap="round" />
    </svg>
  );
}
