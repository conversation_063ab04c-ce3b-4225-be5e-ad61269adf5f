/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGSearch(props: SVGAttrs): RNode {
  return (
    <svg className="vm-svg" viewBox="0 0 24 24" {...props}>
      <path d="M2 3H22" stroke="currentColor" />
      <path d="M2 9H8.66667" stroke="currentColor" />
      <path d="M2 15H8.66667" stroke="currentColor" />
      <path d="M2 21H22" stroke="currentColor" />
      <path
        d="M16.0003 14.6667C17.8413 14.6667 19.3337 13.1743 19.3337 11.3333C19.3337 9.49238 17.8413 8 16.0003 8C14.1594 8 12.667 9.49238 12.667 11.3333C12.667 13.1743 14.1594 14.6667 16.0003 14.6667Z"
        stroke="currentColor"
        fill="none"
      />
      <path d="M18.3579 13.6908L21.3339 16.6668" stroke="currentColor" />
    </svg>
  );
}
