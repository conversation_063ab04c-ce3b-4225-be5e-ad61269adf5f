/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGBin(props: SVGAttrs): RNode {
  return (
    <svg className="vm-svg" viewBox="0 0 20 20" fill="none" {...props}>
      <path d="M4 7V16.3333C4 17.8067 4.895 19 6 19H14C15.105 19 16 17.8067 16 16.3333V7" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M3 4H17" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M8 4V2H12V4" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M10 8V15" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M13 8V15" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M7 8V15" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}
