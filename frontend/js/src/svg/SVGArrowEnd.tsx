/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGArrowEnd(props: SVGAttrs): RNode {
  return (
    <svg className="vm-svg" viewBox="0 0 20 20" fill="none" {...props}>
      <path d="M17.0404 1.33499L8.37537 9.99999L17.0404 18.665" stroke="currentColor" strokeLinecap="round" />
      <path d="M2.95972 1.33499V18.665" stroke="currentColor" strokeLinecap="round" />
    </svg>
  );
}
