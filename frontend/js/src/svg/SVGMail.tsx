/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGMail(props: SVGAttrs): RNode {
  return (
    <svg width="0.85em" height="0.85em" viewBox="0 0 24 24" fill="none" {...props}>
      <path d="M4 7.00005L10.2 11.65C11.2667 12.45 12.7333 12.45 13.8 11.65L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <rect x="3" y="5" width="18" height="14" rx="2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
    </svg>
  );
}
