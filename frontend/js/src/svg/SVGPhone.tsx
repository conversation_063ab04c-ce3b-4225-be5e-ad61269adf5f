/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGPhone(props: SVGAttrs): RNode {
  return (
    <svg width="0.8em" height="0.8em" viewBox="0 0 17 17" fill="none" {...props}>
      <path d="M13.2903 15.9755C7.50184 15.8314 1.86873 11.6445 1.02206 4.3353C0.865444 2.98361 1.55184 1.76989 2.79616 1.17646C3.81536 0.690488 5.11107 1.2485 5.4769 2.35721C5.69714 3.02756 5.89657 3.70891 6.04462 4.39757C6.20612 5.14974 5.97121 5.82497 5.36434 6.2963C4.89451 6.66139 4.92754 7.03136 5.12453 7.51001C5.98834 9.60654 7.46146 11.0669 9.58183 11.8899C9.99048 12.0486 10.289 12.0792 10.601 11.6957C11.2275 10.924 12.0423 10.725 12.9906 11.0254C13.4775 11.1792 13.9755 11.2989 14.4637 11.4515C16.0849 11.9583 16.5082 13.625 15.3226 14.8216C14.7292 15.4199 14.2006 16.1428 13.2928 15.9755H13.2903Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}
