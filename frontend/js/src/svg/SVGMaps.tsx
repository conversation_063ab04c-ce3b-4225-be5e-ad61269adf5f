/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGMaps(props: SVGAttrs): RNode {
  return (
    <svg className="vm-svg" viewBox="0 0 49 49" fill="none" {...props}>
      <path d="M2.40161 24.3301L24.0437 36.1984L45.6302 24.3301" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M2.40161 33.7617L24.0437 45.63L45.6302 33.7617" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M24.0437 26.7665L2.40161 14.5841L24.0437 2.40161L45.6302 14.5841L24.0437 26.7665Z" stroke="currentColor" strokeLinejoin="round" />
    </svg>
  );
}
