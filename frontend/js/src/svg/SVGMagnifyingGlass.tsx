/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGMagnifyingGlass(props: SVGAttrs): RNode {
  return (
    <svg className="vm-svg" viewBox="0 0 16 16" fill="none" {...props}>
      <path d="M6.6152 11.2305C9.16421 11.2305 11.2305 9.16421 11.2305 6.6152C11.2305 4.06631 9.16421 2 6.6152 2C4.06634 2 2 4.06631 2 6.6152C2 9.16421 4.06634 11.2305 6.6152 11.2305Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
      <path d="M9.879 9.87939L13.9995 13.9999Z" fill="currentColor" />
      <path d="M9.879 9.87939L13.9995 13.9999" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
    </svg>
  );
}
