/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGSpeechBubble(props: SVGAttrs): RNode {
  return (
    <svg viewBox="0 0 21.89 16.47" {...props}>
      <path
        d="M16.17.45H5.72A5.27,5.27,0,0,0,5.72,11H7v4.46L11.74,11h4.43a5.27,5.27,0,1,0,0-10.54Z"
        fill="#fff"
        stroke="#1a1a1a"
        strokeMiterlimit="10"
        strokeWidth=".89"
      />
    </svg>
  );
}
