/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGInfo(props: SVGAttrs): RNode {
  return (
    <svg className="vm-svg" viewBox="0 0 16 16" fill="none" {...props}>
      <circle cx="8" cy="8" r="7" stroke="currentColor" />
      <path d="M8 8V12" stroke="currentColor" strokeLinecap="round" />
      <circle cx="8" cy="5" r="1" fill="currentColor" />
    </svg>
  );
}
