/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGMapTerms(props: SVGAttrs): RNode {
  return (
    <svg className="vm-svg" viewBox="0 0 20 20" fill="none" {...props} >
      <path d="M1 6.14279H19M1 9.99993H19M1 13.8571H19M7.42857 6.14279V17.7142M13.8571 6.14279V17.7142M19 17.7142H1V2.28564H19V17.7142Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}
