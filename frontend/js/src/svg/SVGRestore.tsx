/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGRestore(props: SVGAttrs): RNode {
  return (
    <svg className="vm-svg" viewBox="0 0 20 20" fill="none" {...props}>
      <path d="M1 10 A9 9 0 1 0 2.2057 5.5" stroke="currentColor" strokeLinecap="round" />
      <path d="M5.2057 5.5 H2.2057 V2.5" stroke="currentColor" strokeLinecap="round" strokeLinejoin="miter" />
      <circle cx="10" cy="10" r="2" stroke="currentColor" />
    </svg>
  );
}
