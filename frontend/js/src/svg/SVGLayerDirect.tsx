/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGLayerDirect(props: SVGAttrs): RNode {
  return (
    <svg className="vm-svg" viewBox="0 0 24 24" {...props}>
      <path stroke="currentColor" fill="none" strokeLinejoin="round" strokeLinecap="round" d="M3 15.36L11 20.32L19 15.36M11 5.68L3 10.72L11 15.68L19 10.72M11 9.68L20.28 3.44M16.28 3L20.28 3L20.28 6.96" />
    </svg>
  );
}
