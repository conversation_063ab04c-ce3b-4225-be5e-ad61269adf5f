/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGHome(props: SVGAttrs): RNode {
  return (
    <svg className="vm-svg" width="25" height="25" viewBox="0 0 25 25" fill="none" {...props}>
      <path d="M8.37 12.97V23.1H1V8.73L12.05 1L23.1 8.73V23.1H15.73V12.97H8.36" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}
