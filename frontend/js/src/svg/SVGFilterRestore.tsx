/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGFilterRestore(props: SVGAttrs): RNode {
  return (
    <svg className="vm-svg" viewBox="0 0 20 20" fill="none" {...props}>
      <path d="M0.9 9 A8.1 8.1 0 0 0 7.5934 16.9769" stroke="currentColor" strokeLinecap="round" />
      <path d="M16.0148 4.95 A8.1 8.1 0 0 0 1.9851 4.95" stroke="currentColor" strokeLinecap="round" />
      <path d="M4.6851 4.95 H1.9851 V2.25" stroke="currentColor" strokeLinecap="round" strokeLinejoin="miter" />
      <path d="M1 1 V2 L5 6 V12 L8 9 V6 L12 2 V1Z" transform="translate(6 7)" className="vm-stroke-sm" strokeWidth="1" stroke="currentColor" strokeLinecap="round" strokeLinejoin="miter" />
    </svg>
  );
}
