/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGSettings(props: SVGAttrs): RNode {
  return (
    <svg className="vm-svg" viewBox="0 0 20 20" {...props}>
      <path d="M9 5L1 5" stroke="currentColor" strokeLinecap="round" />
      <path d="M19 5H16" stroke="currentColor" strokeLinecap="round" />
      <circle r="3" transform="matrix(-1 0 0 1 12.8333 5)" stroke="currentColor" fill="none" />
      <path d="M11 15L19 15" stroke="currentColor" strokeLinecap="round" />
      <path d="M1 15H4" stroke="currentColor" strokeLinecap="round" />
      <circle cx="7.16669" cy="15" r="3" stroke="currentColor" fill="none" />
    </svg>
  );
}
