/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGBubblePlus(props: SVGAttrs): RNode {
  return (
    <svg className="vm-svg" viewBox="0 0 16 16" fill="none" {...props}>
      <path d="M14.5667 1H2.43333C1.91813 1 1.5 1.41813 1.5 1.93333V11.2667C1.5 11.7819 1.91813 12.2 2.43333 12.2H6.16667L8.5 15L10.8333 12.2H14.5667C15.0819 12.2 15.5 11.7819 15.5 11.2667V1.93333C15.5 1.41813 15.0819 1 14.5667 1Z" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M8.5 4.11111V9.16667" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M11.0278 6.63889L5.97222 6.63889" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}
