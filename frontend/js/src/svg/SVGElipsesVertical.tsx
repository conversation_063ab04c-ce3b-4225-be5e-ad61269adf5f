/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGElipsesVertical(props: SVGAttrs): RNode {
  return (
    <svg className="vm-svg" viewBox="0 0 16 16" {...props}>
      <circle cx="8" cy="2" r="2" transform="rotate(90 8 2)" fill="currentColor" />
      <circle cx="8" cy="8" r="2" transform="rotate(90 8 8)" fill="currentColor" />
      <circle cx="8" cy="14" r="2" transform="rotate(90 8 14)" fill="currentColor" />
    </svg>
  );
}
