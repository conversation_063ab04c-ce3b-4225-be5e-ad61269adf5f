/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGCrosshair(props: SVGAttrs): RNode {
  return (
    <svg className="vm-svg" viewBox="0 0 16 16" fill="none" {...props}>
      <circle cx="8" cy="8" r="5" stroke="currentColor" />
      <path d="M5.9 8H1M8 5.9V1M10.1 8H15M8 10.1V15" stroke="currentColor" strokeLinejoin="round" />
    </svg>
  );
}
