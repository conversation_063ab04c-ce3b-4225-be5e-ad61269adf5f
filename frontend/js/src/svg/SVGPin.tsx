/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGPin(props: SVGAttrs): RNode {
  return (
    <svg className="vm-svg" viewBox="0 0 24 24" {...props}>
      <path
        d="M12.6201 22.017L13.2169 22.4713L13.2169 22.4713L12.6201 22.017ZM11.0942 22.017L10.4974 22.4713V22.4713L11.0942 22.017ZM18.9643 8.85714C18.9643 9.57832 18.6931 10.5634 18.1937 11.7319C17.7016 12.8833 17.0189 14.143 16.2653 15.3908C14.7586 17.8853 13.0035 20.2751 12.0233 21.5627L13.2169 22.4713C14.2103 21.1663 16.0023 18.7277 17.5493 16.1663C18.3225 14.8861 19.0433 13.5608 19.573 12.3214C20.0954 11.0992 20.4643 9.88753 20.4643 8.85714H18.9643ZM11.8571 1.75C15.7823 1.75 18.9643 4.93198 18.9643 8.85714H20.4643C20.4643 4.10355 16.6107 0.25 11.8571 0.25V1.75ZM4.75 8.85714C4.75 4.93198 7.93198 1.75 11.8571 1.75V0.25C7.10355 0.25 3.25 4.10355 3.25 8.85714H4.75ZM11.691 21.5627C10.7108 20.2751 8.95564 17.8853 7.44898 15.3908C6.69535 14.143 6.01268 12.8833 5.52058 11.7319C5.02116 10.5634 4.75 9.57832 4.75 8.85714H3.25C3.25 9.88753 3.61891 11.0992 4.14127 12.3214C4.67096 13.5608 5.39179 14.886 6.165 16.1663C7.71203 18.7277 9.50399 21.1663 10.4974 22.4713L11.691 21.5627ZM12.0233 21.5627C11.9358 21.6777 11.7785 21.6777 11.691 21.5627L10.4974 22.4713C11.1853 23.3749 12.529 23.3749 13.2169 22.4713L12.0233 21.5627ZM14.4499 9.00002C14.4499 10.3531 13.353 11.45 11.9999 11.45V12.95C14.1815 12.95 15.9499 11.1815 15.9499 9.00002H14.4499ZM11.9999 6.55002C13.353 6.55002 14.4499 7.64692 14.4499 9.00002H15.9499C15.9499 6.81849 14.1815 5.05002 11.9999 5.05002V6.55002ZM9.54993 9.00002C9.54993 7.64692 10.6468 6.55002 11.9999 6.55002V5.05002C9.81841 5.05002 8.04993 6.81849 8.04993 9.00002H9.54993ZM11.9999 11.45C10.6468 11.45 9.54993 10.3531 9.54993 9.00002H8.04993C8.04993 11.1815 9.81841 12.95 11.9999 12.95V11.45Z"
        fill="currentColor"
      />
    </svg>
  );
}
