/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGPencil(props: SVGAttrs): RNode {
  return (
    <svg className="vm-svg" viewBox="0 0 20 20" fill="none" {...props}>
      <path d="M13.2268 5.04602L15.954 7.77319" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M7.77254 15.9546L17.4267 6.34136C17.7938 5.97429 18 5.47644 18 4.95732C18 4.43821 17.7938 3.94036 17.4267 3.57329C17.0596 3.20622 16.5618 3 16.0427 3C15.5236 3 15.0257 3.20622 14.6586 3.57329L5.04538 13.2275L3 18L7.77254 15.9546Z" stroke="currentColor" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}
