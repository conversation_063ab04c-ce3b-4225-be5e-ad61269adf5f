/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGExternalLink(props: SVGAttrs): RNode {
  return (
    <svg width="0.7em" height="0.7em" viewBox="0 0 17 17" fill="none" {...props}>
      <path d="M6.69277 2.6265H1V16H14.4639V10.1265M8.13859 8.86141L16 1M9.94582 1.09034H16V7.14458" stroke="currentColor" strokeWidth="2" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}
