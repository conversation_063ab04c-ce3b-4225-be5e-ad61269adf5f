/** @jsx h */

import {h} from 'preact';

import type {RNode, SVGAttrs} from '../types';

export default function SVGBubbleText(props: SVGAttrs): RNode {
  return (
    <svg className="vm-svg" viewBox="0 0 26 26" fill="none" {...props}>
      <path d="M24.9827 5.48847C25.0127 8.761 25.0127 12.2587 24.9077 14.8107C24.8028 17.3626 23.1529 19.2241 19.8682 19.2391C13.7938 19.2691 13.4788 19.089 12.6839 19.6594C10.3741 21.3107 8.07934 23.022 5.76956 24.6733C5.03463 25.1987 4.05972 25.2287 4.2697 23.7125C4.44969 22.4065 4.62967 21.1005 4.82465 19.7945C4.89964 19.3292 4.80965 19.104 4.3147 18.9389C2.30488 18.2783 1.015 16.3569 1 14.1502C1 11.4331 1 8.71596 1 5.99886C1 3.22172 3.0998 1 5.75456 1C10.5691 1 15.3686 1 20.1832 1C23.1079 1 24.9677 3.83719 24.9677 5.50348L24.9827 5.48847Z" stroke="currentColor" strokeMiterlimit="2.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M5.5 8.5H20.5" stroke="currentColor" strokeLinecap="round" />
      <path d="M5.5 13H17.5" stroke="currentColor" strokeLinecap="round" />
    </svg>
  );
}
