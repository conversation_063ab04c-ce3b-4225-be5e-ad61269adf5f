// Simple data store for RDF triples
//
// TODO remove all old methods that aren't triple-based, e.g. classesOf

import {isString, isOfClass, enforce, should} from './utils.js';

import {Triple, IRIS, PREFIXES} from './rdf';


//  ======================================================================
//  == Queryable store for triples.
//  ==   Takes trips by ref since they're immutable
//  ==   You can subscribe to be notified of updates by passing a callback to onUpdate

export class TripleStore {
    constructor () {
        this._triples = [];
        this._subjCache = null;
        this._updateCallbacks = [];
        this._nextSubscriberID = 0;  // subscribers return these to unsub
        this._tag = null;  // to help users know what the store is for
    }

    setTag (tag) {
        should (isString (tag));
        this._tag = tag;
    }

    // Clone contents, but not subscriptions
    // Referenced triples are shared with newly created store
    clone () {
        const res = new TripleStore ();
        res._triples = this._triples.slice();
        return res;
    }

    // Returns a json representation of the triples
    serialise () {
        const raws = this._triples.map (t => ({ subj: t.subj,
                                                pred: t.pred,
                                                obj:  t.obj }));
        return JSON.stringify (raws);
    }
    

    //  --------------------------------------------------
    //  Update notification registration
    //    TODO better method names

    // Subscribe to updates. Returns unique subscriber ID.
    onUpdate (cb) {
        const res = this._nextSubscriberID;
        this._updateCallbacks.push ({ id: res, callback: cb });
        ++this._nextSubscriberID;
        return res;
    }
    // Unsubscribe. Pass in your ID.
    unsubscribeOnUpdate (id) {
        should (this._updateCallbacks.map (sub => sub.id)
                                      .includes (id));
        this._updateCallbacks = this._updateCallbacks
                                    .filter (sub => sub.id !== id);
    }

    _notifyClients () {
        this._subjCache = null;
        if (this._updateCallbacks.length === 0)
            return;
        
        console.log ("TRIPLESTORE (" + this._tag + ") doing all clients notify...",
                     this._updateCallbacks,
                     this._triples);
        this._updateCallbacks.forEach (sub => sub.callback());
    }
    

    //  --------------------------------------------------
    //  Store mutation

    // Add a triple to the store if it's not already present
    // TODO type check
    put (triple) {
        should (isOfClass(Triple) (triple));
        
        if (! this.contains (triple))
            this._triples.push (triple);
        
        this._notifyClients ();
    }

    // TODO type check
    putMany (triples) {
        triples.forEach (t => {
            if (! this.contains (t))
                this._triples.push (t)
        });

        this._notifyClients ();
    }

    clear () {
        this._triples = [];
        
        this._notifyClients ();
    }

    // A RawTriple is an simple data object with following structure:
    //   { subj : String, pred : String, obj : String }
    putRawTriple (rawTrip) {
        should (rawTrip);
        
        const trip = new Triple (rawTrip.subj, rawTrip.pred, rawTrip.obj);
        this.put (trip);
        // the put does the notify for us
    }

    putManyRawTriples (rawTriples) {
        should (rawTriples);
        // Bypass any validation of triples when using raw
        rawTriples.forEach (rt => {
            this._triples.push (new Triple (rt.subj, rt.pred, rt.obj));
        });
        this._notifyClients ();
    }

    // Deletes the given triple. Nop if no such triple exists.
    deleteTriple (trip) {
        should (isOfClass(Triple) (trip));
        this._triples = this._triples.filter (t => !t.equals (trip));
        this._notifyClients ();
    }
    // Deletes any triples with the given subject
    deleteTriplesWithSubj (subj) {
        should (isString (subj));
        this._triples = this._triples.filter (t => t.subj !== subj);
        this._notifyClients ();
    }
    // And the given pred
    deleteTriplesWithPred (pred) {
        should (isString (pred));
        this._triples = this._triples.filter (t => t.pred !== pred);
        this._notifyClients ();
    }
    // And the given object
    deleteTriplesWithObj (obj) {
        should (isString (obj));
        this._triples = this._triples.filter (t => t.obj !== obj);
        this._notifyClients ();
    }

    // Deletes triples that satify a condition
    deleteTriplesWithCond (cond) {
        should (typeof cond === 'function');
        this._triples = this._triples.filter (t => !cond(t));
        this._notifyClients ();
    }
    
    // Deletes all triples with both subject and predicate matching
    // the provided values
    // FIXME test this
    deleteTriplesSubjPred (subj, pred) {
        should (isString (subj));
        should (isString (pred));
        
        this._triples = this._triples
                            .filter (t => (   t.subj !== subj
                                           || t.pred !== pred));

        this._notifyClients ();
    }

    // Changes any stored triple that points to oldObj, such that it now
    // points to newObj
    renameTriplesObj (oldObj, newObj) {
        should (isString (oldObj));
        should (isString (newObj));

        const toChange = this._triples.filter (t => t.obj === oldObj);
        const changed  = toChange.map (t => new Triple (t.subj, t.pred, newObj));

        this._triples = this._triples.filter (t => t.obj !== oldObj);
        changed.forEach (t => this._triples.push (t));

        this._notifyClients ();
    }

    // Changes any stored triple with oldSubj as its subj to have newSubj
    renameTriplesSubj (oldSubj, newSubj) {
        should (isString (oldSubj));
        should (isString (newSubj));

        const toChange = this._triples.filter (t => t.subj === oldSubj);
        const changed  = toChange.map (t => new Triple (newSubj, t.pred, t.obj));

        this._triples = this._triples.filter (t => t.subj !== oldSubj);
        changed.forEach (t => this._triples.push (t));

        this._notifyClients ();
    }
        

    //  --------------------------------------------------
    //  Store non-mutating querying

    // Number of triples in store
    size () {
        return this._triples.length;
    }

    _getSubjCache () {
        if (this._subjCache == null) {
            this._subjCache = this._triples.reduce((acc, t) => {
                (acc[t.subj] ??= []).push(t);
                return acc;
            }, {});
        }
        return this._subjCache;
    }

    _bySubj (subj) {
        return (this._subjCache || this._getSubjCache())[subj] || [];
    }

    contains (triple) {
        should (isOfClass(Triple) (triple));
        const same = this._bySubj (triple.subj).filter (t => t.equals (triple));
        return same.length > 0;
    }

    triplesWithSubj (subj) {
        should (isString (subj));
        return this._bySubj (subj);  // Callers must not mutate array...
    }
    triplesWithPred (pred) {
        should (isString (pred));
        return this._triples.filter (t => t.pred === pred);
    }
    triplesWithObj (obj) {
        should (isString (obj));
        return this._triples.filter (t => t.obj === obj);
    }

    triplesWithSubjPred (subj, pred) {
        should (isString (subj));
        should (isString (pred));

        return this._bySubj (subj).filter(t => t.pred === pred);
    }

    triplesWithPredObj (pred, obj) {
        should (isString (pred));
        should (isString (obj));

        return this._triples.filter(t => (t.pred === pred && t.obj === obj));
    }

    triplesWithSubjOfType (type) {
        should (isString (type));
        const subjs = this._triples.reduce((acc, t) => {
            if (t.obj === type && t.pred === IRIS.RDF.type) {
                acc[t.subj] = true;
            }
            return acc;
        });
        return this._triples.filter (t => subjs[t.subj]);
    }

    // Build dictionary of 'subj' if references 'obj' via any 'pred'.
    referencesObj (obj) {
        should (isString (obj));
        return this._triples.reduce((dict, t) => {
            if (t.obj === obj) {
                dict[t.subj] = true;
            }
            return dict;
        }, {});
    }

    // Assuming exactly 1 triple exists with 'subj' and 'pred', returns its obj.
    // Throws if this is not the case.
    objWithSubjPred (subj, pred) {
        should (isString (subj));
        should (isString (pred));
        const trips = this.triplesWithSubjPred (subj, pred);
        should (trips.length === 1);
        return trips[0].obj;
    }
    // As objWithSubjPred(), but returns null if 0 triples with 'subj' and
    // 'pred' exist. Still throws if >1 such triples exist.
    objWithSubjPredOrNull (subj, pred) {
        should (isString (subj));
        should (isString (pred));
        const trips = this.triplesWithSubjPred (subj, pred);
        should (trips.length <= 1);
        if (trips.length === 0) return null;
        else                    return trips[0].obj;
    }
    
    // Returns an array of the unique IRIs of all stored resources which
    // instance vm:Entity, directly or transitively
    allVMEntities () {
        const results = [];

        // All unique subjs in the store
        const subjs = [];
        this._triples.map (t => t.subj)
            .forEach (s => {
                if (! subjs.includes (s))
                    subjs.push (s);
            });
        
        subjs.forEach (s => {
            const classes = this.classesOf (s);
            if (classes.includes (IRIS.vmEntity)) {
                if (! results.includes (s))
                    results.push (s);
            }
        })

        return results;
    }

    // Given the IRI of a Class, returns an array of IRIs of all the (unique)
    // classes that 'iri' subclasses, transitively. Result never includes 'iri'
    // itself. Result is empty if 'iri' is not present in the store.
    superClassesOf (iri) {
        should (isString (iri));
        
        const result = [];
        const directs = this.triplesWithSubjPred (iri, IRIS.subClassOf)
                            .map (t => t.obj)
                            .filter (o => o !== iri);
        directs.forEach (d => {
            if (! result.includes (d))
                result.push (d);
            
            const supDs = this.superClassesOf (d);
            supDs.forEach (sd => {
                if (! result.includes (sd))
                    result.push (sd);
            });
        });
        return result;
    }

    // Returns an array of the IRIs of all (unique) classes instanced by the
    // resource 'iri' or by any transitive subClassOf relationships those classes
    // have to others. Result is empty if 'iri' doesn't appear in the store.
    classesOf (iri) {
        should (isString (iri));
        
        const result = [];
        const directs = this.triplesWithSubjPred (iri, IRIS.type)
                            .map (t => t.obj);
        directs.forEach (d => {
            if (! result.includes (d))
                result.push (d);

            const sups = this.superClassesOf (d);
            sups.forEach (s => {
                if (! result.includes (s))
                    result.push (s);
            });
        });
        return result;
    }
}


//  ======================================================================
//  == Module self tests

export function self_test () {
    console.log ("-- Running triplestore.js self tests...");

    
    //  ------------------------------------------------------------
    //  Some test data we'll use below
    
    function vmIRI (suffix) {
        return PREFIXES.vm + suffix;
    }

    const TEST_MODEL = [
        {
            // People
            subj: vmIRI ("officeElephant"),
            pred: IRIS.type,
            obj:  vmIRI ("Pet")
        }, {
            subj: vmIRI ("joeThePlumber"),
            pred: IRIS.type,
            obj:  vmIRI ("Employee")
        }, {
            // Classes
            subj: vmIRI ("Pet"),
            pred: IRIS.subClassOf,
            obj:  vmIRI ("Employee")
        }, {
            subj: vmIRI ("Employee"),
            pred: IRIS.subClassOf,
            obj:  vmIRI ("Entity")
        },
        // {
        //     subj: vmIRI ("Pet"),
        //     pred: IRIS.subClassOf,
        //     obj:  vmIRI ("Entity")
        // }
    ];
    

    //  ------------------------------------------------------------
    //  Test Triple

    var someTriple = new Triple ("aaa", "bbb", "ccc");

    enforce (someTriple.subj === "aaa");
    enforce (someTriple.pred === "bbb");
    enforce (someTriple.obj  === "ccc");


    //  ------------------------------------------------------------
    //  Test TripleStore

    var store = new TripleStore ();
    enforce (store.size () === 0);

    // Add the first triple

    store.put (someTriple);
    enforce (store.size () === 1);
    enforce (store.contains (someTriple));

    store.put (someTriple);  // put is idempotent
    enforce (store.size () === 1);

    // Now add the whole model

    //TEST_MODEL.forEach (t => store.putRawTriple (t));
    store.putManyRawTriples (TEST_MODEL);
    enforce (store.size() === 1 + TEST_MODEL.length);

    // Check we can do transitive scans for classes instanced by resources

    const oeClasses = store.classesOf (vmIRI ("officeElephant")) .sort();
    enforce (oeClasses.length === 3);
    enforce (oeClasses [0] === vmIRI ("Employee"));
    enforce (oeClasses [1] === vmIRI ("Entity"));
    enforce (oeClasses [2] === vmIRI ("Pet"));

    // And check the 'get all entities' query works

    const allEnts = store.allVMEntities () .sort();
    enforce (allEnts.length === 2);
    enforce (allEnts [0] === vmIRI ("joeThePlumber"));
    enforce (allEnts [1] === vmIRI ("officeElephant"));

    // Check the 'change all triples with obj' mutator works
    
    store.putRawTriple ({ subj: 'someSubj', pred: 'somePred', obj: 'ccc' });
    enforce (store.triplesWithObj ('ccc') .length === 2);
    
    store.renameTriplesObj ('ccc', 'NEW_OBJ');
    enforce (store.triplesWithObj ('ccc') .length === 0);
    {
        const trips = store.triplesWithObj ('NEW_OBJ');
        enforce (trips.length === 2);
        enforce (trips[0] .equals (new Triple ('aaa', 'bbb', 'NEW_OBJ')));
        enforce (trips[1] .equals (new Triple ('someSubj', 'somePred', 'NEW_OBJ')));
    }

    // And similarly the 'change all triples with subj' mutator

    store.putRawTriple ({ subj: 'someSubj', pred: 'aDiffPred', obj: '8989' });
    enforce (store.triplesWithSubj ('someSubj') .length === 2);

    store.renameTriplesSubj ('someSubj', 'aNewSubj');
    enforce (store.triplesWithSubj ('aNewSubj') .length === 2);
    {
        const trips = store.triplesWithSubj ('aNewSubj');
        enforce (trips.length === 2);
        enforce (trips[0] .equals (new Triple ('aNewSubj', 'somePred', 'NEW_OBJ')));
        enforce (trips[1] .equals (new Triple ('aNewSubj', 'aDiffPred', '8989')));
    }

    // Check batch deleting triples works
    // TODO test these:
    // deleteTriplesSubjPred
    // deleteTriplesWithSubj
    // deleteTriplesWithObj

    // All done

    console.log ("-- OK");
}
