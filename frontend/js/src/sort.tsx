// Encapsulates sorting helpers

// Using the same collator instance for similar comparisons drastically increases perf for large collections
const localeInsensitiveCollator = new Intl.Collator(undefined, {sensitivity: 'base'});
export const localeInsensitive = localeInsensitiveCollator.compare;

export type Comparator = (a: string, b: string) => number;

export function defaultCompare<T>(a: T, b: T): number {
  return a < b ? -1 : +(a > b);
}

export function comparatorForLocale(locale: string): Comparator {
  const collator = new Intl.Collator(locale, {numeric: true, sensitivity: 'base'});
  return collator.compare;
}
