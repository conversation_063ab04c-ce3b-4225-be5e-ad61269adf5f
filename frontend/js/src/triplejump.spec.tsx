// Tests for typed operations on triple store model content
import {IRIS} from './rdf';
import {
  buildClassChains, buildIndexProperties, buildPrimaryProperties, buildPropertyChains,
  buildSubClassForest, getPlaneMask, locationToLensMapping, subjsMatching,
} from './triplejump';
import {buildModel} from './testing/model';

import type {MapModel} from './rdfmodels';

describe('buildClassChains', () => {
  const build = (subs: [string, string][]): MapModel => (
    buildModel(subs.map(([subj, obj]) => ({subj, pred: IRIS.RDFS.subClassOf, obj})))
  );

  it('empty model', () => {
    expect(buildClassChains(buildModel([]))).toEqual({});
  });

  it('A sub B', () => {
    const model = build([['A', 'B']]);
    expect(buildClassChains(model)).toEqual({
      'A': ['A', 'B'],
      'B': ['B'],
    });
  });

  it('A sub T, B sub T, C sub T', () => {
    const model = build([['A', 'T'], ['B', 'T'], ['C', 'T']]);
    expect(buildClassChains(model)).toEqual({
      'A': ['A', 'T'],
      'B': ['B', 'T'],
      'C': ['C', 'T'],
      'T': ['T'],
    });
  });

  it('A sub B sub D, C sub D', () => {
    const model = build([['A', 'B'], ['B', 'D'], ['C', 'D']]);
    expect(buildClassChains(model)).toEqual({
      'A': ['A', 'B', 'D'],
      'B': ['B', 'D'],
      'C': ['C', 'D'],
      'D': ['D'],
    });
  });

  it('A sub B sub A', () => {
    const model = build([['A', 'B'], ['B', 'A']]);
    expect(buildClassChains(model)).toEqual({
      // Cycle results in duplicated keys, but not an infinite loop
      'A': ['A', 'B', 'B'],
      'B': ['B', 'B'],
    });
  });
});

describe('buildSubClassForest', () => {
  const build = (subs: [string, string][]): MapModel => (
    buildModel(subs.map(([subj, obj]) => ({subj, pred: IRIS.RDFS.subClassOf, obj})))
  );

  it('empty model', () => {
    expect(buildSubClassForest(buildModel([]))).toEqual({});
  });

  it('A sub D, B sub D, C sub D', () => {
    const model = build([['A', 'D'], ['B', 'D'], ['C', 'D']]);
    expect(buildSubClassForest(model)).toEqual({
      'D': ['D', 'A', 'B', 'C'],
      'A': ['A'],
      'B': ['B'],
      'C': ['C'],
    });
  });

  it('A sub B sub D sub F, E sub F, C sub D', () => {
    const model = build([['A', 'B'], ['B', 'D'], ['D', 'F'], ['E', 'F'], ['C', 'D']]);
    expect(buildSubClassForest(model)).toEqual({
      'F': ['F', 'D', 'B', 'A', 'C', 'E'],
      'E': ['E'],
      'D': ['D', 'B', 'A', 'C'],
      'A': ['A'],
      'B': ['B', 'A'],
      'C': ['C'],
    });
  });

  it('A sub B sub A', () => {
    const model = build([['A', 'B'], ['B', 'A']]);
    expect(buildSubClassForest(model)).toEqual({
      // Broken, but not an infinite loop
      'B': ['B', 'A', 'A'],
      'A': ['A', 'A'],
    });
  });
});

describe('buildPropertyChains', () => {
  it('empty model', () => {
    expect(buildPropertyChains(buildModel([]))).toEqual({});
  });

  it('some predicates', () => {
    const model = buildModel([
      {subj: 'A', pred: 'p1', obj: 'B'},
      {subj: 'C', pred: 'p1', obj: 'B'},
      {subj: 'D', pred: 'p2', obj: 'A'},
    ]);
    expect(buildPropertyChains(model)).toEqual({
      'p1': ['p1'],
      'p2': ['p2'],
    });
  });

  it('ontology predicates', () => {
    const model = buildModel([
      {subj: 'A', pred: IRIS.RDF.type, obj: IRIS.OWL.Class},
      {subj: 'pD', pred: IRIS.RDF.type, obj: IRIS.OWL.DatatypeProperty},
      {subj: 'pO', pred: IRIS.RDF.type, obj: IRIS.OWL.ObjectProperty},
    ]);
    expect(buildPropertyChains(model)).toEqual({
      [IRIS.RDF.type]: [IRIS.RDF.type],
      'pD': ['pD'],
      'pO': ['pO'],
    });
  });

  it('subproperties', () => {
    const model = buildModel([
      {subj: 'A', pred: IRIS.RDFS.subClassOf, obj: 'B'},
      {subj: 'p1', pred: IRIS.RDFS.subPropertyOf, obj: 'p0'},
    ]);
    expect(buildPropertyChains(model)).toEqual({
      [IRIS.RDFS.subClassOf]: [IRIS.RDFS.subClassOf],
      [IRIS.RDFS.subPropertyOf]: [IRIS.RDFS.subPropertyOf],
      'p0': ['p0'],
      'p1': ['p1', 'p0'],
    });
  });
});

describe('buildPrimaryProperties', () => {
  it('empty model', () => {
    expect(buildPrimaryProperties(buildModel([]))).toEqual({});
  });

  it('a primary', () => {
    const model = buildModel([
      {subj: 'pPD', pred: IRIS.RDF.type, obj: IRIS.OWL.DatatypeProperty},
      {subj: 'pPD', pred: IRIS.RDF.type, obj: IRIS.VM.PrimaryProperty},
      {subj: 'pO', pred: IRIS.RDF.type, obj: IRIS.OWL.ObjectProperty},
      {subj: 'C', pred: 'pPD', obj: 'text'},
      {subj: 'C', pred: 'pO', obj: 'O1'},
      {subj: 'C', pred: 'pO', obj: 'O2'},
    ]);
    expect(buildPrimaryProperties(model)).toEqual({
      'pPD': true,
    });
  });

  it('ofStakeholder subproperty', () => {
    const model = buildModel([
      {subj: 'ofStakeholder', pred: IRIS.RDF.type, obj: IRIS.OWL.ObjectProperty},
      {subj: 'ofStakeholder', pred: IRIS.RDF.type, obj: IRIS.VM.PrimaryProperty},
      {subj: 'ofInternalStakeholder', pred: IRIS.RDFS.subPropertyOf, obj: 'ofStakeholder'},
      {subj: 'C', pred: 'ofInternalStakeholder', obj: 'IS'},
    ]);
    expect(buildPrimaryProperties(model)).toEqual({
      'ofStakeholder': true,
      'ofInternalStakeholder': true,
    });
  });

  it('subproperty of subclass of primary', () => {
    const model = buildModel([
      {subj: 'http://onto.test/KeyProperty', pred: IRIS.RDFS.subClassOf, obj: IRIS.VM.PrimaryProperty},
      {subj: 'hasKey', pred: IRIS.RDF.type, obj: 'http://onto.test/KeyProperty'},
      {subj: 'firstKey', pred: IRIS.RDFS.subPropertyOf, obj: 'hasKey'},
    ]);
    expect(buildPrimaryProperties(model)).toEqual({
      'hasKey': true,
      'firstKey': true,
    });
  });
});

describe('buildIndexProperties', () => {
  it('empty model', () => {
    expect(buildIndexProperties(buildModel([]))).toEqual({});
  });

  it('a index property', () => {
    const model = buildModel([
      {subj: 'pPD', pred: IRIS.RDF.type, obj: IRIS.OWL.DatatypeProperty},
      {subj: 'pPD', pred: IRIS.RDF.type, obj: IRIS.VM.IndexProperty},
      {subj: 'pO', pred: IRIS.RDF.type, obj: IRIS.OWL.ObjectProperty},
      {subj: 'C', pred: 'pPD', obj: 'text'},
      {subj: 'C', pred: 'pO', obj: 'O1'},
      {subj: 'C', pred: 'pO', obj: 'O2'},
    ]);
    expect(buildIndexProperties(model)).toEqual({
      'pPD': true,
    });
  });

  it('ofStakeholder subproperty', () => {
    const model = buildModel([
      {subj: 'ofStakeholder', pred: IRIS.RDF.type, obj: IRIS.OWL.ObjectProperty},
      {subj: 'ofStakeholder', pred: IRIS.RDF.type, obj: IRIS.VM.IndexProperty},
      {subj: 'ofInternalStakeholder', pred: IRIS.RDF.type, obj: IRIS.OWL.DatatypeProperty},
      {subj: 'ofInternalStakeholder', pred: IRIS.RDFS.subPropertyOf, obj: 'ofStakeholder'},
      {subj: 'C', pred: 'ofInternalStakeholder', obj: 'IS'},
    ]);
    expect(buildIndexProperties(model)).toEqual({
      'ofStakeholder': true,
      'ofInternalStakeholder': true,
    });
  });

  it('subproperty of subclass of index property', () => {
    const model = buildModel([
      {subj: 'http://onto.test/IndexProperty', pred: IRIS.RDFS.subClassOf, obj: IRIS.VM.IndexProperty},
      {subj: 'hasKey', pred: IRIS.RDF.type, obj: 'http://onto.test/IndexProperty'},
      {subj: 'firstKey', pred: IRIS.RDF.type, obj: IRIS.OWL.ObjectProperty},
      {subj: 'firstKey', pred: IRIS.RDFS.subPropertyOf, obj: 'hasKey'},
    ]);
    expect(buildIndexProperties(model)).toEqual({
      'hasKey': true,
      'firstKey': true,
    });
  });
});

// Validating mask relies on internal bitfield info that callers won't need
test.each([
  [1, false, false, 8],
  [1, true, true, 11],
  [2, false, true, 18],
  [1 | 2, true, false, 25],
])('getPlaneMask(%s, %s, %s)', (plane, outgoing, incoming, expected) => {
  expect(getPlaneMask(plane, outgoing, incoming)).toEqual(expected);
});

describe('subjsMatching', () => {
  it('empty model', () => {
    expect(subjsMatching(buildModel([]), _ => true)).toEqual([]);
  });

  it('match or not', () => {
    const model = buildModel([
      {subj: 'A', pred: 'p', obj: 'x'},
      {subj: 'B', pred: 'p', obj: 'y'},
    ]);
    expect(subjsMatching(model, t => t.obj === 'y')).toEqual(['B']);
  });

  it('match many', () => {
    const model = buildModel([
      {subj: 'A', pred: 'p', obj: 'x'},
      {subj: 'A', pred: 'p', obj: 'y'},
      {subj: 'B', pred: 'p', obj: 'y'},
      {subj: 'B', pred: 'q', obj: 'y'},
    ]);
    expect(subjsMatching(model, t => t.obj === 'y')).toEqual(['A', 'B']);
  });
});

describe('locationsToLensesMapping', () => {
  it('locations are mapped to lenses correctly using lensmap data', () => {
    const model = buildModel([
      {subj: 'view', pred: IRIS.VM.onLensMap, obj: 'lensMap'},
      {subj: 'view', pred: IRIS.VM.onLens, obj: 'lensDefault'},
      {subj: 'lensA', pred: IRIS.VM.parentLens, obj: 'parentLensA'},
      {subj: 'lensB', pred: IRIS.VM.parentLens, obj: 'parentLensB'},
      {subj: 'lensNodeA', pred: IRIS.RDF.type, obj: IRIS.VM.LensMapNode},
      {subj: 'lensNodeA', pred: IRIS.VM.forDomain, obj: 'domainA'},
      {subj: 'lensNodeA', pred: IRIS.VM.forDomain, obj: 'domainB'},
      {subj: 'lensNodeA', pred: IRIS.VM.onLens, obj: 'lensA'},
      {subj: 'lensNodeA', pred: IRIS.VM.forLensMap, obj: 'lensMap'},
      {subj: 'lensNodeB', pred: IRIS.RDF.type, obj: IRIS.VM.LensMapNode},
      {subj: 'lensNodeB', pred: IRIS.VM.forDomain, obj: 'domainB'},
      {subj: 'lensNodeB', pred: IRIS.VM.onLens, obj: 'lensB'},
      {subj: 'lensNodeB', pred: IRIS.VM.forLensMap, obj: 'lensMap'},
    ]);
    const expectedMapping = {
      'view,domainA,domainB': 'lensA',
      'view,domainB': 'lensB',
      'view': 'lensDefault',
    };
    expect(locationToLensMapping(model)).toEqual(expectedMapping);
  });
  it('nodes without domains do not overwrite view lenses', () => {
    const model = buildModel([
      {subj: 'view', pred: IRIS.VM.onLensMap, obj: 'lensMap'},
      {subj: 'view', pred: IRIS.VM.onLens, obj: 'lensDefault'},
      {subj: 'lensNodeA', pred: IRIS.RDF.type, obj: IRIS.VM.LensMapNode},
      {subj: 'lensNodeA', pred: IRIS.VM.onLens, obj: 'lensA'},
      {subj: 'lensNodeA', pred: IRIS.VM.forLensMap, obj: 'lensMap'},
    ]);
    const expectedMapping = {
      'view': 'lensDefault',
    };
    expect(locationToLensMapping(model)).toEqual(expectedMapping);
  });
});
