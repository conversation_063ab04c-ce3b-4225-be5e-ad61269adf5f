// Changing content of triple store after load

import flag from './flags';
import {IRIS} from './rdf';
import {localeInsensitive} from './sort';
import {partitionSubj} from './triplejump';

import {ANNOTATION_CATEGORY_DEFAULT, BASE_DETAIL, BASE_LENS} from './constants';

import type {Existence, MapData, Term} from './types';
import type {MapModel} from './rdfmodels';
import type {Triple} from './rdf';

type ViewProps = {name: string; desc: null|string; filters: null|string};

function newView(mm: MapModel, slug: string, {name, desc, filters}: ViewProps): string {
  const iri = mm.newView(slug);
  mm._updateProperties(iri, [
    {pred: IRIS.VM.name, obj: name},
    {pred: IRIS.VM.description, obj: desc},
    ...(filters == null ? [] : [{pred: IRIS.VM.useFilters, obj: filters}]),
  ]);
  return iri;
}

function addStakeholderOrder(mm: MapModel, stakeholders: string[]) {
  // TODO: decide how siblings should be sorted
  const sort = (a: string, b: string) => localeInsensitive((mm.nameOf(a) || '?'), (mm.nameOf(b) || '?'));
  let currentOrder = 0;
  function walkNarrower(mm: MapModel, iri: string) {
    const narrower = mm._store.triplesWithPredObj(IRIS.VM.broader, iri);
    if (narrower.length !== 0) {
      narrower.sort((a: Triple, b: Triple) => sort(a.subj, b.subj));
      narrower.forEach((t: Triple) => {
        const order = ++currentOrder;
        mm._updateProperties(t.subj, [{pred: IRIS.VM.order, obj: order}]);
        walkNarrower(mm, t.subj);
      });
    }
  }

  stakeholders.sort(sort);
  stakeholders.forEach(iri => {
    const broader = mm._store.triplesWithSubjPred(iri, IRIS.VM.broader);
    if (broader.length === 0) {
      mm._updateProperties(iri, [{pred: IRIS.VM.order, obj: ++currentOrder}]);
      walkNarrower(mm, iri);
    }
  });
}

type CommentCategoryProps = {type: string; name: string; iconUrl: string; altIconUrl?: string; display?: string; hasCategory?: string};

function addCommentCategory(mm: MapModel, props: CommentCategoryProps) {
  const iri = mm.newCommentCategory(props.type);
  const triples = [
    {pred: IRIS.VM.name, obj: props.name},
    {pred: IRIS.VM.hasIcon, obj: props.iconUrl},
    props.altIconUrl && {pred: IRIS.VM.useMask, obj: 1},
    props.altIconUrl && {pred: IRIS.VM.hasAltIcon, obj: props.altIconUrl},
    props.display && {pred: IRIS.VM.display, obj: props.display},
    props.hasCategory && {pred: IRIS.VM.hasCategory, obj: props.hasCategory},
  ].filter(Boolean);

  mm._updateProperties(iri, triples);
}

function addPrimaryProperty(mm: MapModel, iri: string) {
  mm._updateProperties(iri, [
    {pred: IRIS.RDF.type, obj: IRIS.VM.PrimaryProperty},
  ]);
  mm._updateProperties(IRIS.VM.PrimaryProperty, [
    {pred: IRIS.RDF.type, obj: IRIS.OWL.Class},
    {pred: IRIS.RDFS.label, obj: 'PrimaryProperty'},
  ]);
}

function addExpressionSubclass(mm: MapModel, iri: string, name: string) {
  mm._updateProperties(iri, [
    {pred: IRIS.RDF.type, obj: IRIS.OWL.Class},
    {pred: IRIS.VM.name, obj: name},
    {pred: IRIS.RDFS.subClassOf, obj: IRIS.VM.Expression},
  ]);
  mm._updateProperties(IRIS.VM.Expression, [
    {pred: IRIS.RDF.type, obj: IRIS.OWL.Class},
    {pred: IRIS.RDFS.label, obj: 'Expression'},
    {pred: IRIS.SKOS.altLabel, obj: 'an Expression'},
  ]);
}

const categoricalPredicates: Readonly<Existence> = {
  [IRIS.VM.aidsCapitalType]: true,
  [IRIS.VM.harmsCapitalType]: true,
  [IRIS.VM.hasContentType]: true,
  [IRIS.VM.categorizedBy]: true,
  [IRIS.VM.categorisedBy]: true,
  [IRIS.VM.hasCategory]: true,
  [IRIS.VM.activityType]: true,
  [IRIS.VM.capabilityDefinition]: true,
  [IRIS.VM.ofGood]: true,
  [IRIS.VM.hasPrimaryCategory]: true,
};

function ontologyForCategoricalProperties(mm: MapModel) {
  const props = mm._propertyChains();
  for (const pred in categoricalPredicates) {
    if (pred in props) {
      mm._updateProperties(pred, [
        {pred: IRIS.RDFS.subPropertyOf, obj: IRIS.GIST.isCategorizedBy},
      ]);
    }
  }
}

function fixOntologyCapitalTypes(mm: MapModel) {
  const typeClass = {pred: IRIS.RDF.type, obj: IRIS.GIST.Category};
  mm._updateProperties(IRIS.VM.CAPTYPE.social, [
    {pred: IRIS.RDFS.label, obj: 'Social Capital'},
    typeClass,
  ]);
  mm._updateProperties(IRIS.VM.CAPTYPE.sharedFin, [
    {pred: IRIS.RDFS.label, obj: 'Financial Capital'},
    typeClass,
  ]);
  mm._updateProperties(IRIS.VM.CAPTYPE.human, [
    {pred: IRIS.RDFS.label, obj: 'Human Capital'},
    typeClass,
  ]);
  mm._updateProperties(IRIS.VM.CAPTYPE.environ, [
    {pred: IRIS.RDFS.label, obj: 'Natural Capital'},
    typeClass,
  ]);
}

function fixOntologyContentTypes(mm: MapModel) {
  const typeClass = {pred: IRIS.RDF.type, obj: IRIS.GIST.Category};
  mm._updateProperties(IRIS.VM.CONTENTTYPE.image, [
    {pred: IRIS.RDFS.label, obj: 'Image'},
    typeClass,
  ]);
  mm._updateProperties(IRIS.VM.CONTENTTYPE.video, [
    {pred: IRIS.RDFS.label, obj: 'Video'},
    typeClass,
  ]);
}

function addDefaultAnnotationCategory(mm: MapModel) {
  const categories: string[] = mm.annotationCategories();
  if (!categories.includes(ANNOTATION_CATEGORY_DEFAULT)) {
    addCommentCategory(mm, {
      type: 'default',
      name: 'General Comment',
      display: 'I have a comment…',
      iconUrl: '//opatlas-live.s3.amazonaws.com/icons/Comment-Default.svg',
    });
  }
}

function labelLayersToLens(mm: MapModel) {
  type LabelReshapeInfo = {
    labelIRI: string;
    view: string;
    layer: string;
  };

  // First pass - retrieve and group
  const labelsByScope = (mm.labels() as string[])
    .reduce<{[scope: string]: {[zoomRange: string]: LabelReshapeInfo[]}}>((acc, labelIRI) => {
      const [{
        [IRIS.VM.forView]: forView,
        [IRIS.VM.forLayer]: forLayer,
        [IRIS.VM.forLensDetail]: lensDetail,
      }, _] = partitionSubj(mm, labelIRI, {});
      // skip those in lens world
      if (lensDetail) {
        return acc;
      }

      const view = (forView || '').split('/').pop();
      const [layer, zoomRange] = (forLayer || '').split('@');

      const viewResolved = view ? `_${view}` : '';
      const layerResolved = layer ? `_${layer}` : '';
      const zoomRangeResolved = zoomRange || '0-';

      const scope = `${viewResolved}${layerResolved}`;
      ((acc[scope] ??= {})[zoomRangeResolved] ??= []).push({labelIRI, view: forView, layer});
      return acc;
    }, {});

  /* Second pass - create lenses
   *
   * BASE_LENS is the base lens, only created for all non-specified
   * (no forLayer and no forView) labels.
  */
  const layersLens: {[layer: string]: string} = {};
  Object.entries(labelsByScope).forEach(([scope, byZoomRange]) => {
    const lens = `${BASE_LENS}${scope}`;
    mm._updateProperties(lens, [
      {pred: IRIS.RDF.type, obj: IRIS.VM.Lens},
    ]);

    const zoomRanges = Object.keys(byZoomRange).sort();
    zoomRanges.forEach((zoomRange, index) => {
      const detailNumber = index + 1;
      const lensDetail = `${BASE_DETAIL}_${detailNumber}${scope}`;
      mm._updateProperties(lens, [
        {pred: IRIS.VM.detail + detailNumber, obj: lensDetail},
      ]);

      let mapTiles: string|null = null;
      byZoomRange[zoomRange]?.forEach(({labelIRI, view, layer}) => {
        mm._updateProperties(labelIRI, [
          {pred: IRIS.VM.forLensDetail, obj: lensDetail},
        ]);

        const layerResolved = layer || view;
        layersLens[layerResolved] = lens;
        mapTiles = mapTiles || mm.tilesFor(layerResolved) || null;
      });

      // allow detail levels with no tiles src, these will be defaulted
      // to the current view's tiles
      const lensDetailPreds = [
        {pred: IRIS.RDF.type, obj: IRIS.VM.LensDetail},
        {pred: IRIS.VM.zoomRange, obj: zoomRange},
        mapTiles && {pred: IRIS.VM.usesMapTiles, obj: mapTiles},
      ].filter(Boolean);
      mm._updateProperties(lensDetail, lensDetailPreds);
    });
  });

  // Third pass - link all views on lens
  [...mm.views(), ...mm.storypoints()].forEach((view: string) => {
    if (!mm.onLensOf(view)) {
      const layer = view && (mm._getField(view, IRIS.VM.comment) ?? view);
      const lens = layersLens[layer] || BASE_LENS;
      mm._updateProperties(view, [
        {pred: IRIS.VM.onLens, obj: lens},
      ]);
    }
  });

  /* Fourth pass - link all base lenses to BASE_LENS
   *
   * Separated for stray labels not on any lens details in the lens world(?)
   */
  if (labelsByScope['']) {
    mm._getEntitiesWithClass(IRIS.VM.Lens).forEach((lens: string) => {
      if (lens !== BASE_LENS && !mm.parentLensOf(lens)) {
        mm._updateProperties(lens, [
          {pred: IRIS.VM.parentLens, obj: BASE_LENS},
        ]);
      }
    });
  }
}

/**
 * Move vm:atGeoPoly and vm:withGeoPath from vm:Thing to vm:Label.
 * This step should only deal with old map models or lens world before this fix.
 * The thing's label should be of the last detail level, which vm:forLensDetail link
 * was created in the labelLayersToLens function if not existing.
 *
 * In lens world, area and data path for a thing can appear on different lenses.
 * vm:Thing has at most one of each vm:withGeoPoly and vm:withGeoPath but also in the old world
 * there is one "lens" so moving them to any detail containing associated label would not be ambiguous.
 * Maps in lens world but model before this fix, should be reuploaded
 * due to lost geo location data of things on different lenses.
 * The properties are also renamed to vm:withPoly and vm:withDataPath.
 */
function moveGeoLocationsToLens(mm: MapModel): void {
  const labelsByTargetLens = (mm.labels() as string[])
    .reduce<{[targetIri: string]: {[lens: string]: string[]}}>((acc, labelIRI) => {
      const target = mm._getField(labelIRI, IRIS.VM.forThing);
      if (target) {
        const lensDetail = mm._getField(labelIRI, IRIS.VM.forLensDetail);
        const lens = mm._getField(lensDetail, IRIS.VM.ofLens);
        ((acc[target] ||= {})[lens] ||= []).push(labelIRI);
      }
      return acc;
    }, {});

  const triples: {[key: string]: Term[]} = {
    [IRIS.VM.atGeoPoly]: [],
    [IRIS.VM.withGeoPath]: [],
  };
  Object.entries(labelsByTargetLens).forEach(([target, labelsByLens]) => {
    [IRIS.VM.atGeoPoly, IRIS.VM.withGeoPath].forEach(pred => {
      const geoTerms = maybeGetGeoTerms(mm, labelsByLens, target, pred);
      if (geoTerms.length) {
        (triples[pred] ||= []).push(...geoTerms);
      }
    });
  });
  if (triples[IRIS.VM.atGeoPoly].length || triples[IRIS.VM.withGeoPath].length) {
    // Relatively safe as paths and polys for hanging things should not exist.
    // Removing a chunk of triples one by one is very slow.
    mm.deleteGeoLocationOfNonLabels();
    mm._store.putManyRawTriples([...triples[IRIS.VM.atGeoPoly], ...triples[IRIS.VM.withGeoPath]]);
  }
}

const geoPredicateMap: Readonly<{[pred: string]: string}> = {
  [IRIS.VM.atGeoPoly]: IRIS.VM.withPoly,
  [IRIS.VM.withGeoPath]: IRIS.VM.withDataPath,
};

function maybeGetGeoTerms(mm: MapModel, labelsByLens: {[lens: string]: string[]}, target: string, geoPred: string): Term[] {
  const geoData = mm._getField(target, geoPred);
  if (geoData) {
    return Object.values(labelsByLens).map(labels => {
      // Cheating with how the model is constructed so traversing through it will
      // always have the last detail last. It needs to only attach on one of the labels anyway.
      const label = labels[labels.length - 1];
      return {subj: label, pred: geoPredicateMap[geoPred], obj: geoData};
    });
  }
  return [];
}

// there can be only one
const highlanderPredicates = [
  IRIS.VM.leafletMapSettings,
];

/**
 * Reshape is a step modifying incoming terms to be platform comformant.
 *
 * Types of reshapes:
 * - preset views intended for all map models
 * - preset annotation types
 * - optimization for lenses
 * - preset view depeding on content (legacy maps)
 * - categorical transformations (legacy maps)
 * - layers transformation to work with lenses (legacy maps)
 *
 * @param mm Map model
 * @param mapData Map data resource
 * @param forceReshape Force reshape flag
 */
export function reshapeModel(mm: MapModel, mapData: Partial<MapData>, forceReshape: boolean): void {
  if (flag(mm, 'noReshape') && !forceReshape) {
    return;
  }

  const intro = mapData['welcome-splash'];

  const classes: Readonly<{[iri: string]: unknown}> = mm._classChains();
  const properties: Readonly<{[iri: string]: unknown}> = mm._propertyChains();

  const stakeholders = mm.stakeholders();
  if (stakeholders.length) {
    addStakeholderOrder(mm, stakeholders);
  }

  if (intro) {
    newView(mm, 'introduction', {name: 'Introduction', desc: intro, filters: null});
  }
  // TODO: Do we *want* a view just for media?
  if (IRIS.VM.ContentItem in classes) {
    fixOntologyContentTypes(mm);
    newView(mm, 'media', {
      name: 'Media',
      desc: 'View of media related to the system map.',
      filters: 'a=ContentItem',
    });
  }
  if (IRIS.VM.Painpoint in classes) {
    fixOntologyCapitalTypes(mm);
    newView(mm, 'painpoints', {
      name: 'Painpoints',
      desc: 'View of painpoints present in the system map.',
      filters: 'a=Stakeholder+Painpoint&sort=order',
    });
    addExpressionSubclass(mm, IRIS.VM.Painpoint, 'Painpoint');
  }
  if (IRIS.VM.Brightspot in classes) {
    newView(mm, 'brightspots', {
      name: 'Brightspots',
      desc: 'View of brightspots present in the system map.',
      filters: 'a=Stakeholder+Brightspot',
    });
    addExpressionSubclass(mm, IRIS.VM.Brightspot, 'Brightspot');
  }
  if (IRIS.VM.ofStakeholder in properties) {
    addPrimaryProperty(mm, IRIS.VM.ofStakeholder);
  }
  if (mapData['voting-enabled']) {
    newView(mm, 'voting', {
      name: 'Voting',
      desc: mapData['voting-splash'] || null,
      filters: 'a=Stakeholder+Brightspot+Painpoint',
    });
  }

  addDefaultAnnotationCategory(mm);

  ontologyForCategoricalProperties(mm);

  reshapeLenses(mm);

  mm.dedupePredicates(highlanderPredicates);

  // Have changed model content, so blow caches
  mm._notifySubs();
}

function reshapeLenses(mm: MapModel) {
  // Reshaping for lenses
  labelLayersToLens(mm);
  mm._getEntitiesWithClass(IRIS.VM.LensDetail).forEach((iri: string) => {
    mm._updateProperties(iri, [{pred: IRIS.VM.ofLens, obj: mm._inPredArray(iri, IRIS.VM.detail)}]);
  });
  moveGeoLocationsToLens(mm);
}
