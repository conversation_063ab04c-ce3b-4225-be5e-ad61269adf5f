// RDF triple class, and common RDF literals

import {should, isString, isOfClass} from './utils';


//  ======================================================================
//  == Standard Triple class (immutable)

export class Triple {
    constructor (subj, pred, obj) {
        should (isString (subj));
        should (isString (pred));
        // Allow obj to be an JS type for now

        this._subj = subj;
        this._pred = pred;
        this._obj  = obj;
    }

    get subj () { return this._subj; }
    get pred () { return this._pred; }
    get obj  () { return this._obj;  }

    equals (trip) {
        should (isOfClass(Triple) (trip));
        return this.subj === trip.subj &&
               this.pred === trip.pred &&
               this.obj  === trip.obj;
    }
}


//  ======================================================================
//  == Common RDF prefixes

export const PREFIXES = {
    dc: "http://purl.org/dc/elements/1.1/",
    dcat: "http://www.w3.org/ns/dcat#",
    dcterms: "http://purl.org/dc/terms/",
    gist: "https://ontologies.semanticarts.com/gist/",
    foaf: "http://xmlns.com/foaf/0.1/",
    oa: "http://www.w3.org/ns/oa#",
    org: "http://www.w3.org/ns/org#",
    owl: "http://www.w3.org/2002/07/owl#",
    rdf: "http://www.w3.org/1999/02/22-rdf-syntax-ns#",
    rdfs: "http://www.w3.org/2000/01/rdf-schema#",
    skos: "http://www.w3.org/2004/02/skos/core#",
    vm: "http://visual-meaning.com/rdf/",
    webprotege: "http://webprotege.stanford.edu/",
};


//  ======================================================================
//  == All the core RDF literals we use

export const IRIS = {
    RDF: {
        type: PREFIXES.rdf + 'type',
    },

    RDFS: {
        subClassOf:    PREFIXES.rdfs + "subClassOf",
        subPropertyOf: PREFIXES.rdfs + "subPropertyOf",

        label:   PREFIXES.rdfs + "label",
        comment: PREFIXES.rdfs + "comment",

        domain: PREFIXES.rdfs + 'domain',
        range:  PREFIXES.rdfs + 'range',
    },

    DCTERMS: {
        created: PREFIXES.dcterms + 'created',
        creator: PREFIXES.dcterms + 'creator',
    },

    SKOS: {
        altLabel: PREFIXES.skos + 'altLabel',
        comment: PREFIXES.skos + 'comment',
        definition: PREFIXES.skos + 'definition',
        example: PREFIXES.skos + 'example',
    },

    OA: {
        Annotation: PREFIXES.oa + 'Annotation',

        hasBody: PREFIXES.oa + 'hasBody',
        hasTarget: PREFIXES.oa + 'hasTarget',
        motivatedBy: PREFIXES.oa + 'motivatedBy',

        assessing: PREFIXES.oa + 'assessing',
        moderating: PREFIXES.oa + 'moderating',
        commenting: PREFIXES.oa + 'commenting'
    },

    GIST: {
        Category: PREFIXES.gist + 'Category',
        isCategorizedBy: PREFIXES.gist + 'isCategorizedBy',
    },

    FOAF: {
        Person: PREFIXES.foaf + 'Person',
    },

    ORG: {
        OrganisationalUnit: PREFIXES.org + 'OrganisationalUnit',
    },

    OWL: {
        Class: PREFIXES.owl + 'Class',
        DatatypeProperty: PREFIXES.owl + 'DatatypeProperty',
        ObjectProperty: PREFIXES.owl + 'ObjectProperty',
    },

    // TODO poss change keys which are classes to leading upper case
    VM: {
        atGeoPoint: PREFIXES.vm + "atGeoPoint",
        atGeoPoly:  PREFIXES.vm + "atGeoPoly",
        withGeoPath:  PREFIXES.vm + "withGeoPath",
        withPoly:  PREFIXES.vm + "withPoly",
        withDataPath:  PREFIXES.vm + "withDataPath",

        minGeoPoint: PREFIXES.vm + 'minGeoPoint',
        maxGeoPoint: PREFIXES.vm + 'maxGeoPoint',

        Stakeholder: PREFIXES.vm + 'Stakeholder',

        Expression:  PREFIXES.vm + 'Expression',
        Painpoint:   PREFIXES.vm + 'Painpoint',
        Brightspot:  PREFIXES.vm + 'Brightspot',

        Issue:       PREFIXES.vm + 'Issue',
        hasAspect:   PREFIXES.vm + 'hasAspect',
        hasIcon:     PREFIXES.vm + 'hasIcon',
        hasAltIcon:  PREFIXES.vm + 'hasAltIcon',
        useMask:  PREFIXES.vm + 'useMask',
        hasCategory: PREFIXES.vm + 'hasCategory',
        hasInvolvement: PREFIXES.vm + 'hasInvolvement',

        Category:    PREFIXES.vm + 'Category',
        Capability:  PREFIXES.vm + 'Capability',

        Document:        PREFIXES.vm + 'Document',
        link:            PREFIXES.vm + 'link',
        hasDocumentType: PREFIXES.vm + 'hasDocumentType',
        canEmbed:        PREFIXES.vm + 'canEmbed',

        View:      PREFIXES.vm + 'View',
        Map:      PREFIXES.vm + 'Map',
        Label:       PREFIXES.vm + 'Label',

        Lens:       PREFIXES.vm + 'Lens',
        LensDetail: PREFIXES.vm + 'LensDetail',

        PrimaryProperty: PREFIXES.vm + 'PrimaryProperty',
        IndexProperty: PREFIXES.vm + 'IndexProperty',
        AnnotationCategory: PREFIXES.vm + 'AnnotationCategory',

        useFilters: PREFIXES.vm + 'useFilters',
        classOfInterest: PREFIXES.vm + 'classOfInterest',

        // Temporary-ish top level properties of the map
        defaultView: PREFIXES.vm + 'defaultView',
        navContent: PREFIXES.vm + 'navContent',
        supportedLanguages: PREFIXES.vm + 'supportedLanguages',
        leafletMapSettings: PREFIXES.vm + 'leafletMapSettings',

        story:       PREFIXES.vm + 'Story',
        storypoint:  PREFIXES.vm + 'Storypoint',
        firstPoint:  PREFIXES.vm + 'firstPoint',
        followedBy:  PREFIXES.vm + 'followedBy',

        ofStakeholder:  PREFIXES.vm + 'ofStakeholder',
        ofStory:        PREFIXES.vm + 'ofStory',
        hasContentItem: PREFIXES.vm + 'hasContentItem',

        usesMapTiles: PREFIXES.vm + 'usesMapTiles',
        boxColor: PREFIXES.vm + 'boxColor',
        fontColor: PREFIXES.vm + 'fontColor',
        fontFamily: PREFIXES.vm + 'fontFamily',
        fontSize: PREFIXES.vm + 'fontSize',
        forThing: PREFIXES.vm + 'forThing',
        boxBounds: PREFIXES.vm + 'boxBounds',
        forView: PREFIXES.vm + 'forView',
        forLayer: PREFIXES.vm + 'forLayer',
        forLensDetail: PREFIXES.vm + 'forLensDetail',
        ofLens: PREFIXES.vm + 'ofLens',
        zoomRange: PREFIXES.vm + 'zoomRange',
        display: PREFIXES.vm + 'display',
        broader: PREFIXES.vm + 'broader',
        order: PREFIXES.vm + 'order',
        coversClass: PREFIXES.vm + 'coversClass',

        onLens: PREFIXES.vm + 'onLens',
        onLensMap: PREFIXES.vm + 'onLensMap',
        forLensMap: PREFIXES.vm + 'forLensMap',
        LensMapNode: PREFIXES.vm + 'LensMapNode',
        detail: PREFIXES.vm + 'detail',
        parentLens: PREFIXES.vm + 'parentLens',

        showEdges: PREFIXES.vm + 'showEdges',
        hasGloss: PREFIXES.vm + 'hasGloss',

        TrustPerception:  PREFIXES.vm + 'TrustPerception',
        hasTrustPerception: PREFIXES.vm + 'hasTrustPerception',
        trustor: PREFIXES.vm + 'trustor',
        trustee: PREFIXES.vm + 'trustee',
        trustScore: PREFIXES.vm + 'trustScore',
        importanceScore: PREFIXES.vm + 'importanceScore',
        trustworthinessScore: PREFIXES.vm + 'trustworthinessScore',
        benevolenceScore: PREFIXES.vm + 'benevolenceScore',
        competenceScore: PREFIXES.vm + 'competenceScore',
        integrityScore: PREFIXES.vm + 'integrityScore',

        hasScore: PREFIXES.vm + 'hasScore',
        hasImpact: PREFIXES.vm + 'hasImpact',

        opacity: PREFIXES.vm + 'opacity',

        hasRAGData: PREFIXES.vm + 'hasRAGData',
        isGrayscale: PREFIXES.vm + 'isGrayscale',

        judgedPositive: PREFIXES.vm + '_judged-positive',
        judgedNeutral: PREFIXES.vm + '_judged-neutral',
        judgedNegative: PREFIXES.vm + '_judged-negative',

        ColorCategory:    PREFIXES.vm + 'ColorCategory',
        DeltaCategory:    PREFIXES.vm + 'DeltaCategory',

        forColorCategory: PREFIXES.vm + 'forColorCategory',
        cssVariable: PREFIXES.vm + 'cssVariable',
        hexCode: PREFIXES.vm + 'hexCode',

        hasPrimaryCategory: PREFIXES.vm + 'hasPrimaryCategory',
        hasCategoryAsset: PREFIXES.vm + 'hasCategoryAsset',
        forProperty: PREFIXES.vm + 'forProperty',

        // Some hardcoded categorical type predicates used in certain maps
        categorizedBy: PREFIXES.vm + 'categorizedBy',
        // Handling lack of clarity over spelling via dumb alias for now
        categorisedBy: PREFIXES.vm + 'categorisedBy',
        activityType: PREFIXES.vm + 'activityType',
        capabilityDefinition: PREFIXES.vm + 'capabilityDefinition',
        ofGood: PREFIXES.vm + 'ofGood',

        name:        PREFIXES.vm + 'name',
        abbreviation:PREFIXES.vm + 'abbreviation',
        aliasing:    PREFIXES.vm + 'aliasing',
        initialism:  PREFIXES.vm + 'initialism',
        acronym:     PREFIXES.vm + 'acronym',
        description: PREFIXES.vm + 'description',
        // TODO: don't duplicate from rdfs and skos?
        comment:     PREFIXES.vm + 'comment',
        reverseLabel:     PREFIXES.vm + 'reverseLabel',
        plural: PREFIXES.vm + 'plural',

        Domain:      PREFIXES.vm + 'Domain',
        inDomain: PREFIXES.vm + 'inDomain',
        // non-categorical link to domain for internal map objects
        forDomain: PREFIXES.vm + 'forDomain',
        selectionMode: PREFIXES.vm + 'selectionMode',
        Region:      PREFIXES.vm + 'Region',
        inRegion:      PREFIXES.vm + 'inRegion',
        countryCode: PREFIXES.vm + 'countryCode',

        aidsCapitalType: PREFIXES.vm + 'aidsCapitalType',
        harmsCapitalType: PREFIXES.vm + 'harmsCapitalType',
        relates: PREFIXES.vm + 'relates',

        worksIn: PREFIXES.vm + 'worksIn',

        Voter:            PREFIXES.vm + 'Voter',
        Campaign:         PREFIXES.vm + 'Campaign',
        invitedOn:        PREFIXES.vm + 'invitedOn',
        hasVoter:         PREFIXES.vm + 'hasVoter',
        email:            PREFIXES.vm + 'email',
        url:              PREFIXES.vm + 'url',

        voteOne: PREFIXES.vm + 'vote-1',
        voteTwo: PREFIXES.vm + 'vote-2',

        ContentItem:    PREFIXES.vm + 'ContentItem',
        hasUUID:        PREFIXES.vm + 'hasUUID',
        hasContentType: PREFIXES.vm + 'hasContentType',

        // All the capital types we know about
        CAPTYPE: {
            social:    PREFIXES.vm + 'capitalTypes/social',
            sharedFin: PREFIXES.vm + 'capitalTypes/sharedFinancial',
            human:     PREFIXES.vm + 'capitalTypes/human',
            // TODO: Change to natural?
            environ:   PREFIXES.vm + 'capitalTypes/environmental',
        },

        // All the content types
        CONTENTTYPE: {
            image: PREFIXES.vm + 'contentTypes/image',
            video: PREFIXES.vm + 'contentTypes/video',
        },

        // All the document types
        DOCUMENTTYPE: {
            application: PREFIXES.vm + 'documentTypes/application',
            audio:       PREFIXES.vm + 'documentTypes/audio',
            embed:       PREFIXES.vm + 'documentTypes/embed',
            image:       PREFIXES.vm + 'documentTypes/image',
            text:        PREFIXES.vm + 'documentTypes/text',
            video:       PREFIXES.vm + 'documentTypes/video',
        },
    },
    VMHE: {
        Activity: PREFIXES.vm + 'HE/Activity',
        ActivityInvolvement: PREFIXES.vm + 'HE/ActivityInvolvement',
        // TODO: These are ambiguous between the ontology and transforms
        hasInvolvement: PREFIXES.vm + 'hasInvolvement',
        activeLabel: PREFIXES.vm + 'activeLabel',
        passiveLabel: PREFIXES.vm + 'passiveLabel',
        primary: PREFIXES.vm + 'primary',
        relatesTo: PREFIXES.vm + 'relatesTo',
        causes: PREFIXES.vm + 'HE/causes',
        stronglycauses: PREFIXES.vm + 'HE/stronglycauses',
    },
};
