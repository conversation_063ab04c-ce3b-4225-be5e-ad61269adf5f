// Showing what things are
/** @jsx h */

import {Fragment, h} from 'preact';

import LinkForSelect from '../components/LinkForSelect';
import {Translate, createBold, identity} from '../intl';
import {IRIS} from '../rdf';

import type {MapModel} from '../rdfmodels';
import type {RNode} from '../types';

export type RElement = RNode|string|null;
export type LabelBuilder = (iri: string) => RElement;
export type StringLabelBuilder = (iri: string) => string;
export type ElementsBuilder = (elements: RElement[]) => RElement;

export function objectNamer(mm: MapModel): StringLabelBuilder {
  return (iri: string) => mm.nameOf(iri) || '?';
}

export function classNamer(mm: MapModel): StringLabelBuilder {
  return (iri: string) => mm.className(iri) || '?';
}

export function joiner(elements: RElement[]): RElement {
  return <Fragment>{elements}</Fragment>;
}

export function inlineJoiner(elements: RElement[]): RElement {
  return <Fragment>
    {elements.map((elem, index) => <Fragment key={index}>
      {elem}
      {index < elements.length - 1 && ', '}
    </Fragment>)}
  </Fragment>;
}

function sur(mm: MapModel, iri: string): string|null {
  const ts = mm._store.triplesWithSubjPred(iri, IRIS.RDFS.subClassOf);
  if (!ts.length) {
    return null;
  }
  return ts[0].obj;
}

export function knownCapitalType(iri: string|null): string|null {
  switch (iri) {
    case IRIS.VM.CAPTYPE.environ:
      return 'environmental';
    case IRIS.VM.CAPTYPE.human:
      return 'human';
    case IRIS.VM.CAPTYPE.sharedFin:
      return 'sharedFinancial';
    case IRIS.VM.CAPTYPE.social:
      return 'social';
  }
  return null;
}

export function knownContentType(iri: string|null): string|null {
  switch (iri) {
    case IRIS.VM.CONTENTTYPE.image:
      return 'image';
    case IRIS.VM.CONTENTTYPE.video:
      return 'video';
  }
  return null;
}

export function broader(mm: MapModel, iri: string): RNode|null {
  const rel = mm._getField(iri, IRIS.VM.broader);
  return rel && <p className="vm-d">
    <div className="vm-i offset nav-up" />
    <LinkForSelect iri={rel}>{mm.nameOf(rel) || '?'}</LinkForSelect>
  </p>;
}

export function exampleFor(mm: MapModel, iri: string): RNode|null {
  const example = mm._getField(iri, IRIS.SKOS.example);
  return example && <p><Translate defaultMessage="Example: <b>{example}</b>" values={{example, b: createBold}} /></p>;
}

export function explainType(mm: MapModel, iri: string): RNode|null {
  const classBuilder = (iri: string|null) => {
    const namedType = iri && mm.aClassName(iri);
    return (iri && namedType) ? <Translate defaultMessage="is {class}" values={{class: namedType}} /> : null;
  };
  const superClassBuilder = (iri: string|null) => {
    return iri ? <LinkForSelect iri={iri} key={iri}>{mm.nameOf(iri)}</LinkForSelect> : null;
  };

  const details = explainTypeDetails({mm, iri, classBuilder, superClassBuilder});
  return details ? <p className="vm-inset vm-explain">{details}</p> : null;
}

function maybeCategoryExplanation(baseElem: RElement, settings: ExplainSettings): RElement {
  const {mm, iri, categoryBuilder = objectNamer(mm)} = settings;
  const catRel = mm.primaryCategoryRelationOf(iri);
  if (catRel) {
    const typeLine = mm.nameOf(catRel.predicate);
    const categoryElem = categoryBuilder(catRel.category);
    return <Fragment>{baseElem} {typeLine} {categoryElem}</Fragment>;
  }
  return null;
}

function wrapBroader(element: RElement, settings: ExplainSettings): RElement {
  const {mm, iri, broaderBuilder = objectNamer(mm)} = settings;
  const rel = mm._getField(iri, IRIS.VM.broader);
  const broaderElem = rel && broaderBuilder(rel);
  // "NotFoundError: Failed to execute 'insertBefore' on 'Node': The node before which
  // the new node is to be inserted is not a child of this node."
  // There is an issue with change detection for composite element substitution in translations.
  // Placing it in a tag substitution does seem to correct that.
  return <Translate
    defaultMessage={`{hasBroader, select,
      true {<i>{element}</i> within {broader}}
      other {{element}}
    }`}
    values={{
      element,
      hasBroader: !!broaderElem,
      broader: broaderElem,
      i: identity,
    }}
  />;
}

function finalize(details: RElement, settings: ExplainSettings): RElement {
  return settings.skipBroader ? details : wrapBroader(details, settings);
}

export type ExplainSettings = Readonly<{
  mm: MapModel;
  iri: string;
  classBuilder?: LabelBuilder;
  classesBuilder?: ElementsBuilder;
  superClassBuilder?: LabelBuilder;
  categoryBuilder?: LabelBuilder;
  broaderBuilder?: LabelBuilder;
  skipBroader?: boolean;
}>;

export function explainTypeDetails(settings: ExplainSettings): RElement {
  const {
    mm,
    iri,
    classesBuilder = inlineJoiner,
    classBuilder = classNamer(mm),
    superClassBuilder = objectNamer(mm),
  } = settings;
  const classes = Object.keys(mm.classesOf(iri, true));

  if (classes.length > 1) {
    return <Fragment>{classesBuilder(classes.map(classBuilder))}</Fragment>;
  }

  const classIri = classes[0];
  const classElem = classIri && classBuilder(classIri);

  // TODO: Make this the only way to explain categorized things when all categories are ontology compliant.
  let details = maybeCategoryExplanation(classElem, settings);

  if (details) {
    return finalize(details, settings);
  }

  // TODO: Replace this with something that can look at ontology categories
  switch (classIri) {
    case IRIS.OWL.Class: {
      const surIri = sur(mm, iri);
      const superClassElem = surIri && superClassBuilder(surIri);
      details = <Translate
        defaultMessage={'{class}{hasSuperclass, select, true { of {superClass}} other {}}'}
        values={{class: classElem, hasSuperclass: superClassElem !== null, superClass: superClassElem}} />;

      break;
    }
    case null: // TODO: Perhaps return an indicator the ontology is missing
    case IRIS.VM.Issue:
      return null;
    case IRIS.VM.ContentItem: {
      const type = mm.contentTypeOf(iri);
      details = <Translate
        defaultMessage={`{class} of
        {type, select,
          image {type image}
          video {type video}
          other {unspecified type}
        }`}
        values={{class: classElem, type: knownContentType(type) ?? 'none'}} />;

      break;
    }
    case IRIS.VM.Brightspot: {
      const type = mm.capitalTypeOf(iri);
      details = <Translate
        defaultMessage={`{class}{type, select, none {} other { that aids
        {type, select,
          social {social capital}
          sharedFinancial {financial capital}
          human {human capital}
          environmental {natural capital}
          other {}
        }}}`}
        values={{class: classElem, type: knownCapitalType(type) ?? 'none'}} />;
      break;
    }
    case IRIS.VM.Painpoint: {
      const type = mm.capitalTypeOf(iri);
      details = <Translate
        defaultMessage={`{class}{type, select, none {} other { that harms
        {type, select,
          social {social capital}
          sharedFinancial {financial capital}
          human {human capital}
          environmental {natural capital}
          other {unspecified capital}
        }}}`}
        values={{class: classElem, type: knownCapitalType(type) ?? 'none'}} />;
      break;
    }
    default: {
      details = classElem;
      break;
    }
  }

  return finalize(details, settings);
}
