/**
 * Functions to retrieve data from the document,
 * for example, data set in django template.
 */

import type {MapInfo, Settings} from '../types';

const htmlData: {
  map_settings?: Settings;
  map_info?: MapInfo;
} = {};

function enforceSettings(json: unknown): json is Settings {
  const o = json as Settings;
  return !(!o['map-iri'] || !o['api-root']);
}

function enforceMapInfo(json: unknown): json is MapInfo {
  const o = json as MapInfo;
  return !(!o['iri'] || !o['name']);
}

export function getSettings(): Settings {
  return (htmlData.map_settings ??= (() => {
    const settings = JSON.parse(document.getElementById('map-settings')?.textContent || '{}');
    if (enforceSettings(settings)) {
      return settings;
    }
    throw new Error('unexpected settings');
  })());
}

export function getMapInfo(): MapInfo {
  return (htmlData.map_info ??= (() => {
    const mapInfo = JSON.parse(document.getElementById('map-info')?.textContent || '{}');
    if (enforceMapInfo(mapInfo)) {
      return mapInfo;
    }
    throw new Error('unexpected map info');
  })());
}
