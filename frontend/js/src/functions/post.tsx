// Interface for posting content with compression
//
// Requires the browser to support CompressionStream, if does not then
// uncompressed content will be sent. However does not require fetch api
// support for ReadableStream bodies, as it just converts back to blob.
//
// This is less fancy than using a worker to handle api calls, but has some
// robustness by stashing the pending post content in localStorage, which
// leaves evidence and possibility of recovery in the case of errors.

export type PostBody = Readonly<{
  buildForm: () => FormData;
  getBlob: () => Blob;
  getHeaders: () => {[header: string]: string};
  finish: () => void;
}>;

function blobFromStream(stream: ReadableStream, blobType = ''): Promise<Blob> {
  const reader = stream.getReader();
  const chunks: string[] = [];
  return reader.read().then(function incrementBlob({done, value}): Promise<Blob> {
    if (done) {
      return Promise.resolve(new Blob(chunks, {type: blobType}));
    }
    chunks.push(value);
    return reader.read().then(incrementBlob);
  });
}

function _compressIfSupported(json: string): Promise<Blob> {
  const jsonBlob = new Blob([json], {type: 'application/json'});
  if (window.CompressionStream) {
    return blobFromStream(jsonBlob.stream().pipeThrough(new CompressionStream('deflate')), jsonBlob.type + '&deflate');
  }
  return Promise.resolve(jsonBlob);

}

function _cacheItem(keyName: string, keyValue: string): () => void {
  try {
    localStorage.setItem(keyName, keyValue);
  } catch (e) {
    console.log('failed to cache item for post', keyName, e);
  }
  return () => localStorage.removeItem(keyName);
}

export function prepareJsonPost(tag: string, data: unknown): Promise<PostBody> {
  const key = 'vm' + tag;
  const json = JSON.stringify(data);
  const finish = _cacheItem(key, json);
  return _compressIfSupported(json).then(blob => {
    const [type, encoding] = blob.type.split('&');
    return {
      getBlob: () => blob,
      buildForm: () => {
        const form = new FormData();
        form.set('json', blob, key + (encoding ? '.json.' + encoding : '.json'));
        return form;
      },
      getHeaders: () => ({
        'Content-Type': type,
        ...(encoding ? {'Content-Encoding': encoding} : {}),
      }),
      finish,
    };
  });
}
