import MarkdownIt from 'markdown-it';
import {plainTextPlugin} from './plainTextPlugin';

describe('plain text', () => {
  const mi = MarkdownIt()
    .use(plainTextPlugin);

  test.each([
    ['', ''],
    ['x', 'x '],
    ['x\n\ny', 'x y '],
    ['x and y(s)', 'x and y(s) '],
    ['Fake data [Fake orange](#VM/test-fake-data)', 'Fake data Fake orange '],
    ['__Fake data by [<PERSON>](#VM/_person_xxx) on 02 Feb 1995__', 'Fake data by <PERSON> on 02 Feb 1995 '],
    ['[Fake orange](#VM/test-fake-data) for fake data', 'Fake orange for fake data '],
  ])('plain text for %s', (input, expected) => {
    mi.render(input);
    expect((mi as any).plainText).toEqual(expected);
  });

});

