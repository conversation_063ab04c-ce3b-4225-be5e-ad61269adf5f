// Generates pointer events from Safari trackpad gesture events

// Safari on macOS generates trackpad gesture events (zoom, rotate, etc.)
// which are not handled by OL. If they are not prevented the whole browser
// window zooms. We need to preventDefault() and dispatch equivalent
// multi-touch pointer events which can be understood by OL.
// On iOS the same gesture events are generated in conjunction with pointer
// events. OL prevents the pointer events which also prevents the gesture.

// https://developer.apple.com/documentation/webkitjs/gestureevent
// https://developer.mozilla.org/en-US/docs/Web/API/GestureEvent
interface GestureEvent extends UIEvent {
  clientX: number;
  clientY: number;
  screenX: number;
  screenY: number;
  scale: number;
  rotation: number;
}

declare const GestureEvent: {
  prototype: GestureEvent;
  new(): GestureEvent;
};

// The scale represents the distance between the two fingers as a multiplier
// of the initial distnace. We need a fake initial distance in pixels.
const fingersDistInit = 100;

// Each finger needs to have a unique pointerId accross the active pointers.
// Safari appears to use negative numbers for its native touch events
// and 1 is reserved for the mouse.
const finger1Id = 101;
const finger2Id = 102;

function getGestureCompat(pointerEventType: string) {
  const createPointerEvent = (e: GestureEvent, offset: number, pointerId: number) => {
    // Use the real client and screen coordinates so
    // that zooming is directed to the correct location.
    return new PointerEvent(pointerEventType, {
      bubbles: true,
      cancelable: true,
      clientX: e.clientX + offset,
      clientY: e.clientY + offset,
      screenX: e.screenX + offset,
      screenY: e.screenY + offset,
      pointerId,
    });
  };
  return ((e: GestureEvent) => {
    e.preventDefault();
    if (e.target) {
      const fingersDist = fingersDistInit * e.scale;
      const finger1 = createPointerEvent(e, fingersDist / -2, finger1Id);
      const finger2 = createPointerEvent(e, fingersDist / +2, finger2Id);
      e.target.dispatchEvent(finger1);
      e.target.dispatchEvent(finger2);
    }
  }) as (e: Event) => void;
}

const onGestureStart = getGestureCompat('pointerdown');
const onGestureChange = getGestureCompat('pointermove');
const onGestureEnd = getGestureCompat('pointerup');

export default function addGestureCompat(target: EventTarget): () => void {
  // If gesture events are not available or touch events are available we are
  // either not on Safari or on Safari mobile where emulation is not needed.
  // This may not be true on an iPad with a trackpad and keyboard for example
  if (typeof GestureEvent === 'undefined' || typeof TouchEvent !== 'undefined') {
    return () => undefined;
  }

  target.addEventListener('gesturestart', onGestureStart);
  target.addEventListener('gesturechange', onGestureChange);
  target.addEventListener('gestureend', onGestureEnd);

  return () => {
    target.removeEventListener('gesturestart', onGestureStart);
    target.removeEventListener('gesturechange', onGestureChange);
    target.removeEventListener('gestureend', onGestureEnd);
  };
}
