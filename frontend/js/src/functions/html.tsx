// Functions to make constructing HTML in JSX a bit easier

type maybe<T> = T|undefined|null;
type classDefinitions = {[className: string]: maybe<boolean>};
type maybeClass = {'class'?: string};

// Build a class attribute from a mapping of possible classes
//
// Similar to the className npm package but a bit less flexible
export function classes(
  map: classDefinitions,
  ...classes: maybe<string>[]): maybeClass {
  const names = [];
  names.push(...classes.filter(Boolean));
  for (const cls in map) {
    if (map[cls]) {
      names.push(cls);
    }
  }
  return names.length ? {'class': names.join(' ')} : {};
}

