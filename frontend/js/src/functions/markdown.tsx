// Helper functions for markdown-it-marked

// Safari below 17.1 doesn't support regex lookbehind stably
export const supportLookBehind = function() {
  try {
    new RegExp('(?<=^|\\p{Cc}\\p{Cf}|\\p{P}|\\p{Z})cat', 'iu').test('cat');
  } catch {
    return false;
  }
  const uAgt = navigator.userAgent;
  const isSafari = /^((?!chrome|android).)*safari/i.test(uAgt);
  if (isSafari) {
    const versionString = uAgt.match(/Version\/(\S)+/);
    if (versionString !== null) {
      const version = parseFloat(versionString[0].substring(8));
      if (version && version < 17.1) {
        return false;
      }
    }
  }
  return true;
}();
