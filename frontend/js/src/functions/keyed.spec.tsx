import {
  inclusionsEqual,
  toggleGroupedInclusion,
  toggleInclusion,
} from './keyed';

import type {GroupedInclusion, Inclusion, Keyed} from '../types';

type InclusionTest = {
  inclusion: Inclusion|undefined;
  key: string;
  expected: Inclusion|undefined;
};

type GroupedInclusionTest = {
  inclusion: GroupedInclusion|undefined;
  group: string;
  key: string;
  expected: GroupedInclusion|undefined;
};

describe('toggleInclusion', () => {
  it('default', () => {
    const tests: InclusionTest[] = [
      {inclusion: {a: true}, key: 'b', expected: {a: true, b: true}},
      {inclusion: undefined, key: 'a', expected: {a: true}},
      {inclusion: {a: true}, key: 'a', expected: undefined},
      {inclusion: {a: true, b: true}, key: 'b', expected: {a: true}},
    ];
    tests.forEach(({inclusion, key, expected}) => {
      expect(toggleInclusion(inclusion, key)).toEqual(expected);
    });
  });
  it('singleton', () => {
    const tests: InclusionTest[] = [
      {inclusion: {a: true}, key: 'b', expected: {b: true}},
      {inclusion: undefined, key: 'a', expected: {a: true}},
      {inclusion: {a: true}, key: 'a', expected: undefined},
    ];
    tests.forEach(({inclusion, key, expected}) => {
      expect(toggleInclusion(inclusion, key, true)).toEqual(expected);
    });
  });
});

describe('toggleGroupedInclusion', () => {
  it('default', () => {
    const tests: GroupedInclusionTest[] = [
      {inclusion: {x: {a: true}, y: {c: true}}, group: 'x', key: 'b', expected: {x: {a: true, b: true}, y: {c: true}}},
      {inclusion: {x: {a: true}}, group: 'y', key: 'c', expected: {x: {a: true}, y: {c: true}}},
      {inclusion: {x: {a: true}, y: {c: true}}, group: 'x', key: 'a', expected: {y: {c: true}}},
      {inclusion: {x: {a: true}}, group: 'x', key: 'a', expected: undefined},
      {inclusion: {x: {a: true, b: true}, y: {c: true}}, group: 'x', key: 'b', expected: {x: {a: true}, y: {c: true}}},
    ];
    tests.forEach(({inclusion, group, key, expected}) => {
      expect(toggleGroupedInclusion(inclusion, group, key)).toEqual(expected);
    });
  });
  it('singleton', () => {
    const tests: GroupedInclusionTest[] = [
      {inclusion: {x: {a: true}, y: {c: true}}, group: 'x', key: 'b', expected: {x: {b: true}, y: {c: true}}},
      {inclusion: {x: {a: true}}, group: 'y', key: 'c', expected: {x: {a: true}, y: {c: true}}},
      {inclusion: {x: {a: true}, y: {c: true}}, group: 'x', key: 'a', expected: {y: {c: true}}},
      {inclusion: {x: {a: true}}, group: 'x', key: 'a', expected: undefined},
    ];
    tests.forEach(({inclusion, key, group, expected}) => {
      expect(toggleGroupedInclusion(inclusion, group, key, true)).toEqual(expected);
    });
  });
});

describe('inclusionsEqual', () => {
  it('handles primitive inclusion', () => {
    const tests: [[Keyed<unknown>, Keyed<unknown>], boolean][] = [
      [[{a: true, b: false}, {a: true, b: false}], true],
      [[{a: 'value1', b: 'value2'}, {a: 'value1', b: 'value2'}], true],
      [[{a: true, b: false}, {a: true, b: true}], false],
    ];
    tests.forEach(([test, expected]) => expect(inclusionsEqual(test[0], test[1])).toEqual(expected));
  });
  it('handles grouped inclusion', () => {
    const tests: [[GroupedInclusion, GroupedInclusion], boolean][] = [
      [[{a: {c: true}, b: {d: true}}, {a: {c: true}, b: {d: true}}], true],
    ];
    tests.forEach(([test, expected]) => expect(inclusionsEqual(test[0], test[1])).toEqual(expected));
  });
  it('handles grouped non-inclusion', () => {
    const tests: [[Keyed<Keyed<string>>, Keyed<Keyed<string>>], boolean][] = [
      [[{a: {c: 'test'}}, {a: {c: 'test'}}], false],
    ];
    tests.forEach(([test, expected]) => expect(inclusionsEqual(test[0], test[1])).toEqual(expected));
  });
});
