// Helper functions related to map animations

export function getPanelsNodeList(selectors: string): NodeListOf<Element> | [] {
  const panelsContainer = document.querySelector('.vm-page:not(.vm-skeleton) .vm-float-container.base');
  return panelsContainer?.querySelectorAll(selectors) || [];
}

export function getPaddingWidestPanelLeftOffset(selectors: string, isLargeScreen: boolean, initialPadding = 0): [number, number, number, number] {
  if (isLargeScreen) {
    const panelsNodeList = getPanelsNodeList(selectors);
    if (panelsNodeList.length) {
      const rects: {right: number}[] = [];
      panelsNodeList.forEach(node => rects.push(node.getBoundingClientRect()));
      const maxRightBounds = Math.max(...rects.map(rect => rect.right)); // return the bounds of the widest elem

      // apply inversed 'right' to comply with map extent and avoid scaling issues
      return [initialPadding, initialPadding - maxRightBounds, initialPadding, initialPadding]; // [top, right, bottom, left]
    }
  }
  return [initialPadding, initialPadding, initialPadding, initialPadding];
}
