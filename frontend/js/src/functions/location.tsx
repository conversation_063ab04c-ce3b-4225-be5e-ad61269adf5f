// Functions for location

/**
 * Partition the URL into useful paths.
 *
 * @param page the main subpage/subdirectory name
 * @returns a list of paths:
 *   1. the URL base including the project name, e.g. /maps/highways/
 *   2. the view name if included, e.g. _map_activity
 */
export function partitionURL(page: string): [string, string] {
  const pattern = new RegExp(`^(/${page}/[^/?#]+)(?:/([^/?#]+))?`);
  const pathname = window.location.pathname;
  const [base, view] = pathname.match(pattern)?.slice(1, 3) || [null, null];
  return [
    base ? `${base}/` : '',
    view || '',
  ];
}

/**
 * Normalize the URL path by merging successive forward slashes.
 * If the original window path differs from the normalized path,
 * a redirect is performed to the corrected path.
 */
export function normalizePath(): void {
  const normPath = window.location.pathname.replace(/\/{2,}/g, '/');
  if (window.location.pathname !== normPath) {
    window.location.replace(normPath);
  }
}
