// Boolean logic for filter handling with multiple values

import type {RelationFunctions} from '../hooks/useRelations';
import type {Inclusion} from '../types';

type InclusionChecker = (key: string|undefined) => boolean;
type IntersectionChecker = (key: string) => boolean;

function inclusionFalsesTrues(inclusion: Inclusion): [boolean, boolean] {
  return Object.values(inclusion).reduce<[boolean, boolean]>((acc, v) => {
    acc[+v] = true;
    return acc;
  }, [false, false]);
}

export function buildInclusion(inclusion: Inclusion, excludeIfMissing?: boolean): InclusionChecker|null {
  const [falses, trues] = inclusionFalsesTrues(inclusion);
  const missing = excludeIfMissing ?? trues;
  if (!falses) {
    if (!trues) {
      return null;
    }
    return key => key ? inclusion[key] !== true : missing;
  }
  return key => {
    if (!key) {
      return missing;
    }
    const included = inclusion[key];
    return included === false || (included == null && trues);
  };
}

export function buildIntersection(inclusion: Inclusion, getValues: (iri: string) => undefined|Readonly<string[]>): IntersectionChecker|null {
  const [falses, trues] = inclusionFalsesTrues(inclusion);
  if (!falses && !trues) {
    return null;
  }
  return iri => {
    const values = getValues(iri);
    let noTrues = trues;
    return values?.some(k => {
      const included = inclusion[k];
      if (included === false) {
        return true;
      }
      if (included === true && noTrues) {
        noTrues = false;
      }
      return undefined;
    }) || noTrues;
  };
}


export function buildRelated(
  filterFor: Inclusion,
  getRelations: RelationFunctions['getRelations'],
  matchRelation: RelationFunctions['hasAnyRelation'],
  alwaysRelatesSelf = true,
): IntersectionChecker|null {
  const [falses, trues] = inclusionFalsesTrues(filterFor);
  if (!falses && !trues) {
    return null;
  }
  const keys = Object.keys(filterFor);
  const initial = trues ? null : true;
  return iri => {
    if (iri in filterFor && alwaysRelatesSelf) {
      return !filterFor[iri];
    }
    const rels = getRelations(iri);
    if (!rels) {
      // If item has no relations report only if no postive matches expected
      return trues;
    }
    return !keys.reduce<boolean|null>((result, k) => {
      const present = matchRelation(rels, k);
      if (!filterFor[k] && present) {
        return false;
      } else if (result == null && filterFor[k] && present) {
        return true;
      }
      return result;
    }, initial);
  };
}
