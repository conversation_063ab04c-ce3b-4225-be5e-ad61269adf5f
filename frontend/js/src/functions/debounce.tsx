// Debounce function

type AnyFunction<TArgs, TReturn> = (...args: TArgs[]) => TReturn | void;

export const DEFAULT_DEBOUNCE_MS = 400;

/**
 * The return type of the `debounce` function, containing the debounced function
 * and a method to clear the debounce timeout.
 *
 * @template TArgs - The type of arguments the debounced function accepts.
 */
export type DebounceReturn<TArgs> = {
  /**
   * The debounced version of the original function. It delays execution until
   * after the specified time has elapsed since the last call.
   *
   * @param {...TArgs} args - The arguments to pass to the original function.
   */
  debouncedFunction: AnyFunction<TArgs, void>;

  /**
   * Clears the debounce timeout, preventing the function from being executed
   * if it was pending.
   */
  clearDebounce: () => void;
};

/**
 * Creates a debounced version of the provided function, delaying its execution
 * until after the specified time has elapsed since the last call.
 *
 * @template TArgs - The type of the function's arguments.
 * @template TReturn - The return type of the function (not used in the debounced function).
 *
 * @param {AnyFunction<TArgs, TReturn>} func - The function to debounce.
 * @param {number} [delayMs=DEFAULT_DEBOUNCE_MS] - The delay in milliseconds before invoking the function.
 * @returns {DebounceReturn<TArgs>} An object containing the debounced function and a method to clear the debounce timeout.
 *   - `debouncedFunction`: A function that applies the debounce behavior.
 *   - `clearDebounce`: A function to cancel any pending execution.
 */
export function debounce<TArgs, TReturn>(
  func: AnyFunction<TArgs, TReturn>,
  delayMs: number = DEFAULT_DEBOUNCE_MS,
): DebounceReturn<TArgs> {
  let timeout: number | undefined;

  const clearDebounce = () => {
    clearTimeout(timeout);
    timeout = undefined;
  };

  const debouncedFunction = (...args: TArgs[]) => {
    const executeLater = () => {
      timeout = undefined;
      func(...args);
    };

    clearTimeout(timeout);
    timeout = window.setTimeout(executeLater, delayMs);
  };

  return {debouncedFunction, clearDebounce};
}
