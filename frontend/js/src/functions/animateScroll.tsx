// Scrolling to a certain height position within an HTMLElement
// with easing out function.
// The function is limited by duration and a step is executed
// every frame according the refresh rate of the display.

const PIXEL_APPROXIMATION = 5e-1;

// The easing function
function easeOutQuart(x: number): number {
  return 1 - Math.pow(1 - x, 4);
}

type AnimateScrollProps={
  element: HTMLElement;
  targetPosition: number;
  duration: number;
  stopAtApproximation?: boolean; // might not work with synchronous DOM changes
};
// Returns cancellation function
export function animateScroll({element, targetPosition, duration, stopAtApproximation = false}: AnimateScrollProps): () => void {
  let start: number;
  let position: number;
  let animationFrame: number;
  const initialPosition: number = element.scrollTop;
  const offsetPosition = initialPosition - targetPosition;

  const step: FrameRequestCallback = (timestamp: number) => {
    if (start === undefined) {
      start = timestamp;
    }

    const elapsed = timestamp - start;
    const relativeTimeProgress = elapsed / duration;
    const easedProgress = easeOutQuart(relativeTimeProgress);

    let offsetProgress = offsetPosition * Math.min(easedProgress, 1);

    if (stopAtApproximation
      && Math.abs(offsetProgress - offsetPosition) < PIXEL_APPROXIMATION) {
      offsetProgress = offsetPosition;
    }

    position = initialPosition - offsetProgress;
    element.scrollTo(0, position);

    if (stopAtApproximation
      && offsetProgress === offsetPosition
      && animationFrame !== undefined) {
      window.cancelAnimationFrame(animationFrame);
      return;
    }

    if (elapsed < duration) {
      animationFrame = window.requestAnimationFrame(step);
    }
    return;
  };

  animationFrame = window.requestAnimationFrame(step);
  return () => {
    window.cancelAnimationFrame(animationFrame);
  };
}
