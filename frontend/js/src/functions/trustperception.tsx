// Trust perception pure functions

import type {GlossArrowSize, GlossAttitude, GlossWeight} from './glosschoose';

export function getTrustAttitude(trustScore: null|number): GlossAttitude {
  if (trustScore == null) {
    return 'unknown';
  }
  return trustScore < 6 ? 'negative' : trustScore < 8 ? 'neutral' : 'positive';
}

export function getTrustWeight(importanceScore: number): GlossWeight {
  return importanceScore < 4 ? 'XS' : importanceScore < 8 ? 'M' : 'XL';
}

export function getTrustArrowSize(importanceScore: number): GlossArrowSize {
  return importanceScore < 4 ? 'S' : importanceScore < 8 ? 'M' : 'L';
}
