// Selection of edge display semantics based on local graph properties

import {IRIS} from '../rdf';
import {getTrustArrowSize, getTrustAttitude, getTrustWeight} from './trustperception';

import type {MapModel} from '../rdfmodels';

const GLOSS_BASE = 'gloss';
// should use only for trust preset
export const GLOSS_TRUST = 'trust';
export const GLOSS_NONE = 'none';

export type GlossWeight = 'XS' | 'S' | 'M' | 'L' | 'XL';
export type GlossArrowSize = 'XS' | 'S' | 'M' | 'L' | 'XL';
export type GlossAttitude = 'negative' | 'neutral' | 'positive' | 'unknown';
// S - S-shaped curve, *when not too tall
// C - clockwise curve
// L - line
// B - bidirectional line, will draw two ways arrows
export type GlossShape = 'S' | 'C' | 'L' | 'B';
// from CSS
export type GlossFinish = string;

type a<T extends string> = `:${T | ''}`;
export type Gloss =
  `${typeof GLOSS_BASE}${a<GlossShape>}${a<GlossWeight>}${a<GlossArrowSize>}${a<GlossFinish>}` |
  `${typeof GLOSS_TRUST}${a<GlossShape>}${a<GlossWeight>}${a<GlossArrowSize>}${a<GlossAttitude>}`;
export type GlossPartition = [GlossShape, GlossWeight, GlossArrowSize | (typeof GLOSS_NONE), GlossFinish];

export const DEFAULT_GLOSS_PARTS: GlossPartition = ['L', 'M', GLOSS_NONE, 'default'];

export function parseGloss(g: string) : GlossPartition | null {
  const pattern = new RegExp(`^(?:${GLOSS_BASE}|${GLOSS_TRUST}):([^:]*):([^:]*):([^:]*):([^:]*)$`);
  const m = g.match(pattern);
  if (!m) {
    return null;
  }
  return [
    m[1] as GlossShape || DEFAULT_GLOSS_PARTS[0],
    m[2] as GlossWeight || DEFAULT_GLOSS_PARTS[1],
    m[3] as GlossArrowSize || (m[1] === 'B' && 'M') || DEFAULT_GLOSS_PARTS[2],
    m[4] as GlossFinish || DEFAULT_GLOSS_PARTS[3],
  ];
}

export function parseGlossOrDefault(g: string) : GlossPartition {
  const gloss = parseGloss(g);
  return gloss || [...DEFAULT_GLOSS_PARTS];
}

export function getGloss(mm: MapModel, subjType: string, predicate: string): Gloss {
  const gloss = mm.hasGlossOf(predicate);
  if (gloss) {
    return gloss;
  }
  if (predicate === IRIS.VMHE.stronglycauses) {
    return 'gloss:S:S:S:causal';
  }
  if (predicate === IRIS.VMHE.causes) {
    return 'gloss:S:XS:S:causal';
  }
  if (predicate === IRIS.VM.broader) {
    return 'gloss:L:M:S:parental';
  }
  if (predicate === IRIS.VM.ofStakeholder && (subjType === IRIS.VM.Brightspot || subjType === IRIS.VM.Painpoint)) {
    return 'gloss:L:M:S:from';
  }
  if (predicate === IRIS.VM.relates && (subjType === IRIS.VM.Brightspot || subjType === IRIS.VM.Painpoint)) {
    return 'gloss:L:S::from';
  }
  return 'gloss::::';
}

export function getGlossForTrust(trustScore: null|number, importanceScore: number): Gloss {
  const attitude = getTrustAttitude(trustScore);
  const weight = getTrustWeight(importanceScore);
  const arrowSize = getTrustArrowSize(importanceScore);

  return `${GLOSS_TRUST}:C:${weight}:${arrowSize}:${attitude}`;
}
