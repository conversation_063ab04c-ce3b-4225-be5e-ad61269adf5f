// Functions, mostly to deal with Keyed objects.

import type {GroupedInclusion, Inclusion, Keyed} from '../types';

// Toggling key "in/out of" Inclusion.
export function toggleInclusion(prev: Inclusion|undefined, iri: string, singleton?: boolean): Inclusion|undefined {
  if (prev) {
    if (singleton && Object.keys(prev).length > 1) {
      console.error('not a singleton filter');
    }
    const {[iri]: last, ...preRest} = prev;
    const rest = singleton ? {} : preRest;
    if (last) {
      return Object.keys(rest).length ? rest : undefined;
    }
    return {...rest, [iri]: true};
  }
  return {[iri]: true};
}

function cloneGroupedInclusion(prev: GroupedInclusion): GroupedInclusion {
  return Object.keys(prev).reduce<GroupedInclusion>((acc, key) => {
    acc[key] = {...prev[key]};
    return acc;
  }, {});
}

// Toggling key "in/out of" nested/grouped Inclusion.
export function toggleGroupedInclusion(prev: GroupedInclusion|undefined, group: string, iri: string, singleton?: boolean): GroupedInclusion|undefined {
  if (prev) {
    const {[group]: last, ...prevRefRest} = prev;
    const rest = cloneGroupedInclusion(prevRefRest);
    const updated = toggleInclusion(last, iri, singleton);
    if (updated) {
      return {...rest, [group]: updated};
    }
    return Object.keys(rest).length ? rest : undefined;
  }
  return {[group]: {[iri]: true}};
}

export function uInclusionsEqual<T>(a: Keyed<T>|undefined, b: Keyed<T>|undefined): boolean {
  return !a ? !b : !!b && inclusionsEqual(a, b);
}

export function inclusionsEqual<T>(a: Keyed<T>, b: Keyed<T>): boolean {
  const ak = Object.keys(a);
  if (ak.length !== Object.keys(b).length) {
    return false;
  }

  return ak.every(k => {
    const av = a[k];
    const bv = b[k];
    return isInclusion(av) && isInclusion(bv) ? inclusionsEqual(av, bv) : av === bv;
  });
}

// Not ideal type checking
function isInclusion(value: unknown): value is Inclusion {
  return typeof value === 'object'
    && value !== null
    && !Array.isArray(value)
    && Object.values(value).every(key => typeof key === 'boolean');
}
