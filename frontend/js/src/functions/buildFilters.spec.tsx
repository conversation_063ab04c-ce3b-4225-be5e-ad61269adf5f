// Tests for boolean logic for filters

import {buildInclusion, buildIntersection} from './buildFilters';

describe('buildInclusion', () => {
  const cases = ['red', 'blue', undefined];
  const resultOfCases = (checker: ReturnType<typeof buildInclusion>) => {
    if (checker == null) {
      throw new Error('no checker');
    }
    return cases.map(checker);
  };

  it('no check', () => {
    expect(buildInclusion({})).toBeNull();
  });

  it('empty of', () => {
    const emptyOfRed = buildInclusion({'red': true});
    expect(resultOfCases(emptyOfRed)).toEqual([false, true, true]);
  });

  it('empty of non-', () => {
    const emptyOfNonRed = buildInclusion({'red': false});
    expect(resultOfCases(emptyOfNonRed)).toEqual([true, false, false]);
  });

  it('empty of (excludes missing)', () => {
    const emptyOfRed = buildInclusion({'red': true}, false);
    expect(resultOfCases(emptyOfRed)).toEqual([false, true, false]);
  });

  it('empty of (includes missing)', () => {
    const emptyOfRed = buildInclusion({'red': true}, true);
    expect(resultOfCases(emptyOfRed)).toEqual([false, true, true]);
  });

  it('empty of non- (excludes missing)', () => {
    const emptyOfNonRed = buildInclusion({'red': false}, false);
    expect(resultOfCases(emptyOfNonRed)).toEqual([true, false, false]);
  });

  it('empty of non- (includes missing)', () => {
    const emptyOfNonRed = buildInclusion({'red': false}, true);
    expect(resultOfCases(emptyOfNonRed)).toEqual([true, false, true]);
  });
});

describe('buildIntersection', () => {
  const hierarchy: {[iri: string]: Readonly<string[]>} = {
    'iriA': ['A'],
    'iriB': ['B'],
    'iriC': ['C'],
    'iriAB': ['A', 'B'],
  } as const;
  const resolveChains = (iri: string) => hierarchy[iri];
  const cases = ['iriA', 'iriB', 'iriC', 'iriAB'];
  const resultOfCases = (checker: ReturnType<typeof buildIntersection>) => (
    cases.reduce<string[]>((acc, c) => {
      if (checker && !checker(c)) {
        acc.push(c);
      }
      return acc;
    }, [])
  );

  it('no check', () => {
    expect(buildIntersection({}, () => undefined)).toBeNull();
  });

  it('has A', () => {
    const hasA = buildIntersection({'A': true}, resolveChains);
    expect(resultOfCases(hasA)).toEqual(['iriA', 'iriAB']);
  });

  it('has non-A', () => {
    const hasNonA = buildIntersection({'A': false}, resolveChains);
    expect(resultOfCases(hasNonA)).toEqual(['iriB', 'iriC']);
  });

  it('has A C', () => {
    const hasAC = buildIntersection({'A': true, 'C': true}, resolveChains);
    expect(resultOfCases(hasAC)).toEqual(['iriA', 'iriC', 'iriAB']);
  });

  it('has A non-B', () => {
    const hasANonB = buildIntersection({'A': true, 'B': false}, resolveChains);
    expect(resultOfCases(hasANonB)).toEqual(['iriA']);
  });
});
