import {normalizePath, partitionURL} from './location';

function mockPathname(pathname: string) {
  const mockReplace = jest.fn();
  Object.defineProperty(window, 'location', {
    configurable: true,
    value: {
      ...window.location,
      get pathname() {
        return pathname;
      },
      replace: mockReplace, // Manually override replace
    },
  });
  return mockReplace;
}

describe('partitionURL', () => {
  it('all inclusive', () => {
    mockPathname('/test_page/test_map/test_view');
    expect(partitionURL('test_page')).toEqual(['/test_page/test_map/', 'test_view']);

    mockPathname('/test_page/test_map/test_view?a=1');
    expect(partitionURL('test_page')).toEqual(['/test_page/test_map/', 'test_view']);

    mockPathname('/test_page/test_map/test_view#test_thing');
    expect(partitionURL('test_page')).toEqual(['/test_page/test_map/', 'test_view']);

    mockPathname('/test_page/test_map/test_view/');
    expect(partitionURL('test_page')).toEqual(['/test_page/test_map/', 'test_view']);
  });

  it('no view', () => {
    mockPathname('/test_page/test_map');
    expect(partitionURL('test_page')).toEqual(['/test_page/test_map/', '']);

    mockPathname('/test_page/test_map/');
    expect(partitionURL('test_page')).toEqual(['/test_page/test_map/', '']);

    mockPathname('/test_page/test_map?a=1');
    expect(partitionURL('test_page')).toEqual(['/test_page/test_map/', '']);
  });

  it('invalid page', () => {
    mockPathname('/test_page/test_map/test_view');
    expect(partitionURL('invalid')).toEqual(['', '']);
  });
});

describe('normalizePath', () => {

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should normalize multiple slashes and trigger redirect 1', () => {
    const mockReplace = mockPathname('//test_page/test_map/test_view');
    normalizePath();
    expect(mockReplace).toHaveBeenCalledWith('/test_page/test_map/test_view');
  });

  test('should normalize multiple slashes and trigger redirect 2', () => {
    const mockReplace = mockPathname('////test_page///test_map////test_view');
    normalizePath();
    expect(mockReplace).toHaveBeenCalledWith('/test_page/test_map/test_view');
  });

  test('should not trigger redirect if path is correct', () => {
    const mockReplace = mockPathname('/test_page/test_map/test_view');
    normalizePath();
    expect(mockReplace).not.toHaveBeenCalled();
  });
});
