// Style definition and canvas drawing for edge glosses

import Feature from 'ol/Feature';
import MultiLineString from 'ol/geom/MultiLineString';
import {Style} from 'ol/style';
import {GLOSS_NONE, parseGlossOrDefault} from './glosschoose';

import type Geometry from 'ol/geom/Geometry';
import type {RenderFunction} from 'ol/style/Style';
import type {Coord} from '../context/ol';
import type {Gloss, GlossArrowSize, GlossShape, GlossWeight} from './glosschoose';

type State = Readonly<Parameters<RenderFunction>[1]>;
type Vectors = Readonly<[Coord, Coord][]>;

// in px
const GLOSS_WEIGHT: {[w in GlossWeight]: number} = {
  'XS': 1.5,
  'S': 3,
  'M': 4.5,
  'L': 6,
  'XL': 9,
} as const;

// in <not sure>
const GLOSS_SIZE: {[s in GlossArrowSize]: number} = {
  'XS': 6,
  'S': 12,
  'M': 18,
  'L': 24,
  'XL': 30,
} as const;

const GLOSS_RENDER: {[s in GlossShape]: Renderer} = {
  'S': renderWiggles,
  'C': renderCWCurves,
  'L': renderLines,
  'B': renderLines,
};

function drawArrow(
  context: CanvasRenderingContext2D,
  dx: number,
  dy: number,
  px: number,
  py: number,
  arrow_len: number,
  arrow_rad = Math.PI * 0.25,
): void {
  // TODO: Doing maths at render time, here, could move into custom geometry
  const angle = Math.atan2(dy, dx) + Math.PI;
  const xa = px + Math.cos(angle - arrow_rad) * arrow_len;
  const ya = py + Math.sin(angle - arrow_rad) * arrow_len;
  const xb = px + Math.cos(angle + arrow_rad) * arrow_len;
  const yb = py + Math.sin(angle + arrow_rad) * arrow_len;

  context.moveTo(xa, ya);
  context.lineTo(px, py);
  context.lineTo(xb, yb);
}

function renderWiggles(coords: Vectors, {context, feature, pixelRatio}: State): void {
  const {lineDash, lineWidth, strokeStyle} = feature.get('canvasProperties');
  const arrowSize = (feature.get('arrowStyle')?.size || 0) * pixelRatio;
  context.save();
  context.lineWidth = lineWidth * pixelRatio;
  context.strokeStyle = strokeStyle;
  context.setLineDash(lineDash);
  coords.forEach(([[x1, y1], [x2, y2]]) => {
    context.beginPath();
    context.moveTo(x1, y1);
    const dx = x2 - x1;
    const dy = y2 - y1;
    if (Math.abs(8 * dx) < Math.abs(dy)) {
      // If little change in the x axis, draw a simple arc-like
      const xo = 0.125 * dy;
      const xn = x1 + xo;
      context.bezierCurveTo(xn, y1, xn, y2, x2, y2);
      if (arrowSize) {
        const px = 0.125 * (x1 + x2) + 0.75 * xn;
        const py = y1 + 0.5 * dy;
        drawArrow(context, dx, dy, px, py, arrowSize);
      }
    } else {
      // Otherwise draw an s-shaped curve
      const xm = x1 + 0.5 * dx;
      const ym = y1 + 0.5 * dy;
      context.bezierCurveTo(x1, ym, x2, ym, x2, y2);
      if (arrowSize) {
        drawArrow(context, dx, dy * 0.5, xm, ym, arrowSize);
      }
    }
    context.stroke();
  });
  context.restore();
}

function renderCWCurves(coords: Vectors, {context, feature, pixelRatio}: State): void {
  const {lineDash, lineWidth, strokeStyle} = feature.get('canvasProperties');
  const arrowSize = (feature.get('arrowStyle')?.size || 0) * pixelRatio;
  context.save();
  context.lineWidth = lineWidth * pixelRatio;
  context.strokeStyle = strokeStyle;
  context.setLineDash(lineDash);
  coords.forEach(([[x1, y1], [x2, y2]]) => {
    // vector <dx, dy>
    const dx = x2 - x1;
    const dy = y2 - y1;

    // midpoint of segment <x1y1, x2y2>
    const xm = x1 + 0.5 * dx;
    const ym = y1 + 0.5 * dy;

    // vector <xv, yv> perpendicular to <dx, dy>
    const xv = dy;
    const yv = -dx;

    // scalar s determines curvature size
    const s = 0.25;
    // point <xc, yc> at the tip of s * <xv, yv> with tail at <xm, ym>
    const xc = xm + s * xv;
    const yc = ym + s * yv;

    // point <px, py> at the middle of curve
    const px = 0.5 * (xm + xc);
    const py = 0.5 * (ym + yc);

    context.beginPath();
    context.moveTo(x1, y1);
    context.quadraticCurveTo(xc, yc, x2, y2);
    if (arrowSize) {
      drawArrow(context, dx, dy, px, py, arrowSize);
    }
    context.stroke();
  });
  context.restore();
}

function renderLines(coords: Vectors, {context, feature, pixelRatio}: State): void {
  const {lineDash, lineWidth, strokeStyle, bidirectional} = feature.get('canvasProperties');
  const arrowSize = (feature.get('arrowStyle')?.size || 0) * pixelRatio;
  const arrows = bidirectional ? [0.95, 0.05] : [0.5];
  context.save();
  context.lineWidth = lineWidth * pixelRatio;
  context.strokeStyle = strokeStyle;
  context.setLineDash(lineDash);
  coords.forEach(([[x1, y1], [x2, y2]]) => {
    const dx = x2 - x1;
    const dy = y2 - y1;
    context.beginPath();
    context.moveTo(x1, y1);
    context.lineTo(x2, y2);
    if (arrowSize) {
      drawArrow(context, dx, dy, x1 + arrows[0] * dx, y1 + arrows[0] * dy, arrowSize);
      if (arrows[1] != null) {
        drawArrow(context, -dx, -dy, x1 + arrows[1] * dx, y1 + arrows[1] * dy, arrowSize);
      }
    }
    context.stroke();
  });
  context.restore();
}

type Renderer = (v: Vectors, s: State) => void;
function styleRenderer(renderer: Renderer): Style {
  const resetRenderer = (renderer: Renderer): Renderer => (v, s) => {
    s.context.setLineDash([]);
    renderer(v, s);
  };
  // Cast through unknown as we know more about the coords than OL type does
  return new Style({renderer: resetRenderer(renderer) as unknown as RenderFunction});
}

function getRootCssProperty(property: string) {
  return getComputedStyle(document.documentElement).getPropertyValue(property);
}

function toLineDash(glossFinish: string): number[] {
  try {
    const parsed = JSON.parse(`[${getRootCssProperty(`--gloss-finish-dt-${glossFinish}`)}]`);
    if (Array.isArray(parsed) && parsed.length === 2 && parsed.every(x => typeof x === 'number')) {
      return parsed;
    }
  } catch {
    // noop
  }
  return [];
}

export function featureWithGloss(coords: [Coord, Coord][], gloss: Gloss): Feature<Geometry> {
  const feature = new Feature({
    geometry: new MultiLineString(coords),
  });
  const [shape, weight, arrowSize, finish] = parseGlossOrDefault(gloss);

  const lineWidth = GLOSS_WEIGHT[weight];
  const strokeStyle = getRootCssProperty(`--gloss-finish-fg-${finish}`) || undefined;
  const backgroundStrokeStyle = getRootCssProperty(`--gloss-finish-bg-${finish}`) || undefined;
  const lineDash = toLineDash(finish);
  feature.set('canvasProperties', {lineWidth, strokeStyle, backgroundStrokeStyle, lineDash, bidirectional: shape === 'B'});

  if (arrowSize !== GLOSS_NONE) {
    feature.set('arrowStyle', {size: GLOSS_SIZE[arrowSize]});
  }

  const style = GLOSS_RENDER[shape] && styleRenderer(GLOSS_RENDER[shape]);
  feature.setStyle(style);
  return feature;
}
