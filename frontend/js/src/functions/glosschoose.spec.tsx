import {parseGloss} from './glosschoose';

describe('parseGloss', () => {
  it('valid', () => {
    ([
      ['gloss:S:M:S:causal', ['S', 'M', 'S', 'causal']],
      ['trust:S:M:S:negative', ['S', 'M', 'S', 'negative']],
      ['gloss:BS:M:S:causal', ['BS', 'M', 'S', 'causal']],
      ['gloss:test1:test12:test3:test4', ['test1', 'test12', 'test3', 'test4']],
    ] as [string, string[]][]).forEach(([input, expected]) => {
      const result = parseGloss(input);
      expect(result).toEqual(expected);
    });
  });

  it('bidirectional', () => {
    ([
      ['gloss:B:M:S:a', ['B', 'M', 'S', 'a']],
      ['gloss:B:M::a', ['B', 'M', 'M', 'a']],
    ] as [string, string[]][]).forEach(([input, expected]) => {
      const result = parseGloss(input);
      expect(result).toEqual(expected);
    });
  });

  it('defaulting', () => {
    ([
      ['gloss::M:S:', ['L', 'M', 'S', 'default']],
      ['gloss::::', ['L', 'M', 'none', 'default']],
    ] as [string, string[]][]).forEach(([input, expected]) => {
      const result = parseGloss(input);
      expect(result).toEqual(expected);
    });
  });

  it('invalid', () => {
    [
      'gloss',
      'notgloss::::',
      'notgloss:S:S:S:default',
      'gloss:S:S',
    ].forEach((input) => {
      const result = parseGloss(input);
      expect(result).toEqual(null);
    });
  });
});
