import {djangoCsrfTokenValue} from '../django';

export const logoutUser = (): void => {
  const currentUrl = window.location.pathname + window.location.search + window.location.hash;

  const encodedNext = encodeURIComponent(
    `/signout?next=${encodeURIComponent(currentUrl)}&logout-intent=frontend`,
  );

  const form = document.createElement('form');
  form.method = 'post';
  form.action = `/accounts/logout/?next=${encodedNext}`;

  const csrfInput = document.createElement('input');
  csrfInput.type = 'hidden';
  csrfInput.name = 'csrfmiddlewaretoken';
  csrfInput.value = djangoCsrfTokenValue();

  form.appendChild(csrfInput);
  document.body.appendChild(form);
  form.submit();
};
