// Resolve item properties into colors from CSS variables

import {IRIS} from '../rdf';

import type {MapModel} from '../rdfmodels';
import type {Existence} from '../types';

type Context = 'default' | 'overlay';
type ColorResolver = (iri: string) => string|null;
export type ColorRGB = {
  r: number;
  g: number;
  b: number;
};

const styles = getComputedStyle(document.documentElement);

export function asColor(varName: string): string {
  return styles.getPropertyValue(varName);
}

export function getRGB(varName: string): ColorRGB {
  const val = asColor(varName);
  const rgb = val.split(',');
  return {r: +rgb[0], g: +rgb[1], b: +rgb[2]};
}

export function colorRGBWithOpacity(color: ColorRGB, backgroundColor: ColorRGB, opacity: number): ColorRGB {
  opacity = opacity > 1 ? 1 : opacity < 0 ? 0 : opacity;
  return {
    r: (1 - opacity) * backgroundColor.r + opacity * color.r,
    g: (1 - opacity) * backgroundColor.g + opacity * color.g,
    b: (1 - opacity) * backgroundColor.b + opacity * color.b,
  };
}

export function makeJudgementResolver(mm: MapModel, cssContext: Context): ColorResolver {
  const suffix = cssContext === 'default' ? '' : '-' + cssContext;

  const judgementToColor = {
    [IRIS.VM.judgedNegative]: asColor('--color-rag-negative' + suffix),
    [IRIS.VM.judgedNeutral]: asColor('--color-rag-neutral' + suffix),
    [IRIS.VM.judgedPositive]: asColor('--color-rag-positive' + suffix),
  };

  return iri => {
    const categories = iri && mm.categoriesOf(iri);
    if (categories) {
      for (const category in judgementToColor) {
        if (categories[category]) {
          return judgementToColor[category];
        }
      }
    }
    return null;
  };
}

export function makeScoreResolver(mm: MapModel): ColorResolver {
  const scoreToColor = (s: number) => asColor('--color-score-' + s);

  return iri => {
    const score = Number(iri && mm.hasScore(iri));
    if (isNaN(score) || score < 1 || score > 5) {
      return null;
    }

    return scoreToColor(score);
  };
}

export function makeCategoryResolver(mm: MapModel, colorCategoryTypes: Existence): ColorResolver {
  return iri => {
    const categories = iri && mm.categoriesOf(iri) || {};
    for (const category in categories) {
      if (colorCategoryTypes[mm.classOf(category)]) {
        const cssVariable = mm.cssVariableOf(category);
        return cssVariable && asColor(cssVariable) || null;
      }
    }
    return null;
  };
}
