// Helpful functions for graphs

export type ParentChildrenGraph = {
  [iri: string]: string[];
};

export function findRoots(graph: ParentChildrenGraph): string[] {
  const roots: string[] = [];
  const parentNodes = Object.keys(graph);
  const childNodes = new Set<string>();

  for (const parent of parentNodes) {
    graph[parent].forEach((key) => childNodes.add(key));
  }

  for (const parent of parentNodes) {
    if (!childNodes.has(parent)) {
      roots.push(parent);
    }
  }

  return roots;
}

// Does assume a correct forest
export function toBfs(forest: ParentChildrenGraph, roots?: string[]): string[] {
  roots ??= findRoots(forest);
  const result = [...roots];
  let current: string;
  let i = 0;
  while ((current = result[i++])) {
    result.push(...(forest[current] || []));
  }
  return result;
}
