// Functions for handling SVG path minilanguage

import type {Coord} from '../context/ol';
import type {Paths, Poly} from '../types';

export function boundingBox(polyPath: Poly|null): Coord[]|null {
  if (!polyPath) {
    return null;
  }

  if (Array.isArray(polyPath)) {
    return polyPath;
  }

  const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
  const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
  path.setAttribute('d', polyPath);
  svg.appendChild(path);

  const container = document.createElement('div');
  container.style.display = 'none';
  container.appendChild(svg);
  document.body.appendChild(svg);

  const {x, y, width, height} = path.getBBox();
  document.body.removeChild(svg);

  return [[x, y], [x + width, y + height]];
}

export function buildArea(path: Poly): string {
  if (typeof path === 'string') {
    return path + 'Z';
  }
  const d: string[] = [];
  let instruction = 'M';
  for (const point of path) {
    d.push(`${instruction}${point}`);
    instruction = 'L';
  }
  d.push('Z');
  return d.join(' ');
}

export function buildAreaOrRect(path: Poly): string {
  if (Array.isArray(path) && path.length === 2) {
    const [x1, y1, x2, y2] = path.flatMap(x => x);
    return `M${x1},${y1} H${x2} V${y2} H${x1} Z`;
  }
  return buildArea(path);
}

export function buildLines(paths: Paths): string {
  if (typeof paths === 'string') {
    return paths;
  }
  const d: string[] = [];
  for (const path of paths) {
    if (typeof path === 'string') {
      d.push(path);
      continue;
    }
    let instruction = 'M';
    for (const point of path) {
      d.push(`${instruction}${point}`);
      instruction = 'L';
    }
  }
  return d.join(' ');
}

export function pathsCount(paths: Paths): number {
  if (Array.isArray(paths)) {
    return paths.length;
  }
  // Assumes each new separated path will starts with an M/m command.
  return paths.split(new RegExp('m', 'i')).length - 1;
}
