/*
 * Plain text extractor plugin for markdown-it.
 *
 * https://github.com/wavesheep/markdown-it-plain-text
 *
 * Copyright (c) 2020 wavesheep.

Permission is hereby granted, free of charge, to any person
obtaining a copy of this software and associated documentation
files (the "Software"), to deal in the Software without
restriction, including without limitation the rights to use,
copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the
Software is furnished to do so, subject to the following
conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
OTHER DEALINGS IN THE SOFTWARE.
 */

import type MarkdownIt from 'markdown-it';
import type {Token} from 'markdown-it';

export function plainTextPlugin(md : MarkdownIt): void {
  const scan = function(tokens : Token[]) {
    let text = '';
    const tokenTypes = ['text', 'fence', 'html_block', 'code_block', 'code_inline', 'html_inline', 'emoji'];
    for (let i = 0; i < tokens.length; i++) {
      const token = tokens[i];
      if (token.children !== null) {
        text += scan(token.children);
      } else {
        if (tokenTypes.includes(token.type)) {
          text += token.content;
        } else if (/[a-zA-Z]+_close/.test(token.type)) { // prevent words from sticking together
          text += ' ';
        }
      }
    }
    return text;
  };
  const plainTextRule = function(state: {tokens: Token[]}) {
    const text = scan(state.tokens);
    // remove redundant white spaces
    // @ts-expect-error see https://github.com/wavesheep/markdown-it-plain-text?tab=readme-ov-file#typescript
    md.plainText = text.replace(/\s+/g, ' ');
  };
  md.core.ruler.push('plainText', plainTextRule);
}
