import {buildIndex as buildSearchIndex} from './buildsearchindex';
import {IRIS} from './rdf';
import {buildModel} from './testing/model';

import type {IndexResultItem, SearchEngineResult} from './context/search';
import type {MapModel} from './rdfmodels';

const mockRenderer = {
  plaintext: (text : string) => text,
  inline: (s: string) => ({children: s}),
  render: (s: string) => ({children: s}),
};

const buildIndex = (mm: MapModel, lang: string = 'en') => buildSearchIndex(mm, mockRenderer, lang);

const build = (subs: [string, string, string][]): MapModel => (
  buildModel(subs.map(([subj, pred, obj]) => ({subj, pred, obj})))
);

const buildResult = (expected: [string, {iri: number}][]) => {
  const fieldResult = (fieldName : string, iris: {[key: string]: number}) => {
    const result: IndexResultItem[] = [];
    Object.keys(iris).forEach((iri) => {
      result.push({
        doc: {
          subjectIRI: iri,
        },
        id: iris[iri],
      });
    });
    return {
      field: fieldName,
      result,
    };
  };
  const result: {field: string; result: IndexResultItem[]}[] = [];
  expected.forEach(([fieldName, iris]) => {
    result.push(fieldResult(fieldName, iris));
  });
  return result;
};

describe('Search name only', () => {
  const build = (subs: [string, string][]): MapModel => (
    buildModel(subs.map(([subj, obj]) => ({subj, pred: IRIS.RDFS.label, obj})))
  );

  it('empty model', () => {
    expect(buildIndex(buildModel([])).contain(1)).toEqual(false);
  });

  it('search single word without special characters', () => {
    const index = buildIndex(build([
      ['P1', 'cat'], ['P2', 'dog cat'],
      ['P3', 'catherine'], ['P4', 'allocate'],
      ['P5', 'cat&dog'], ['P6', 'ca'],
    ]));
    expect((((index.search('cat') as SearchEngineResult[])[0])).result).toEqual([1, 3, 2, 5]);
    expect((index.search('dog') as SearchEngineResult[])[0].result).toEqual([2, 5]);
  });

  it('accented letters', () => {
    const index = buildIndex(build([['P1', 'Siân']]));
    expect((index.search('Siân') as SearchEngineResult[])[0].result).toEqual([1]);
    expect((index.search('Sian') as SearchEngineResult[])[0].result).toEqual([1]);
  });

  it('search single word with special character', () => {
    const index = buildIndex(build([
      ['P1', 'cat&dog?'], ['P2', 'dog cat&dog?'],
      ['P3', '**cat&dog?'], ['P4', 'dog'], ['P5', 'cat&dog'],
    ]));
    expect((index.search('cat&dog?') as SearchEngineResult[])[0].result).toEqual([1, 3, 5, 2]);
  });

  it('search single word with special character at the start', () => {
    const index = buildIndex(build([
      ['P1', '(cat)'], ['P2', '(cat) dog'],
      ['P3', 'dog (cat)'], ['P4', 'dog(cat)'],
    ]));
    expect((index.search('(cat)') as SearchEngineResult[])[0].result).toEqual([1, 2, 3, 4]);
  });

  it('search multiple words without special characters', () => {
    const index = buildIndex(build([
      ['P1', 'Tom Cruise'], ['P2', 'Cruise Tom'],
      ['P3', 'Tom-Cruise'], ['P4', 'Tom - cruise'],
      ['P5', '(Tom) cruise'], ['P6', 'Tom-Edward Cruises'],
      ['P7', 'tom'], ['P8', 'tom c'],
    ]));
    expect((index.search('tom Cruise') as SearchEngineResult[])[0].result).toEqual([1, 2, 3, 4, 5, 6]);
  });

  it('search multiple words with special characters', () => {
    const index = buildIndex(build([
      ['P1', 'V&A - East:'], ['P2', 'V&A - East: Users'],
      ['P3', 'Users - East: V&A'], ['P4', 'V&A - East: Users&Customers'],
      ['P5', 'V&A'], ['P6', 'V&A-East:'],
    ]));
    expect((index.search('V&A - East:') as SearchEngineResult[])[0].result).toEqual([1, 6, 2, 3, 4]);
  });

  it('search multiple words with special characters at the start', () => {
    const index = buildIndex(build([
      ['P1', 'V&A - (East)'], ['P2', 'V&A - (East): Users'],
      ['P3', 'Users - (East): V&A'], ['P4', 'V&A - (East): Users&Customers'],
      ['P5', 'V&A'], ['P6', 'V&A-(East)'],
    ]));
    expect((index.search('V&A - (East)') as SearchEngineResult[])[0].result).toEqual([1, 6, 2, 3, 4]);
  });

  test('search in Chinese', () => {
    const index = buildIndex(build([
      ['P1', '一个单词'], ['P2', '一个单词好不好'],
      ['P3', '一个'], ['P4', 'V&A - (East): Users&Customers'],
    ]), 'zh');
    expect((index.search('单词') as SearchEngineResult[])[0].result).toEqual([1, 2]);
  });
});

describe('Search whole contents', () => {
  const index = buildIndex(build([
    ['iri_cat', IRIS.VM.name, 'cat'],
    ['iri_cat', IRIS.RDFS.label, 'meow'],
    ['iri_cat', IRIS.VM.description, 'a pretty cat that can meow'],
    ['iri_dog', IRIS.VM.name, 'dog'],
    ['iri_dog', IRIS.VM.abbreviation, 'DG'],
    ['iri_dog', IRIS.SKOS.altLabel, 'puppy'],
    ['iri_dog', IRIS.VM.description, 'a big dog'],
    ['iri_fox', IRIS.VM.name, 'fox'],
    ['iri_fox', IRIS.VM.description, 'what does the fox say'],
    ['iri_equipment', IRIS.VM.name, 'equipment'],
    ['iri_familiyfriends', IRIS.VM.name, 'Family and Friends'],
    ['iri_getting', IRIS.VM.name, 'Getting worse'],
    ['iri_socialworkers', IRIS.VM.name, 'Social Workers'],
  ]));

  test.each([
    ['CAT', [['name[]', {'iri_cat': 1}], ['description', {'iri_cat': 1}]]],
    ['meow', [['name[]', {'iri_cat': 1}], ['description', {'iri_cat': 1}]]],
    ['ca', [['name[]', {'iri_cat': 1}]]],
    ['a pretty', [['description', {'iri_cat': 1}]]],
    ['dog', [['name[]', {'iri_dog': 2}], ['description', {'iri_dog': 2}]]],
    ['DG', [['alts[]', {'iri_dog': 2}]]],
    ['puppy', [['name[]', {'iri_dog': 2}]]],
    ['blah', []],
    ['ick', []],
    ['wurce', []],
    // multiple terms within an index
    ['CAT meow', [['name[]', {'iri_cat': 1}]]],
    // ideally this should match getting
    ['geting', []],
    // future: searches across multiple indexes
  ])('search for %s', (input, expected) => {
    expect(buildResult(expected as [string, {iri: number}][])).toEqual(index.search(input, {enrich: true}));
  });
});

describe('Search model with index properties', () => {
  const index = buildIndex(build([
    ['iri_cat', IRIS.VM.name, 'milo'],
    ['iri_cat', IRIS.RDFS.label, 'meow'],
    ['iri_cat', 'http://visual-meaning.com/rdf/ofBreed', 'shorthair cat'],
    ['iri_cat', 'http://visual-meaning.com/rdf/from', 'Oxford'],
    ['iri_dog', IRIS.VM.name, 'milo'],
    ['iri_dog', IRIS.VM.abbreviation, 'DG'],
    ['iri_dog', IRIS.SKOS.altLabel, 'puppy'],
    ['iri_dog', 'http://visual-meaning.com/rdf/ofBreed', 'husky dog'],
    ['iri_dog', 'http://visual-meaning.com/rdf/from', 'Oxford'],
    ['iri_fox', IRIS.VM.name, 'milo'],
    ['iri_fox', 'http://visual-meaning.com/rdf/ofBreed', 'shorthair fox'],
    ['http://visual-meaning.com/rdf/ofBreed', IRIS.RDF.type, IRIS.VM.IndexProperty],
    ['http://visual-meaning.com/rdf/from', IRIS.RDF.type, IRIS.VM.IndexProperty],
  ]));

  test.each([
    ['milo', [['name[]', {'iri_cat': 1, 'iri_dog': 2, 'iri_fox': 3}], ['keywords[]', {'iri_cat': 1, 'iri_dog': 2, 'iri_fox': 3}]]],
    ['milo shorthair cat', [['keywords[]', {'iri_cat': 1}]]],
    ['meow shorthair', [['keywords[]', {'iri_cat': 1}]]],
    ['milo meow cat', [['keywords[]', {'iri_cat': 1}]]],
    ['milo oxford shorthair', [['keywords[]', {'iri_cat': 1}]]],
    ['milo husky oxford dog', [['keywords[]', {'iri_dog': 2}]]],
    ['milo short', [['keywords[]', {'iri_cat': 1, 'iri_fox': 3}]]],
    // terms from one index
    ['oxford shorthair', [['keywords[]', {'iri_cat': 1}]]],
    ['milo meow', [['name[]', {'iri_cat': 1}], ['keywords[]', {'iri_cat': 1}]]],
  ])('search for %s', (input, expected) => {
    expect(buildResult(expected as [string, {iri: number}][])).toEqual(index.search(input, {enrich: true}));
  });
});

describe('Search model - ordering result', () => {
  it('reversed index definition', () => {
    const triples: [string, string, string][] = [
      ['iri_cat', IRIS.VM.name, 'arlo'],
      ['iri_cat', 'http://visual-meaning.com/rdf/ofBreed', 'bobtail cat'],
      ['iri_cat', 'http://visual-meaning.com/rdf/from', 'Oxford'],
      ['iri_dog', IRIS.VM.name, 'milo'],
      ['iri_dog', 'http://visual-meaning.com/rdf/from', 'Oxford'],
    ];

    const input = 'oxford';
    const expected = [['keywords[]', {'iri_cat': 1, 'iri_dog': 2}]];
    const expectedResult = buildResult(expected as [string, {iri: number}][]);

    const index = buildIndex(build([
      ...triples,
      ['http://visual-meaning.com/rdf/ofBreed', IRIS.RDF.type, IRIS.VM.IndexProperty],
      ['http://visual-meaning.com/rdf/from', IRIS.RDF.type, IRIS.VM.IndexProperty],
    ]));

    expect(expectedResult).toEqual(index.search(input, {enrich: true}));

    const reversedIndex = buildIndex(build([
      ...triples,
      ['http://visual-meaning.com/rdf/from', IRIS.RDF.type, IRIS.VM.IndexProperty],
      ['http://visual-meaning.com/rdf/ofBreed', IRIS.RDF.type, IRIS.VM.IndexProperty],
    ]));

    expect(expectedResult).toEqual(reversedIndex.search(input, {enrich: true}));
  });
});

