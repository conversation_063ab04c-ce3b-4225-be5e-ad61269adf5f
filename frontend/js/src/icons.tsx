// Selection of icons for thing
/** @jsx h */

import {h} from 'preact';
import {useContext} from 'preact/hooks';

import {AssetsContext} from './context/assets';
import {useModel} from './context/triples';
import {IRIS} from './rdf';

import type {RNode} from './types';
import type {Assets} from './context/assets';
import type {MapModel} from './rdfmodels';

export type FallbackIcon = {backgroundImage: string; className: string}|string;

const regionCache: {[r: string]: FallbackIcon} = {};
export function iconForRegion(mm: MapModel, region: string): FallbackIcon {
  const getValue = () => {
    const code: string|undefined = mm._getField(region, IRIS.VM.countryCode);
    if (code) {
      return {
        backgroundImage: `url(//opatlas-live.s3.amazonaws.com/flags/${code.toUpperCase()}.svg)`,
        className: 'vm-icon-flag',
      };
    }
    const comment: string|undefined = mm._getField(region, IRIS.VM.comment);
    if (comment) {
      return comment;
    }
    return 'R';
  };

  return regionCache[region]
    ? regionCache[region]
    : regionCache[region] = getValue();
}

function maybePrimaryCategoryIcon(mm: MapModel, iri: string, fromFilename: Assets['fromFilename']): string|null {
  const relation = mm.primaryCategoryRelationOf(iri);
  if (!relation) {
    return null;
  }
  return fromFilename(mm.categoryIconOf(relation.category, relation.predicate));
}

export function iconFor(mm: MapModel, assets: Assets, iri: string, classIri: string): string|null {
  const {fromFilename, BRIGHT_ICON, CAPTYPE_ICON, CONTENTTYPE_ICON} = assets;
  const categoryIcon = maybePrimaryCategoryIcon(mm, iri, fromFilename);
  if (categoryIcon) {
    return categoryIcon;
  }

  switch (classIri) {
    case IRIS.VM.Brightspot:
      return BRIGHT_ICON[mm.capitalTypeOf(iri) ?? 'unspecified'];
    case IRIS.VM.Painpoint:
      return CAPTYPE_ICON[mm.capitalTypeOf(iri)];
    case IRIS.VM.ContentItem:
      return CONTENTTYPE_ICON[mm.contentTypeOf(iri)];
    case IRIS.OWL.Class:
      return mm.iconOf(classIri);
    case null:
      return null;
    default:
      return mm.iconOf(classIri) ?? fromFilename(mm.iconOf(iri));
  }
}

export function iconForClass(mm: MapModel, assets: Assets, classIri: string): string|null {
  const {fromFilename} = assets;
  return fromFilename(mm.iconOf(classIri));
}

const ENCLOSED_REGEX_OFFSET_MASK: [RegExp, number, number][] = [
  [/[0-9]/, 0x245f, 0xF],
  [/[A-Za-z]/, 0x24b5, 0x1F],
  [/[?]/, 0x2bd0, 0x1],
];

export function enclosedKnownSymbolFor(str: string): string {
  const char = str[0];
  for (const [regex, offset, mask] of ENCLOSED_REGEX_OFFSET_MASK) {
    if (regex.test(char)) {
      return String.fromCodePoint(offset + (char.charCodeAt(0) & mask));
    }
  }
  return '';
}

export function tagFor(mm: MapModel, classIri: string): string|null {
  let typeName = classIri && mm.className(classIri);
  // Technically a bug if this symbol is displayed
  if (!typeName) {
    typeName = '?';
  }
  // Fix up basic view type till schema gets reworked
  if (typeName === 'Story') {
    typeName = 'View';
  }
  return enclosedKnownSymbolFor(typeName);
}

function resolveIcon(mm: MapModel, icon: string|null, classIri: string, offset?: boolean, tooltip?: boolean) {
  const className = offset ? 'vm-i offset' : 'vm-i';
  const title = tooltip && classIri && mm.className(classIri) || null;
  if (icon) {
    return <img className={className} title={title} src={icon} />;
  }
  const tag = tagFor(mm, classIri);
  return <span className={className} title={title}>{tag}</span>;
}

export type Props = Readonly<{
  iri: string;
  offset?: boolean;
  tooltip?: boolean;
}>;

export function Icon({iri, offset, tooltip}: Props): RNode {
  const assets = useContext(AssetsContext);
  const mm = useModel();
  const classIri = mm.classOf(iri);
  const icon = iconFor(mm, assets, iri, classIri);

  return resolveIcon(mm, icon, classIri, offset, tooltip);
}

export function ClassIcon({iri, offset, tooltip}: Props): RNode {
  const assets = useContext(AssetsContext);
  const mm = useModel();
  const icon = iconForClass(mm, assets, iri);

  return resolveIcon(mm, icon, iri, offset, tooltip);
}
