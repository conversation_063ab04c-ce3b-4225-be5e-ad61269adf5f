// Build flexsearch index

import {Charset, Document} from 'flexsearch';

import {IRIS} from './rdf';

import type {MapModel} from './rdfmodels';
import type {Renderer} from './context/mark';
import type {Triple} from './rdf';

function dedupe<T>(arr: T[]): T[] {
  return arr.filter((a, i) => arr.indexOf(a) === i);
}

const RESOLUTION = 18;
const TERM_SEPARATOR = '\n';

// Use private cache from triplestore but must be careful not to modify it
type SubjCache = Readonly<{[subj: string]: Triple[]}>;

type CharsetType = typeof Charset;
type EncoderOptions = CharsetType[keyof CharsetType];

/**
 * According to author, the Charset.Default should be good enough to handle
 * the simplified CjkDefault, hence  it is removed. The Chinese text test is
 * failing though without this encoding.
 *
 * CJK Encoder Options:
 * https://github.com/nextapps-de/flexsearch/blob/1cbd1b33fe9088916a2e5e2d0b2523fc2497ab46/src/charset/cjk/default.js
 */
const CjkDefault: EncoderOptions = {
  normalize: !1,
  dedupe: !0,
  split: '',
  prepare(str) {
    // eslint-disable-next-line no-control-regex
    return ('' + str).replace(/[\x00-\x7F]+/g, '');
  },
};

/**
 * Builds a scoring function for the forward index. It is based on the default
 * scoring function:
 * https://github.com/nextapps-de/flexsearch/blob/112a07725f72cdbf59ba96dfddf3b826bc53708b/src/index/add.js#L267-L296
 *
 * It calculates the score of a term in the index based on its position,
 * but now it also accounts for length of the content.
 *
 * It is more biased towards content length, and has higher score if index is
 * higher, e.g. in case index1 + length1 and index2 + length2 are equal.
 */
function forwardCompletnessScorer(resolution: number)
  : (content: string[], term: string, termIndex: number, partial: string|null, partialIndex: number) => number {
  return (content, _t, ti, _p, _pi) => {
    const length = content.length;
    return 2 * length <= resolution
      ? ((ti * 0.9) | 0) + length
      : length <= resolution
        ? ((resolution - length - 1) / length * (ti + 1) | 0) + length
        : resolution - 1;
  };
}

type Options = {
  [lang: string]: {
    encoder: EncoderOptions;
  };
};
type Doc = {
  id: number;
  name: string[];
  alts: string[];
  keywords: string[];
  description: string;
  subjectIRI: string;
};

/**
 * Builds an flexsearch index document from the provided MapModel, using a specified language and renderer.
 * The index is configured with fields for names, alternative names, keywords, and descriptions,
 * supporting multilingual tokenization and search optimization.
 *
 * @param {MapModel} mm - The MapModel instance.
 * @param {Renderer} kd - The renderer instance used for processing and formatting text content.
 * @param {string} [lang='en'] - The language to configure the index for, defaults to English ('en').
 *                               Current supported languages include 'en' (English) and 'zh' (Chinese).
 * @returns {Document} A Document instance containing the indexed data from the MapModel.
 */
export function buildIndex(mm: MapModel, kd: Renderer, lang: string = 'en'): Document {
  const score = forwardCompletnessScorer(RESOLUTION);
  const langOptions : Options = {
    en: {
      encoder: Charset.Default,
    },
    zh: {
      encoder: CjkDefault,
    },
  };
  if (!(lang in langOptions)) {
    lang = 'en';
  }
  const index = new Document({document: {
    id: 'id',
    index: [
      {
        field: 'name[]',
        tokenize: 'forward',
        score,
        resolution: RESOLUTION,
        ...langOptions[lang],
      },
      {
        field: 'alts[]',
        tokenize: 'strict',
        ...langOptions[lang],
      },
      {
        field: 'keywords[]',
        tokenize: 'forward',
        ...langOptions[lang],
      },
      {
        field: 'description',
        tokenize: 'strict',
        resolution: 5,
        context: {
          depth: 1,
          resolution: 3,
          bidirectional: true,
        },
        encoder: {
          ...langOptions[lang].encoder,
          minlength: 3,
        },
      },
    ],
    store: ['subjectIRI'],
  }});
  const _subjs: SubjCache = mm._store._getSubjCache();
  const _indexPropertites = mm._indexProperties();
  let count = 1;

  type MemoInfo = {
    name: string[];
    includeDocument: boolean;
    alts?: string[];
    keywords?: string[];
    description?: string;
    additionalData?: string[];
  };

  const memoed: {[subj: string]: MemoInfo} = {};

  const iterate = (subj: string): MemoInfo => {
    if (memoed[subj]) {
      return memoed[subj];
    }

    memoed[subj] = {name: [], includeDocument: false};
    const indexedObj: string[] = [];

    const triples = _subjs[subj];
    if (!triples || !Array.isArray(triples)) {
      return memoed[subj];
    }

    _subjs[subj].forEach((t: Triple) => {
      switch (t.pred) {
        case IRIS.VM.name:
        case IRIS.RDFS.label:
          memoed[subj]['includeDocument'] = true;
          // fall through
        case IRIS.SKOS.altLabel:
        case IRIS.VM.aliasing:
          memoed[subj]['name'].push(t.obj);
          break;

        case IRIS.VM.abbreviation:
        case IRIS.VM.initialism:
        case IRIS.VM.acronym:
          (memoed[subj]['alts'] ??= []).push(t.obj);
          break;

        case IRIS.VM.description:
          memoed[subj]['description'] = kd.plaintext(t.obj);
          break;
      }

      // While iterating through subj, we might hit a _indexPropertites before all subj name/labels are set,
      // so in the case of reverse _indexPropertites for t.obj, which tries to get all t.subj names, they won't have been populated.
      // To ensure names are always populated, we add the object names afterwards.
      if (_indexPropertites[t.pred]) {
        indexedObj.push(t.obj);
      }
    });

    memoed[subj]['name'] = dedupe(memoed[subj]['name']);

    // Process objects from indexed properties
    indexedObj.forEach((obj) => {
      const objInfo = iterate(obj);
      (memoed[subj]['additionalData'] ??= []).push(...(objInfo['name'].length > 0 ? objInfo['name'] : [obj]));
    });

    if (memoed[subj]['additionalData']) {
      memoed[subj]['additionalData'] = dedupe(memoed[subj]['additionalData']);
    }

    return memoed[subj];
  };

  for (const subj in _subjs) {
    const doc: Doc = {
      id: count,
      name: [],
      alts: [],
      keywords: [],
      description: '',
      subjectIRI: subj,
    };

    iterate(subj);

    doc['name'] = memoed[subj]['name'];
    doc['alts'] = memoed[subj]['alts'] || [];
    doc['description'] = memoed[subj]['description'] || '';

    // Generate keywords
    doc['name'].forEach((subjName) => {
      memoed[subj]['additionalData']?.forEach(data => {
        doc['keywords'].push([subjName, data].join(TERM_SEPARATOR));
      });
    });

    // Add to index if includeDocument is true
    if (memoed[subj]['includeDocument']) {
      index.add(doc);
      count += 1;
    }
  }

  return index;
}
