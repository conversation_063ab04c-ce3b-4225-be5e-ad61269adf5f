import {buildSearch} from './search';

describe('buildSearch', () => {
  it('single word without special character', () => {
    const testString = 'cat';
    expect(buildSearch(testString).test('cat')).toBeTruthy();
    expect(buildSearch(testString).test('dog cat')).toBeTruthy();
    expect(buildSearch(testString).test('catherine')).toBeTruthy();
    expect(buildSearch(testString).test('allocate')).toBeFalsy();
    expect(buildSearch(testString).test('dog')).toBeFalsy();
    expect(buildSearch(testString).test('ca')).toBeFalsy();
  });

  it('word containing test string should not be found', () => {
    const testString = 'col';
    expect(buildSearch(testString).test('école')).toBeFalsy();
  });

  it('single word with special characters', () => {
    const testString = 'cat&dog?';
    expect(buildSearch(testString).test('cat&dog?')).toBeTruthy();
    expect(buildSearch(testString).test('dog cat&dog?')).toBeTruthy();
    expect(buildSearch(testString).test('**cat&dog?')).toBeTruthy();
    expect(buildSearch(testString).test('dog')).toBeFalsy();
    expect(buildSearch(testString).test('cat&dog')).toBeFalsy();
  });

  it('single word with special character at the start', () => {
    const testString = '(cat)';
    expect(buildSearch(testString).test('(cat)')).toBeTruthy();
    expect(buildSearch(testString).test('(cat) dog')).toBeTruthy();
    expect(buildSearch(testString).test('dog (cat)')).toBeTruthy();
    expect(buildSearch(testString).test('dog(cat)')).toBeFalsy();
  });

  it('multiple words without special characters', () => {
    const testString = 'tom Cruise';
    expect(buildSearch(testString).test('Tom Cruise')).toBeTruthy();
    expect(buildSearch(testString).test('Cruise Tom')).toBeTruthy();
    expect(buildSearch(testString).test('Tom-Cruise')).toBeTruthy();
    expect(buildSearch(testString).test('Tom - cruise')).toBeTruthy();
    expect(buildSearch(testString).test('(Tom) cruise')).toBeTruthy();
    expect(buildSearch(testString).test('Tom-Edward Cruises')).toBeTruthy();
    expect(buildSearch(testString).test('tom')).toBeFalsy();
    expect(buildSearch(testString).test('tom c')).toBeFalsy();
  });

  it('multiple words with special characters', () => {
    const testString = 'V&A - East:';
    expect(buildSearch(testString).test('V&A - East:')).toBeTruthy();
    expect(buildSearch(testString).test('V&A - East: Users')).toBeTruthy();
    expect(buildSearch(testString).test('Users - East: V&A')).toBeTruthy();
    expect(buildSearch(testString).test('V&A - East: Users&Customers')).toBeTruthy();
    expect(buildSearch(testString).test('V&A')).toBeFalsy();
    expect(buildSearch(testString).test('V&A-East:')).toBeFalsy();
  });

  it('multiple words with special characters at the start', () => {
    const testString = 'V&A - (East)';
    expect(buildSearch(testString).test('V&A - (East)')).toBeTruthy();
    expect(buildSearch(testString).test('V&A - (East): Users')).toBeTruthy();
    expect(buildSearch(testString).test('Users - (East): V&A')).toBeTruthy();
    expect(buildSearch(testString).test('V&A - (East): Users&Customers')).toBeTruthy();
    expect(buildSearch(testString).test('V&A')).toBeFalsy();
    expect(buildSearch(testString).test('V&A-(East)')).toBeFalsy();
  });
});
