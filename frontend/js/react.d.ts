// Re-export most of preact/compat to make typings of react libraries work

export type {
  Context,
  Provider,
} from 'preact';

// TODO: This one can't be `export type` or react-intl is unhappy?
export {
  ComponentType,
  FC,
  ForwardRefExoticComponent,
  PropsWithChildren,
  PropsWithoutRef,
  PureComponent,
  ReactElement,
  ReactNode,
  Ref,
  RefAttributes,
  JSX,
} from 'preact/compat';

export type ElementType<T> = preact.JSX.ElementType<T>;

// Doesn't matter, is an unused alternate.
export type ReactHTML = unknown;

// Added to satisfy react-intl@7.1.7 and above, which expects React.Children.toArray to exist.
// Preact's compat layer does not provide this API, so declare it here to avoid TS errors.
export namespace Children {
  export function toArray(children: import('preact').ComponentChildren): import('preact').ComponentChildren[];
}
