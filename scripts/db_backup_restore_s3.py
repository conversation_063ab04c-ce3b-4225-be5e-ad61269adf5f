#!/usr/bin/env python3

# Copyright 2023 Visual Meaning Ltd

"""Backup and restore Heroku Postgres databases."""

import argparse
import datetime
import json
import os
import subprocess
import sys

import boto3


# If the AWS credentials are set in the env vars, use them. (Heroku scheduler.)
# Otherwise, use defaults from AWS CLI credentials. (Running locally.)
client_kwargs = {
    'aws_access_key_id': os.environ.get('BACKUP_AWS_ACCESS_KEY'),
    'aws_secret_access_key': os.environ.get('BACKUP_AWS_SECRET_KEY'),
}
client_kwargs = {k: v for k, v in client_kwargs.items() if v}
s3_client = boto3.client('s3', **client_kwargs)

TMP_BACKUP = 'pg_backup.dump'


def _delete_if_exists(path):
    os.remove(path) if os.path.exists(path) else None


def clean_files():
    for path in [TMP_BACKUP, TMP_BACKUP + '.gpg']:
        _delete_if_exists(path)


def backup(heroku_app, gpg_password, backup_bucket):
    """Backup a database attached to a Heroku app to S3 via pg_dump."""
    # This can be run either locally or as a scheduled job on the Heroku app. If it's scheduled
    # we get the DB url from the built-in DATABASE_URL environment variable.
    db_url = os.environ.get('DATABASE_URL')
    # if running locally (i.e. not on Heroku) and no DATABASE_URL set, get from app config
    if not db_url:
        db_url = subprocess.check_output(
            ['heroku', 'config:get', 'DATABASE_URL', '--app', heroku_app]
        ).decode('utf8').strip()

    # Make sure we're starting from a clean environment.
    clean_files()

    # Take backup of DATABASE_URL via pg_dump.
    # This command is a reasonable reproduction of the heroku pg:backups:capture output.
    with open(TMP_BACKUP, 'w') as f:
        subprocess.check_call(
            [
                'pg_dump',
                # compressed format
                '-Fc',
                # don't copy roles or table owners
                '--no-acl',
                '--no-owner',
                # enclose table names, column names etc. in quotes. Restore will break
                # if this isn't done, probably because of some dodgy row data.
                '--quote-all-identifiers',
                # heroku_ext is a new schema Heroku introduced to manage extensions that's
                # automatically added to every Heroku DB. Their pg:backups:capture dumps
                # don't have it, and including it breaks the restore command, so we exclude it.
                '--exclude-schema=heroku_ext',
                db_url,
            ],
            stdout=f,
        )

    # Encrypt the backup.
    subprocess.check_call(
        [
            'gpg',
            '--yes',
            '--batch',
            '--passphrase={}'.format(gpg_password),
            '-c',
            f'{TMP_BACKUP}',
        ]
    )

    # Push to S3
    version = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    backup_path = f'heroku/{heroku_app}/{heroku_app}-backup-{version}.dump.gpg'

    s3_client.upload_file(f'{TMP_BACKUP}.gpg', backup_bucket, backup_path)

    # Rmemove backup file and/or encrypted copy from local machine
    clean_files()


def heroku_cli_restore(tmp_filename, tmp_s3_key, heroku_app):
    # Upload decrypted backup to temporary S3 location.
    # don't use a bucket with versioning for this as we want to delete it immediately after restoring
    # opatlas-robo not perfect, but best fit
    s3_client.upload_file(tmp_filename, 'opatlas-robo', tmp_s3_key)
    # Create a presigned URL for our temporary S3 backup that the restore command can use
    # See https://devcenter.heroku.com/articles/heroku-postgres-import-export#import-to-heroku-postgres
    url = s3_client.generate_presigned_url(
        'get_object',
        Params={'Bucket': 'opatlas-robo', 'Key': tmp_s3_key},
        # what is a good lifetime?
        ExpiresIn=60,
    )
    # Run heroku restore using presigned url
    try:
        subprocess.check_call(
            [
                'heroku',
                'pg:backups:restore',
                url,
                'DATABASE_URL',
                '--app', heroku_app
            ]
        )
    finally:
        # Delete decrypted backup from temporary S3 location
        s3_client.delete_object(Bucket='opatlas-robo', Key=tmp_s3_key)


def _get_param_from_cli_output(param, output):
    # this is horrible
    # Heroku cli output is all Key: Value\n formatted, but with a whole bunch of whitespace and the occasional
    # line that doesn't conform, and also it's Key:\n Value\n sometimes
    parsed = {
        k: v.strip() for k, v in
        [
            x.split(':', maxsplit=1) for x in output.replace(':\n', ':').split('\n')
            if len(x.split(':')) > 1
        ]
    }
    return parsed[param]


def _major_version(version):
    # as we're dealing with one specific type of version formatting we can take a dumb approach
    return int(version.split('.')[0])


def pg_restore(backup_filename, heroku_app):
    # if Heroku Postgres has a higher major version, pg_restore will not work
    # get Heroku Postgres version and compare with local pg_restore version
    heroku_pg_info = subprocess.check_output(
        [
            'heroku',
            'pg:info',
            '-a', heroku_app,
        ],
        encoding='utf8',
    )
    db_pg_version = _get_param_from_cli_output('PG Version', heroku_pg_info)

    # output looks like "pg_restore (PostgreSQL) 16.2 (Ubuntu 16.2-1.pgdg22.04+1)"
    pg_restore_version = subprocess.check_output(
        [
            'pg_restore',
            '--version',
        ],
        encoding='utf8',
    ).split(' ')[2].strip()

    # Restore compatibility breaks if the DB has a higher major version than pg_restore
    if _major_version(db_pg_version) > _major_version(pg_restore_version):
        raise RuntimeError(
            f'Target Postgres DB version {db_pg_version} is a higher major version than '
            f'local pg_restore version {pg_restore_version}. pg_restore must have a major version '
            'that is equal to or higher than the target DB version in order for restore to work.'
        )

    # get DB connection URL - slightly horrible as need to parse Heroku CLI output
    heroku_cred_url = subprocess.check_output(
        [
            'heroku',
            'pg:credentials:url',
            '-a', heroku_app,
        ],
        encoding='utf8',
    )
    # This is brittle, if the command output changes then this could break.
    conn_url = _get_param_from_cli_output('Connection URL', heroku_cred_url)

    # reset the DB - our pg_restore command will have --clean which should drop all tables
    # anyway, but doing this first is useful as the Heroku CLI reset command will prompt for user
    # confirmation
    subprocess.check_call(
        [
            'heroku',
            'pg:reset',
            '-a', heroku_app,
        ]
    )

    # then run pg_restore command directly against the DB using our decrypted local backup file
    # see https://help.heroku.com/QG1W7LIJ/how-do-i-restore-a-partial-backup-or-single-table-to-heroku-postgres
    subprocess.check_call(
        [
            'pg_restore',
            '--verbose',
            '--clean',
            # --if-exists not in Heroku command, but required to avoid pg_restore attempting to drop tables that don't
            # exist. The restore will continue with errors, but if there are any errors it exits with exit code 1.
            '--if-exists',
            '--no-acl',
            '--no-owner',
            '-h', 'localhost',
            '-d', conn_url,
            backup_filename,
        ]
    )
    print('\nRestore complete!')


def pg_restore_localdb(backup_filename):
    # for local dev only, so okay to version
    # should keep matching the vars used in the Makefile for now
    localdb_vars = {
        'dbname': 'semsys_dev',
        'username': 'semsys_dev_user',
        'password': 'white2paper',
    }
    subprocess.check_call(
        # aggregated version of older setup_db.sh script + Makefile commands
        # will drop DB and user and then recreate them, cleaning the environment prior to backup restore
        ['./scripts/setup_db.sh'],
        env=dict(
            os.environ,
            LOCAL_DB=localdb_vars['dbname'],
            LOCAL_DB_USER=localdb_vars['username'],
            LOCAL_DB_USER_PASSWORD=localdb_vars['password'],
        ),
    )
    # run pg_restore command directly against the DB using our decrypted local backup file
    # see https://help.heroku.com/QG1W7LIJ/how-do-i-restore-a-partial-backup-or-single-table-to-heroku-postgres
    subprocess.check_call(
        [
            'pg_restore',
            '--verbose',
            # --clean is redundant as we have already reset the local DB, but include for parity with command
            # as documented by Heroku
            '--clean',
            # --if-exists not in Heroku command, but required to avoid pg_restore attempting to drop tables that don't
            # exist. The restore will continue with errors, but if there are any errors it exits with exit code 1.
            '--if-exists',
            # --no-acl stops the restore from running GRANT/REVOKE commands to set up access privileges - we don't
            # want to inherit any permissions from the Heroku DB
            '--no-acl',
            # we used to do a bunch of awkward rewriting of ownership in the Makefile restore but --no-owner makes
            # pg_restore create all tables under the ownership of the postgres user running the restore command
            '--no-owner',
            # this is the postgres user running the restore command, which is the one we set up in setup_db.sh
            '--username', localdb_vars['username'],
            # the heroku backups have two extensions, heroku_ext and pg_stat_statements, for use with heroku DBs
            # these extensions are not present in our local DB and the backup will "fail" if we include them
            # so only restore the public schema (which is where all of our actual app data lives)
            '-n', 'public',
            '-h', 'localhost',
            '-d', localdb_vars['dbname'],
            backup_filename,
        ],
        env=dict(os.environ, PGPASSWORD=localdb_vars['password']),
    )
    print('\nRestore complete!')


def restore(heroku_app, gpg_password, s3_backup_loc, restore_method):
    # Download target backup from S3 to tmp_filename.
    tmp_filename = TMP_BACKUP
    bucket, key = s3_backup_loc.replace('s3://', '').split('/', maxsplit=1)
    print(f'downloading {s3_backup_loc}...')
    s3_client.download_file(bucket, key, tmp_filename + '.gpg')

    # Decrypt downloaded backup.
    subprocess.check_call(
        [
            'gpg',
            '--decrypt',
            # required for passphrase to be taken without user confirmation prompt
            '--pinentry-mode=loopback',
            '--passphrase={}'.format(gpg_password),
            '--output', tmp_filename,
            tmp_filename + '.gpg',

        ]
    )
    try:
        print(f'restoring backup using method {restore_method}...')
        if restore_method == 'heroku':
            version = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
            tmp_s3_key = 'backups-restore/{}/{}'.format(version, key.split('/')[-1].replace('.gpg', ''))
            heroku_cli_restore(tmp_filename, tmp_s3_key, heroku_app)
        elif restore_method == 'pg_restore':
            pg_restore(tmp_filename, heroku_app)
        elif restore_method == 'localdb':
            pg_restore_localdb(tmp_filename)
    finally:
        clean_files()


def get_gpg_password():
    """Get the GPG encryption password from AWS Secrets Manager."""
    secrets_client = boto3.client('secretsmanager')
    secure_vars = json.loads(
        secrets_client.get_secret_value(SecretId='sm_platform/secure_config_vars')['SecretString']
    )
    return secure_vars['BACKUP_GPG_PASSWORD']


def get_last_env_backup(app_name):
    """Get the latest backup file from S3 for a named Heroku application."""
    contents = s3_client.list_objects(Bucket='opatlas-backups', Prefix=f'heroku/{app_name}')['Contents']
    sorted_contents = sorted(contents, key=lambda x: x['LastModified'])
    last = [obj['Key'] for obj in sorted_contents if obj['Key'].endswith('dump.gpg')][-1]
    return f's3://opatlas-backups/{last}'


def main(argv):
    parser = argparse.ArgumentParser()
    # this will be the envvar APP_HEROKU_NAME when running on the Heroku scheduler
    parser.add_argument(
        '--heroku-app', required=True,
        help='Name of heroku app to backup/restore.',
    )
    # this will be the envvar BACKUP_GPG_PASSWORD when running on the Heroku scheduler
    parser.add_argument(
        '--gpg-password',
        help='Password for encrypting/decrypting backup file.',
    )
    subparsers = parser.add_subparsers(dest='subcommand')

    run_backup = subparsers.add_parser('backup', help='Backup selected Heroku app DB to S3')
    # this will be the envvar BACKUP_S3_BUCKET when running on the Heroku scheduler
    run_backup.add_argument(
        '--backup-bucket', required=True,
        help='Name of the S3 bucket to write the backup file to.'
    )

    # restore command should only be run locally
    run_restore = subparsers.add_parser('restore', help='Restore backup from S3 to Heroku app DB.')
    backup_loc_args = run_restore.add_mutually_exclusive_group(required=True)
    backup_loc_args.add_argument(
        '--s3-backup-loc',
        help='Location of backup in S3, formatted as s3://bucket/key.',
    )
    backup_loc_args.add_argument(
        '--use-latest-backup', action='store_true',
        help='Query S3 and download the most recent backup for the app named with --heroku-app.'
    )
    run_restore.add_argument(
        '--method', choices=['heroku', 'pg_restore', 'localdb'], default='pg_restore',
        help='Whether to restore using the Heroku CLI pg:restore command or directly via pg_restore. '
        '`localdb` will use pg_restore to restore into a local DB instance instead of remote Heroku DB.',
    )

    args = parser.parse_args(argv[1:])

    # can't use default value in argparse param as it'll attempt to call the function even if arg is passed
    # and this fails in Heroku scheduler because it doesn't have permissions/config for AWS secrets manager
    if not args.gpg_password:
        args.gpg_password = get_gpg_password()

    if args.subcommand == 'backup':
        backup(args.heroku_app, args.gpg_password, args.backup_bucket)

    elif args.subcommand == 'restore':
        # better safe than sorry - can't use lazy backup arg if restoring into an actual Heroku app
        if args.use_latest_backup and args.method != 'localdb':
            parser.error('must pass explicit --s3-backup-loc argument if not restoring to a local DB instance')
        s3_backup_loc = get_last_env_backup(args.heroku_app) if args.use_latest_backup else args.s3_backup_loc
        restore(args.heroku_app, args.gpg_password, s3_backup_loc, args.method)

    else:
        parser.error('must run with either backup or restore subcommands')


if __name__ == '__main__':
    sys.exit(main(sys.argv))
