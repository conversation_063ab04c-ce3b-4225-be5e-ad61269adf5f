#!/usr/bin/env python3

# Copyright 2023 Visual Meaning Ltd

"""Set up and configure a Heroku application capable of running the sm_platform code."""

import argparse
import json
import secrets
import subprocess
import sys

import boto3


class HerokuCreator:

    def __init__(self, app_name, execute, full):
        self.execute = execute
        self.app_name = app_name
        self.full = full
        self.client = boto3.client('secretsmanager')

    def _run(self, cmd):
        print(' '.join(cmd))
        if self.execute:
            subprocess.check_call(cmd)

    def create(self):
        # create new Heroku app under visualmeaning org
        self._run(
            [
                'heroku',
                'apps:create',
                self.app_name,
                '--team', 'visualmeaning',
                '--region', 'eu',
                '--stack', 'heroku-24',
            ],
        )
        # add postgres database
        # TODO: remove '--' and '--version 17' once Postgres 17 is not a beta version for Heroku anymore.
        self._run(
            ['heroku', 'addons:create', 'heroku-postgresql:standard-0', '-a', self.app_name, '--', '--version 17'],
        )
        # configure python and node buildpacks
        # see https://devcenter.heroku.com/articles/using-multiple-buildpacks-for-an-app
        self._run(
            ['heroku', 'buildpacks:set', 'heroku/nodejs', '-a', self.app_name],
        )
        self._run(
            ['heroku', 'buildpacks:add', 'heroku/python', '-a', self.app_name],
        )
        # May need to wait for app to finish creating before git push can work
        self._run(
            ['heroku', 'ps:wait', '-a', self.app_name],
        )
        # If doing full production configuration, add deploy webhook
        # See https://devcenter.heroku.com/articles/app-webhooks
        if self.full:
            # lambda function URL that functions as webhook endpoint
            webhook_url = boto3.client('lambda').get_function_url_config(
                FunctionName='vm-heroku-deploy-webhook'
            )['FunctionUrl']
            # payloads need to have a valid signing secret - this is checked in the lambda function
            webhook_secret_raw = self.client.get_secret_value(SecretId='heroku/deploy-webhook-secret')
            webhook_secret = json.loads(webhook_secret_raw['SecretString'])['secret']
            cmd = [
                'heroku',
                'webhooks:add',
                # -i is which events to subscribe to
                '-i', 'api:release',
                # -l is whether to retry failed notifications or not - "notify" says we should not
                '-l', 'notify',
                '-u', webhook_url,
                '-s', webhook_secret,
                '-a', self.app_name,
            ]
            self._run(cmd)
        # Also wait for DB to finish provisioning... stupid, but apparently necessary now.
        self._run(
            ['heroku', 'addons:wait', '-a', self.app_name],
        )


    def configure(self):
        # configure environment variables
        envvars = {
            # used to sign Django session, programmatic use only and may be generated per app
            'DJANGO_SECRET_KEY': secrets.token_urlsafe(),
            'WEB_CONCURRENCY': 2,
            'prometheus_multiproc_dir': '/app',
            # you need a labs addon to get metadata envvars, so just set this ourselves
            # weird name is because HEROKU_ namespace is protected
            'APP_NAME_HEROKU': self.app_name,
        }
        # if doing a full-fat deploy that needs functionality beyond quick sanity-checking,
        # yank actual sensitive values out of AWS Secrets Manager
        if self.full:
            for secret in (
                'sm_platform/secure_config_vars',
                'sm_platform/microsoft-app-registration',
            ):
                sensitive = self.client.get_secret_value(SecretId=secret)
                envvars.update(json.loads(sensitive['SecretString']))
        cmd = ['heroku', 'config:set', '--app', self.app_name]
        for key, value in envvars.items():
            cmd.append('{}={}'.format(key, value))
        self._run(cmd)

    def deploy(self):
        """Deploy sm_platform code to the new app."""
        self._run(
            ['git', 'push', 'https://git.heroku.com/{}.git'.format(self.app_name), 'master']
        )

    def run(self, stages):
        for s in stages:
            func = getattr(self, s)
            func()


def main(argv):
    parser = argparse.ArgumentParser()
    parser.add_argument(
        'heroku_app', help='Name of heroku app to generate creation commands for.',
    )
    parser.add_argument(
        '--execute', action='store_true', help='Optionally execute creation commands.',
    )
    parser.add_argument(
        '--full', action='store_true',
        help='Apply production-ready configuration instead of minimum required for testing.',
    )
    stages = parser.add_argument_group('stages', 'Setup stages to run.')
    stages.add_argument(
        '-c', '--create', action='store_true',
        help='Create Heroku app.'
    )
    stages.add_argument(
        '-e', '--configure', action='store_true',
        help='Configure environment variables for Heroku app.'
    )
    stages.add_argument(
        '-d', '--deploy', action='store_true',
        help='Deploy sm_platform code to Heroku app via git push.'
    )
    args = parser.parse_args(argv[1:])

    all_stages = ['create', 'configure', 'deploy']
    run_stages = [s for s in all_stages if getattr(args, s)] or all_stages

    creator = HerokuCreator(args.heroku_app, args.execute, args.full)
    creator.run(run_stages)


if __name__ == "__main__":
    sys.exit(main(sys.argv))
