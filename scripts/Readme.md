# Devops scripts for the Heroku SMP deployment

This folder contains scripts for standing up a Heroku deployment of the SMP from scratch, and for backing up and restoring a Heroku DB to a target Heroku app.

### Contents

1. [Creating a Heroku app running the SMP](#creating-a-heroku-app-running-the-smp)
    1. [Requirements](#requirements-create)
    1. [Execution](#execution-create)
    1. [Teardown](#teardown)
1. [Backing up a Postgres database attached to a Heroku app to S3](#backing-up-a-postgres-database-attached-to-a-heroku-app-to-s3)
    1. [Requirements](#requirements-backup)
    1. [Execution](#execution-backup)
1. [Restoring an encrypted database file from S3 to a Heroku app](#restoring-an-encrypted-database-file-from-s3-to-a-heroku-app)
    1. [Requirements](#requirements-restore)
    1. [Execution](#execution-restore)
1. [Testing backup files](#testing-backup-files)
    1. [Playbook](#playbook)
1. [Rollback Heroku application database to a previous point in time](#rollback-heroku-application-database-to-a-previous-point-in-time)
    1. [Requirements](#requirements-rollback)
    1. [Execution](#execution-rollback)
1. [Rollback Production Code to a Previous Version](#rollback-production-code-to-a-previous-version)
1. [Rollback Production Database to a Previous Migration (Not Recommended)](#rollback-production-database-to-a-previous-migration-not-recommended)

# Creating a Heroku app running the SMP

## Requirements <a name="requirements-create"></a>

You will need:

- The `sm_platform` dev environment installed according to the instructions in the main repository readme. 
- Heroku credentials with admin access to the `visualmeaning` team.
- The [Heroku CLI](https://devcenter.heroku.com/articles/heroku-cli).

In addition, if you want to do a deployment with a full configuration instead of the barebones required for testing, you will need:

- The [AWS CLI](), configured with credentials that have access to the `sm_platform/secure_config_vars` secret in AWS Secrets Manager.

To restore a database backup, you will also need the gpg password of the app that made the backup, which can be obtained with:

```bash
heroku config:get -a original-app BACKUP_GPG_PASSWORD
```

## Execution <a name="execution-create"></a>

### Testing app

Run the following from the root of `sm_platform`:

```bash
pipenv run ./scripts/heroku_setup.py my-test-app
```

This will print (but **not** execute) a set of Heroku CLI commands to your terminal that, when run, will:

- Set up and configure a new application in Heroku called `my-test-app`.
- Add a Postgres database addon to that app.
- Configure the bare minimum environment variables required for the app to run.
- Deploy the `sm_platform` code to the new app via a `git push` command.

Double-check them to make sure they look vaguely sane. To actually execute the setup commands, do:

```bash
pipenv run ./scripts/heroku_setup.py my-test-app --execute
```

The script will then run through all of the commands - this takes around 5 minutes, mostly for the code deploy. Once complete, your app should be available at https://my-test-app.herokuapp.com. However, the app will not display anything past the login/privacy policy pages as the attached DB will still be empty - you will need to restore a backup into it as per the instructions below.

### Full app deploy

Run the following from the root of `sm_platform`:

```bash
pipenv run ./scripts/heroku_setup.py my-test-app --execute --full
```

The only difference between this commmand and the testing version is that the `--full` variant extracts and applies an additional set of sensitive config parameters from AWS Secrets Manager. 

Bear in mind that if you are deploying a new production-ready version of the SMP you will need to handle the DNS routing to that app manually. Detailed instructions for this are TBD; most of our DNS routing is handled via Cloudflare ('opmodel.guide') or AWS Route53 (other domains), which need to be redirected to a matching endpoint that's set up via the Settings panel in the Heroku web GUI.

## Teardown

If you have spun up an application for testing purposes you should delete it when you have finished. Once you're sure you don't need it any more, do:

```bash
heroku apps:destroy my-test-app
```

**Be very careful with this command, as it irrevocably deletes everything associated with the app including any Heroku-native DB backups!** This is one reason why we now additionally ship backups "offsite" to Amazon S3, as per the below.

# Backing up a Postgres database attached to a Heroku app to S3

Heroku has a built-in backup process (`heroku pg:backups:capture`) that is easy to use, but which has two major limitations:

- We can only store a week's worth of backups at the `basic` Postgres DB tier.
- The backups are attached to the Heroku app. If the app is deleted, the backups are too.

The `db_backup_restore_s3.py` script takes DB backups and ships them off to S3, where they are stored securely, retained for as long as we want and protected against accidental deletion.

## Requirements <a name="requirements-backup"></a>

This backup script will only work for apps that have the complete set of environment variables applied as per the **Full app deploy** instructions above.

It is best to run this script remotely on Heroku as part of a one-off dyno, as this will have all of the required config baked into it. For this, all you need is:

- Heroku credentials with access to the app you want to backup.
- The [Heroku CLI](https://devcenter.heroku.com/articles/heroku-cli).

## Execution <a name="execution-backup"></a>

### Manual backup

Run the following from the root of `sm_platform`:

```bash
heroku run sh scripts/backup.sh -a my-app-name
```

This will do the following:

- Take a backup of the Postgres database attached to `my-app-name` via the `pg_dump` utility.
- GPG encrypt the backup.
  - The encryption passphrase can be found in the app environment variables (and in AWS Secrets Manager, and Dashlane), but you should not need to care about it as it's automatically handled by both the backup and restore steps.
- Push the encrypted backup to the S3 bucket `opatlas-backups`.

(Why are we running a shell script instead of the Python script directly? Because shell expansion of the required environment variables only works if it's packaged up this way, is the short answer.)

### Automated backup

It is possible to schedule automated backup pushes to S3 using the [Heroku scheduler](https://devcenter.heroku.com/articles/scheduler) addon.

Add the scheduler to your app:

```bash
heroku addons:create scheduler:standard -a my-app-name
```

It is not possible to configure the scheduler via the CLI. The create command should spit out an URL to configure it via the web interface, and you can also access it by going to the app in the web interface and then selecting the `Resources` menu. The scheduler should be visible in the list of addons.

Scheduler configuration is very limited -- all it will let you do is set a command and a coarse-grain execution frequency. Set the desired execution time and frequency, and set the execution command as follows:

```bash
sh scripts/backup.sh
```

This is identical to the `heroku run` command for the manual backup, as the scheduler also invokes a one-off dyno to take the backup.

The scheduler sends its log output to the main application log stream. You should be able to verify that the backups are running both by checking the logs in Heroku (or AWS Cloudwatch, if you're shipping them there) and by checking the `opatlas-backups` S3 bucket for the corresponding encrypted backup files.

# Restoring an encrypted database file from S3 to a Heroku app

## Requirements <a name="requirements-restore"></a>

You will need:

- Heroku credentials with access to the target Heroku app.
- The [Heroku CLI](https://devcenter.heroku.com/articles/heroku-cli).
- Local AWS CLI credentials with read access to the `opatlas-backups` S3 bucket and full access to the `opatlas-robo` bucket.
- The GPG password for the backup files. This can be found in AWS Secrets Manager as part of the `sm_platform/secure_config_vars` secret.

## Execution <a name="execution-restore"></a>

Run the following from the root of `sm_platform`:

```bash
pipenv run ./scripts/db_backup_restore_s3.py --heroku-app my-app-name --gpg-password my-gpg-password restore --s3-backup-loc s3://opatlas-backups/path/to/my/encrypted-backup.dump.gpg
```

This will:

- Download the encrypted backup from S3.
- Decrypt it using the GPG password.
- Run a `pg_restore` command to restore the decrypted backup directly into the DB for the Heroku app named with `--heroku-app`, using the `DATABASE_URL` connection string to connect.

The Heroku CLI will ask you to confirm the restore action before running it. **Make sure you are pointing at the right Heroku app!** The existing database is deleted prior to restoring a backup.

# Testing backup files

Automated backups should be tested regularly to ensure we retain the ability to restore them, and that nothing has changed underneath us that would make this impossible. Testing is a combination of the above commands for creating a Heroku app and restoring a backup file.

## Playbook

Pick a backup file to test from the `opatlas-backups` bucket in S3.

Stand up a test application in Heroku:

```bash
pipenv run ./scripts/heroku_setup.py $APP_NAME --execute
```

Restore your backup file into the test application:

```bash
pipenv run ./scripts/db_backup_restore_s3.py --heroku-app $APP_NAME --gpg-password $GPG_PASSWORD  restore --s3-backup-loc $S3_BACKUP_PATH
```

Visit the app in the browser by running:

```bash
heroku apps:open -a $APP_NAME /accounts/login/
```

If [that doesn't work](https://stackoverflow.com/questions/********) it at least gives the correct URL for the app.

Then test the following:

- That the site loads and that you can log in with your credentials for the environment that the restored backup came from.
- That you can view maps. (Note: may have odd behaviour thanks to the Django-level site handling. Needs debugging.)
- Check the Django admin portal to see that the various model objects look the way that they should.
- In particular check that the backup has up-to-date information corresponding to the time that the backup was taken.

Once you are satisfied that the backup has restored correctly, you can delete the test application.

```bash
heroku apps:destroy -a $APP_NAME
```

# Rollback Heroku application database to a previous point in time

In case we would like to restore database to a state in the past other than a nightly build, e.g. 2 hours back, you can [rollback to a specific point](https://devcenter.heroku.com/articles/heroku-postgres-rollback). The mechanism is forking the current database to a new one and applying only changes from the [WAL](https://devcenter.heroku.com/articles/postgres-write-ahead-log-usage) until the selected point.

The point in time we can go back to depends on the database plan tier and time of current database creation.

## Requirements <a name="requirements-rollback"></a>

You will need:

- Heroku credentials with access to the target Heroku app.
- The [Heroku CLI](https://devcenter.heroku.com/articles/heroku-cli).

## pg:info

It is advised you check the state of databases after each action:
```bash
heroku pg:info --app $APP_NAME
```

## Execution <a name="execution-rollback"></a>

1. Provisioning a new database as a rollback with set datetime:

```bash
heroku addons:create heroku-postgresql:standard-0 --app $APP_NAME -- --rollback DATABASE_URL --to '2024-10-23 10:50+00'
```

- `DATABASE_URL` is the var name of the currently used database. When you check [pg:info](#pginfo), if it was previously actioned on, it may have an alias, e.g. `DATABASE_URL, HEROKU_POSTGRESQL_BROWN_URL`. All aliases has a color in the name. From this point on only use aliases.
- Use the provided format for datetime. Also use single `'` for unix and double `"` for windows.

2. The previous action works in the background and may take some time. You can keep watch:

```bash
heroku pg:wait --app $APP_NAME
```

- Once finished you may check the var name of the newly created database either from output or [pg:info](#pginfo). The database uses a new color, e.g. `HEROKU_POSTGRESQL_CYAN_URL`

3. Attach the new database to the application by promoting it:

```bash
heroku pg:promote HEROKU_POSTGRESQL_CYAN_URL --app $APP_NAME
```

### Cleanup

The detached database will keep working. Once done with investigation you can deprovision/destroy it:
```bash
heroku addons:destroy HEROKU_POSTGRESQL_BROWN_URL --app $APP_NAME
```
- Use the alias of the detached database `HEROKU_POSTGRESQL_BROWN_URL` and not `DATABASE_URL`, which is linked with the new color.

# Rollback Production Code to a Previous Version

1. Checkout the Rollback Commit:

Create a new local branch from the commit you want to roll back to (usually the last stable production deployment).

```bash
git checkout -b <local_new_branch> <hash>
```

2. Push to Heroku Production Master:

Force push the new branch to the Heroku production remote as the master branch.
```bash
git push production <local_new_branch>:master --force
```

# Rollback Production Database to a Previous Migration (Not Recommended)

> Warning: Rolling back a database schema is risky and not recommended. The preferred approach is [restoring an encrypted database backup from S3 to Heroku](#restoring-an-encrypted-database-file-from-s3-to-a-heroku-app). Proceed with caution.

1. Run the Migration Rollback Command:

Use the migration file from the semsys/migrations folder to revert the database schema to a previous state. Replace $MIGRATION_NAME with the specific migration filename.
```bash
DATABASE_URL=$PROD_DATABASE_URL python3 manage.py migrate semsys $MIGRATION_NAME
```

# Heroku Annual Dyno–Postgres Version Mismatch

Every year, Heroku performs scheduled upgrades to both  **dyno stacks** and **PostgreSQL database**. However, these two upgrade sometimes fall out of sync. This leads to a potentially annual issue where the Postgres version installed on dynos used for backups does not match the version of the Postgres database instance. This idiosyncracy could cause problems with our database backup and restore, which uses `pg_dump` and `pg_restore` and rely on version compatibility between client and server.

---

As of April 2025, we’ve encountered this version mismatch:

- **Heroku Postgres (Staging & Production)** is running **PostgreSQL 15**

- **Heroku Dynos** used for generating backups are running **PostgreSQL 17**

Attempting to restore these backups into Postgres 15 results in failure or warnings.

### Current Workaround

- Upgraded local Postgres client to version 17

- Restores from dyno-generated backups now succeed, but still produce warning messages during the process

## Long-Term Plan

To eliminate this incompatibility, once PostgreSQL 17 is officially supported on Heroku, staging and production databases should be upgraded. This will align both the dyno’s `pg_dump` version and the target DB version.

---

## Heroku Pattern

This is part of a recurring annual pattern with Heroku:

1. **Dyno Stack Deprecation** (Spring – e.g., `heroku-20` → `heroku-22`)

   - Heroku transitions apps to newer stack versions.

   - These stacks come pre-installed with newer Postgres clients (v17 in 2025).

   - [Changelog Example](https://devcenter.heroku.com/changelog-items/3161): Deprecation of `heroku-20` in April 2025.

2. **PostgreSQL Database Upgrade Push** (Late April)

   - New PostgreSQL versions become available for managed databases. [Examples for Postgres 16](https://devcenter.heroku.com/changelog-items/2777)



