# Copyright 2020 Visual Meaning Ltd

"""Optional app config to allow cross origin api requests."""

from corsheaders.signals import check_request_enabled
from django.apps import AppConfig


def cors_allow_api_to_all(sender, request, **kwargs):
    return request.path.startswith("/api/")


class AllowApiCors(AppConfig):
    """Django settings class to enable cross origin access to the api."""

    name = "semsys"

    def ready(self):
        check_request_enabled.connect(cors_allow_api_to_all)
