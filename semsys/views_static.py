# Copyright 2022 Visual Meaning Ltd

"""
View functions for returning file-like content that would otherwise be stored in /static
"""

import base64
import datetime

from django.conf import settings
from django.contrib.auth.decorators import login_required
from django.contrib.sites.shortcuts import get_current_site
from django.http import HttpResponse, HttpResponseRedirect, Http404, JsonResponse

from .helpers import get_site_with_override
from .models import Site
from .shortercuts import get_object_or_None


# TODO: Where should debug/testing APIs live?
@login_required
def set_session_expiry(req):
    """Allow staff users to set their session expiry time for testing purposes."""
    site = get_current_site(req)
    if not req.user.is_staff or site.name not in settings.DEV_SITES:
        raise Http404()
    try:
        new_expiry = int(req.GET.get("expiry", 30))
    except ValueError:
        return JsonResponse(
            dict(error="expiry parameter value must be an integer"),
            status=400,
        )
    now = datetime.datetime.now()
    strdate_fmt = "%Y-%m-%d %H:%M:%S"
    # Store original expiry time as of signin - they should not be able to set
    # an expiry time longer than this to prevent security issues.
    # Need to convert to string as needs to be JSON-serializable to store in cookie,
    # which results in overly verbose code for strftime/strptime calls.
    if not req.session.get("original_session_expiry"):
        req.session["original_session_expiry"] = (
            now + datetime.timedelta(seconds=req.session.get_expiry_age())
        ).strftime(strdate_fmt)
    # Now we convert back from string to datetime so that we can use it for diff
    max_age = datetime.datetime.strptime(
        req.session["original_session_expiry"], strdate_fmt
    )
    new_expiry = min(
        new_expiry,
        (max_age - now).total_seconds(),
    )
    # Get new expiry time as a string datetime for human-readable return value
    new_expiry_time = (now + datetime.timedelta(seconds=new_expiry)).strftime(
        strdate_fmt
    )
    # Set new session expiry time and return debug response for the user
    req.session.set_expiry(new_expiry)
    return JsonResponse(
        {
            "session_expiry": {
                "expires_in_seconds": int(new_expiry),
                "expires_time": new_expiry_time,
                "max_allowed_expires_time": req.session["original_session_expiry"],
            }
        }
    )


@login_required
def site_override(req):
    """Allow staff users to override current site for testing purposes."""
    # to avoid potential data leaks, only want to allow this for staging and local dev
    # use actual get_current_site function to get actual site without override applied
    real_site = get_current_site(req)
    if not req.user.is_staff or real_site.name not in settings.DEV_SITES:
        raise Http404()
    # hang site setting off of the session cookie - get_site_with_override will
    # check this first, then fall back to normal site name from get_current_site
    site_param = req.GET.get("site")
    req.session["site"] = site_param
    return HttpResponseRedirect(req.GET.get("next", "/"))


def site_override_reset(req):
    """Reset site override."""
    # on a different view because we want to allow this for unauthenticated users
    if "site" in req.session:
        del req.session["site"]
    return HttpResponseRedirect(req.GET.get("next", "/"))


def custom_css(req):
    curr_site = get_site_with_override(req)
    site = get_object_or_None(Site, name=curr_site.name)
    # apply default branding if not set, or if we're on staging/localhost
    # TODO: distinct branding for settings.DEV_SITES
    if not site or site.name in settings.DEV_SITES:
        return HttpResponse("", content_type="text/css")
    return HttpResponse(site.custom_css, content_type="text/css")


def favicon(req):
    curr_site = get_site_with_override(req)
    site = get_object_or_None(Site, name=curr_site.name)
    if not site or not site.favicon:
        response = HttpResponse(status=303)
        response["location"] = "/favicon.ico"
        return response
    img = base64.b64decode(site.favicon)
    return HttpResponse(img, content_type="image/png")
