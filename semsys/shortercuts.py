# Copyright 2020 Visual Meaning Ltd

"""Utils for making common Django operations simpler.

Like django.shortcuts but more so.
"""

from django import (
    shortcuts,
)
import json


def get_latest_or_None(cls, *fields):
    """Like QuerySet.latest() but returns None if object does not exist."""
    queryset = shortcuts._get_queryset(cls)
    try:
        return queryset.latest(*fields)
    except queryset.model.DoesNotExist:
        return None


def get_object_or_None(cls, *args, **kwargs):
    """Like QuerySet.get() but returns None if object does not exist.

    Raises MultipleObjectsReturned if multiple objects are found.
    """
    queryset = shortcuts._get_queryset(cls)
    try:
        return queryset.get(*args, **kwargs)
    except queryset.model.DoesNotExist:
        return None


def get_value_from_json(json_string, key):
    """Get the value from a strigified JSON.

    Any error will result to None.
    """
    if not json_string:
        return None

    try:
        json_data = json.loads(json_string)
        value = json_data.get(key)
        return value
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON: {e}")
        return None
