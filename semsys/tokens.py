# Copyright 2020-2021 Visual Meaning Ltd

from django.conf import settings
from django.contrib.auth.tokens import PasswordResetTokenGenerator
from django.utils.crypto import constant_time_compare, salted_hmac
from django.utils.http import (
    base36_to_int,
    int_to_base36,
    urlsafe_base64_decode,
    urlsafe_base64_encode,
)


class GuestEmailTokenGenerator(PasswordResetTokenGenerator):
    """Django password reset token logic hacked to work with just user email, not full user."""

    # it's possible to set a distinct secret here but the default setting of SECRET_KEY from
    # settings.py should be fine
    key_salt = "sm_platform.semsys.GuestEmailTokenGenerator"

    def make_token(self, email):
        return self._make_token_with_timestamp(email, self._num_seconds(self._now()))

    @staticmethod
    def get_email_from_token(token):
        try:
            return urlsafe_base64_decode(token.split("-")[0]).decode("utf8")
        except ValueError:
            return None

    def check_token(self, token):
        if not token:
            return False
        # Parse the token
        email = self.get_email_from_token(token)
        if not email:
            return False

        try:
            _, ts_b36, _ = token.split("-")
        except ValueError:
            return False

        try:
            ts = base36_to_int(ts_b36)
        except ValueError:
            return False

        # Check that the timestamp/uid has not been tampered with
        if not constant_time_compare(self._make_token_with_timestamp(email, ts), token):
            return False

        # Check the timestamp is within limit.
        if (self._num_seconds(self._now()) - ts) > settings.EMAIL_TOKEN_TIMEOUT:
            return False

        return True

    def _make_token_with_timestamp(self, email, timestamp):
        # timestamp is number of seconds since 2001-1-1. Converted to base 36,
        # this gives us a 6 digit string until about 2069.
        ts_b36 = int_to_base36(timestamp)
        # the token hash uses the email as part of its input, but we cannot derive the email from the hash,
        # so we need a simple encoded version as a prefix for comparison and for assigning as a value
        email_b64 = urlsafe_base64_encode(email.encode("utf8"))
        hash_string = salted_hmac(
            self.key_salt,
            self._make_hash_value(email, timestamp),
            secret=self.secret,
            algorithm=self.algorithm,
        ).hexdigest()[
            ::2
        ]  # hexdigest always returns 64 chars, so this limits to 32 chars.
        return "%s-%s-%s" % (email_b64, ts_b36, hash_string)

    def _make_hash_value(self, email, timestamp):
        return f"{email}{timestamp}"


default_email_token_generator = GuestEmailTokenGenerator()
