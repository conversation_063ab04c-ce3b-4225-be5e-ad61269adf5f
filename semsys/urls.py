"""semsys URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/2.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include
from django.views.generic import RedirectView

from . import (
    edhandlers,
    vmhandlers,
    genhandlers,
    settings,
    views_auth,
    views_errors,
    views_static,
)
from .api_v1 import get_maps, clear_terms_cache
from .reporting import get_model_upload_logs
from .views_annotations import (
    annotations_summary,
    delete_annotations_for_map,
    map_annotations_report,
    map_votes_report,
    user_map_annotations,
)
from .views_azure import get_instance_health, explode
from .views_blobs import blob_get
from .views_purpose import purpose
from .views_user import get_user_data


##  ======================================================================
##  == URL PATTERNS

urlpatterns = [
    # TODO: Welcome page is vestigial, trialling a redirect to the maps index page
    # while we're working on signin flow. Martin's going to love me for this.
    # Either revert this change, or remove welcome page code depending on how it goes.
    # path("", vmhandlers.welcomePage, name="welcome"),
    path("", RedirectView.as_view(pattern_name="maps-index"), name="welcome"),
    path("privacy-policy/", vmhandlers.privacy_policy, name="privacy"),
    path("admin/", admin.site.urls),
]

if settings.AUTH_FRAMEWORK == "vm":
    urlpatterns += [
        ##  ------------------------------------------------------------
        ##  django-prometheus metrics endpoint
        path("", include("django_prometheus.urls")),
        ##  ------------------------------------------------------------
        ##  Django admin/auth
        path(
            "accounts/login/",
            views_auth.CustomLoginView.as_view(next_page="/maps"),
            name="login",
        ),
        path(
            "accounts/logout/",
            views_auth.CustomLogoutView.as_view(next_page="/maps"),
            name="logout",
        ),
        path(
            "accounts/password_reset/",
            views_auth.CustomPasswordResetView.as_view(),
            name="password_reset",
        ),
        path("accounts/", include("django.contrib.auth.urls")),
        ## Root signin page for other signin methods
        path("signin/", views_auth.signin, name="signin"),
        ## Signout page when session is expired
        path("signout/", views_auth.signout, name="signout"),
        ## Microsoft SSO paths
        path(
            "microsoft_authentication/callback/",
            views_auth.sso_callback,
            name="ms-auth-callback",
        ),
        path(
            "microsoft_authentication/login/",
            views_auth.sso_login,
            name="ms-auth-login",
        ),
        ## Email verification paths
        path("guest-email/", views_auth.anonymous_email, name="guest-email"),
        path(
            "guest-email/submitted/",
            views_auth.guest_access_email_submitted,
            name="guest-email-submitted",
        ),
        path(
            "guest-email/validate/",
            views_auth.validate_token_for_email,
            name="guest-email-validate",
        ),
        path(
            "guest-email-logout/",
            views_auth.guest_email_logout_user,
            name="guest-email-logout",
        ),
        path("api/user", get_user_data),
    ]

elif settings.AUTH_FRAMEWORK == "azure":
    urlpatterns += [
        path("api/health", get_instance_health),
        path("api/explode", explode),
        # Also expect the following .auth urls to be handled by EasyAuth prior
        # to reaching the Django routing:
        #   .auth/me
        #   .auth/logout
    ]

if settings.BLOBPROXY_CONN_STR:
    urlpatterns += [
        # Blob proxying
        path("blobs/<path:blob_key>", blob_get),
    ]

urlpatterns += [
    ##  ------------------------------------------------------------
    ##  Cross-app API
    path("api/content-item", genhandlers.contentItem),
    ##  ------------------------------------------------------------
    ##  Model editor API
    path("api/projects", edhandlers.projectsAndMaps),
    path("api/map-model", edhandlers.mapModel),
    path("api/map-votes", edhandlers.mapVotes),
    ##  ------------------------------------------------------------
    ##  Viewmap api
    path("api/map-model-viewmap", vmhandlers.mapModel_get),
    path("api/v1/map-model-terms", vmhandlers.mapModel_get_terms),
    path("api/annotations/report", map_annotations_report),
    path("api/annotations/votes", map_votes_report),
    path("api/annotations/summary", annotations_summary),
    path("api/annotations/delete-for-map", delete_annotations_for_map),
    path("api/annotations", user_map_annotations),
    path("api/purpose", purpose),
    ## ------------------------------------------------------------
    ## Debug APIs - will 404 if not on staging/localhost
    path("api/session/set_expiry", views_static.set_session_expiry),
    path("api/site/set", views_static.site_override),
    path("api/site/reset", views_static.site_override_reset),
    ##  ------------------------------------------------------------
    ##  V1 API
    path("api/v1/maps", get_maps),
    path("api/v1/model-upload-logs", get_model_upload_logs),
    path("api/v1/clear-terms-cache", clear_terms_cache),
    ##  --------------------------------------------------------------
    ##  Graphical maps
    path("maps/", vmhandlers.mapIndexPage, name="maps-index"),
    ## No-slash paths are automatically redirected to slash-paths
    path("maps/<mapSlug>/", vmhandlers.showMapSlash),
    path("maps/<mapSlug>/<path:remaining>", vmhandlers.showMapSlash),
    ##  Map terms
    path("mapterms/<mapSlug>/", vmhandlers.showMapTerms),
    path("mapterms/<mapSlug>/<path:remaining>", vmhandlers.showMapTerms),
    # Error views for testing
    path("400", views_errors.bad_request),
    path("403", views_errors.forbidden),
    path("404", views_errors.not_found),
    path("500", views_errors.internal_server_error),
    # custom css file for this domain
    path(
        "file-content/css/custom-theme.css",
        views_static.custom_css,
        name="custom-theme",
    ),
    path("file-content/img/favicon.png", views_static.favicon, name="favicon"),
    # Handle legacy top level paths for maps if they correspond still
    path("<slug:maybe_map>/", vmhandlers.fallback_to_map),
]
