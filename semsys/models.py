from django.db import models
from django.contrib.auth.models import Group
from django.core.exceptions import ValidationError
from django.utils.timezone import now as django_tz_now

from .users import User
from .terms_response import spliced_terms, delete_cached_terms


class Site(models.Model):
    """Container for domain-specific options and settings."""

    name = models.TextField(primary_key=True)
    custom_css = models.TextField(
        blank=True,
        help_text="Custom branding that will be applied to site base pages such as maps index, login etc.",
    )
    favicon = models.TextField(
        blank=True,
        help_text="A favicon PNG converted to a base64 text string.",
    )
    guest_signin = models.BooleanField(
        help_text="Allows Guest Sign In via email admission.",
        default=True,
    )
    sso_signin = models.BooleanField(
        help_text="Allows access via SSO sign in.",
        default=False,
    )
    microsoft_tenant_id = models.CharField(
        help_text="Microsoft Entra Tenant ID of Organisation. Used as an endpoint for Microsoft SSO sign in.",
        blank=True,
        max_length=36,
    )

    def __str__(self):
        return self.name


##  ----------------------------------------------------------------------
##  Defining projects, which own maps


class Project(models.Model):
    """One client-facing project"""

    iri = models.TextField(primary_key=True)
    name = models.TextField()
    description = models.TextField()
    created_on = models.DateField()
    related_site = models.ForeignKey(
        Site, blank=True, null=True, on_delete=models.SET_NULL
    )
    # for now, whitelist on email in cookie
    whitelist = models.TextField(blank=True)

    def __str__(self):
        return 'Project("{}" :: "{}" :: "{}" :: "{}")'.format(
            self.iri,
            self.name,
            getattr(self.related_site, "name", "none"),
            self.created_on,
        )


# create a new group whenever a new project is created - will be needed for accessing maps in that project
# see similar MapTermsBag function for commentary on what this is doing
def _create_group_for_new_project(instance, created, raw, **kwargs):
    if raw or not created:
        return

    Group.objects.get_or_create(name=instance.name)


models.signals.post_save.connect(
    _create_group_for_new_project,
    sender=Project,
    dispatch_uid="default_group_for_new_project",
)


##  ----------------------------------------------------------------------
##  Defining maps, each of which owns one RDF model


class Map(models.Model):
    """One graphical map"""

    iri = models.TextField(primary_key=True)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    name = models.TextField()
    description = models.TextField()
    created_on = models.DateField()
    voting_open = models.BooleanField()
    public = models.BooleanField(
        help_text="Set a map as public so that it can be viewed by anyone.",
        default=False,
    )
    tiles_src = models.TextField()  # Leaflet-friendly tilestore string
    # A collection of low-fidelity image sources used during platform load.
    # Depending on the map/view being loaded (from the URL /maps/example/{map|view}),
    # the respective image will appear in the background as a placeholder.
    # {
    #   "_map_1": "s3://opatlas-live/example/placeholder_1.png",
    #   "_map_2": "s3://opatlas-live/example/placeholder_2.png"
    # }
    loading_src = models.TextField(blank=True, null=True)
    ### 'path' means suffix on /maps/; "" means dont' expose. Must end with '/'.
    guest_map_path = models.CharField(blank=True, max_length=96, null=True, unique=True)
    voting_map_path = models.CharField(
        blank=True, max_length=96, null=True, unique=True
    )
    # Voting app splash screen raw html. Make null to mean 'don't draw'
    welcome_splash = models.TextField(blank=True)
    voting_splash = models.TextField(blank=True)

    # Concurrent editing helpers:
    # These fields are updated on each rdf model save, with a new uuid generated.
    # Saves must be supplied with a uuid of the last loaded state, and they are
    # rejected if the uuid is wrong (i.e. subsequent update to the db that's
    # been missed).
    # These are all null before the first write has taken place.
    terms_updated_on = models.DateTimeField(auto_now=True)
    terms_updated_by = models.ForeignKey(User, null=True, on_delete=models.SET_NULL)
    terms_updated_uuid = models.UUIDField(null=True)

    # Map admins want to be able to customise styling of map elements based on
    # map zoom level. We achieve this by defining a set of named zoom 'classes'
    # in terms of the min and max (inclusive) zoom levels they denote, and then by applying
    # the first that matches to the leaflet map elem as a css class. E.g.:
    # [{ name: "midzoom", min: 5, max: 5},
    #  { name: "zoomedout", min: 0, max: 2}]
    map_zoom_classes_json = models.TextField(blank=True)

    # Admins want to be able to add extra css rules to maps at runtime, without
    # changing the source code via git. We allow them to add extra raw css strings
    # that get added to <head> as a <style> elem on map model load time.
    # This is particularly useful for customising leaflet maps based on zoom, e.g.
    # with the following rules (based on the above 'zoomedout' zoom rule)
    # .LeafletMap.zoomedout .PainpointMarker .text {
    #     color: #aaa;
    #     font-weight: bold;
    # }
    map_extra_css = models.TextField(blank=True)

    class Meta:
        ## TODO provide a better error message when this constraint get tripped
        ## (probably just normally customsie via js app, and do checking there too)
        constraints = [
            models.CheckConstraint(
                check=(
                    models.Q(guest_map_path=None)
                    | models.Q(guest_map_path__endswith="/")
                ),
                name="guest_map_path_empty_or_ends_with_slash",
            ),
            models.CheckConstraint(
                check=(
                    models.Q(voting_map_path=None)
                    | models.Q(voting_map_path__endswith="/")
                ),
                name="voting_map_path_empty_or_ends_with_slash",
            ),
        ]

    # temporary constraint validation for Map creation via admin form
    # TODO: this can be removed when we refactor the above constraints out
    def clean(self):
        if self.guest_map_path and not self.guest_map_path.endswith("/"):
            raise ValidationError("'Guest map path' must end with /")
        if self.voting_map_path and not self.voting_map_path.endswith("/"):
            raise ValidationError("'Voting map path' must end with /")

    def __str__(self):
        return 'Map("{}" :: "{}" :: "{}" :: "{}")'.format(
            self.iri, self.name, self.project, self.created_on
        )

    @property
    def all_terms_as_string(self):
        """Canonical set of combined map terms, returned as a string."""
        terms_querysets = [
            MapTermsBag.objects.filter(map=self),
            LensTermsBag.objects.filter(map=self),
        ]
        return spliced_terms(terms_querysets)


##  ----------------------------------------------------------------------
##  Defining RDF models by their triples, each model owned by one map


class MapTermsBag(models.Model):
    """Many terms relating to one map"""

    map = models.ForeignKey(Map, on_delete=models.CASCADE)
    created_on = models.DateTimeField(auto_now_add=True)
    terms = models.JSONField()

    class Meta:
        get_latest_by = "created_on"

    def __str__(self):
        return 'MapTermsBag("{}")'.format(self.map)


class LensTermsBag(models.Model):
    """High-level config for maps, lenses, views etc."""

    map = models.ForeignKey(Map, on_delete=models.CASCADE)
    created_on = models.DateTimeField(auto_now_add=True)
    terms = models.JSONField(blank=True)

    class Meta:
        get_latest_by = "created_on"

    def __str__(self):
        return 'LensTermsBag("{}")'.format(self.map)

    def clean(self):
        if not self.terms:
            self.terms = []


def _default_mapterms_for_new_map(instance, created, raw, **kwargs):
    # raw is True if map saved exactly as presented, seems to be relevant
    # for test fixtures
    if raw or not created:
        return

    MapTermsBag.objects.get_or_create(map=instance, terms=[])
    LensTermsBag.objects.get_or_create(map=instance, terms=[])


def _blow_terms_cache_and_etag(instance, raw, **kwargs):
    if raw:
        return
    # Update the map terms_updated_on to blow the etag
    map = instance.map
    map.terms_updated_on = django_tz_now()
    map.save()
    # We can't rebuild cache on terms creation/edit because of a circular dependency on
    # map creation - this triggers terms bag creation, which will trigger this function,
    # which will attempt to read all terms bags to rebuild cache, but they don't all
    # exist yet because we're in the middle of creating them, so we get an error.
    # So wimp out and simply delete the cached terms instead. They'll be rebuilt on the
    # next GET request.
    delete_cached_terms(map.iri)


# register a signal reciever function that fires post Map-save
models.signals.post_save.connect(
    _default_mapterms_for_new_map,
    sender=Map,
    # prevents many duplicate recievers being registered - only one per uid
    dispatch_uid="default_mapterms_for_new_map",
)

models.signals.post_save.connect(
    _blow_terms_cache_and_etag,
    sender=MapTermsBag,
    dispatch_uid="cache_compressed_terms_mterms",
)

models.signals.post_save.connect(
    _blow_terms_cache_and_etag,
    sender=LensTermsBag,
    dispatch_uid="cache_compressed_terms_lterms",
)

##  ----------------------------------------------------------------------
##  Painpoint votes by graphical map users


class MapPainpointVote(models.Model):
    """One voter's vote on one painpoint in one map"""

    map = models.ForeignKey(Map, on_delete=models.CASCADE)
    painpoint_iri = models.TextField()  # refs map's rdf
    voter_iri = models.TextField()  # refs map's rdf
    score = models.IntegerField()

    class Meta:
        unique_together = ("map", "painpoint_iri", "voter_iri")

    def __str__(self):
        return 'MapPainpointVote("{}", "{}", "{}", {})'.format(
            self.map.iri, self.painpoint_iri, self.voter_iri, self.score
        )


##  ----------------------------------------------------------------------
##  Content Item storage - binary assets for graphical maps to display


class ContentItem(models.Model):
    """An opaque binary blob, named with a uuid"""

    uuid = models.UUIDField(primary_key=True)
    data = models.BinaryField()
    data_sha1 = models.BinaryField(unique=True)  # Binary sha1 digest of 'data'

    def __str__(self):
        return 'ContentItem("{}")'.format(self.uuid)


class UserMapAnnotationBag(models.Model):
    """Generic annotations on a map stored per-user."""

    map = models.ForeignKey(Map, on_delete=models.CASCADE)
    created_on = models.DateTimeField(auto_now_add=True)
    terms = models.JSONField()
    user_stub = models.TextField()

    class Meta:
        get_latest_by = "created_on"

    def __str__(self):
        return 'UserMapAnnotations("{}, {}")'.format(self.map, self.user_stub)


class PurposeBag(models.Model):
    """Generic storage of external purpose model."""

    created_on = models.DateTimeField(auto_now_add=True)
    name = models.TextField()
    terms = models.JSONField()

    class Meta:
        get_latest_by = "created_on"

    def __str__(self):
        return 'PurposeBag("{}")'.format(self.name)
