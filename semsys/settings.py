"""
Django settings for semsys project.
"""

import datetime
import os
import sys

import dj_database_url

from . import custom_logging

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Specifically where is the app deployed too, based on global env settings
_is_heroku = os.environ.get("APP_NAME_HEROKU")
_is_azure = os.environ.get("WEBSITE_RESOURCE_GROUP")

# Toggle Plausible metrics tracking script on in production
# the script has a localhost check and should not run locally, but just to be sure...
PLAUSIBLE_METRICS = _is_heroku or bool(os.environ.get("PLAUSIBLE_METRICS"))

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = bool(os.environ.get("DJANGO_DEBUG"))

# Is the deployment on some remote environment (including staging, short-lived
# envionments too) rather than being run locally on a developer's machine?
# Can't just use DEBUG as we may want to test non-DEBUG configs locally.
LIVE = _is_heroku or _is_azure

# When running unittests only
TESTING = "test" in sys.argv

# Live deployments must set a non-well known value so sessions are safe
SECRET_KEY = os.environ["DJANGO_SECRET_KEY"] if LIVE else "FIXED"

# As previously configured by django-heroku
ALLOWED_HOSTS = ["*"]

# Force https when deployed on heroku
SECURE_SSL_REDIRECT = _is_heroku
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

AUTH_FRAMEWORK = os.environ.get("AUTH_FRAMEWORK", "vm")

if AUTH_FRAMEWORK == "azure":
    AUTH_MODULE = "semsys.permissions_azure"
    # placeholder for azure/highways-specific User model
    AUTH_USER_MODEL = "semsys.User"
else:
    AUTH_MODULE = "semsys.permissions_vm"
    AUTH_USER_MODEL = "semsys.User"

# 15 minute timeout for access tokens emailed to "anonymous" guest users
EMAIL_TOKEN_TIMEOUT = 900

# Allow 15MiB data uploads (which will live in process memory)
DATA_UPLOAD_MAX_MEMORY_SIZE = 15 * 1024 * 1024

# Don't need big auto id fields for models introduced with Django 3.2
DEFAULT_AUTO_FIELD = "django.db.models.AutoField"

# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "corsheaders",
    "semsys.apps.AllowApiCors",
    "whitenoise.runserver_nostatic",
    "django_prometheus",
    "django_simple_task",
    "axes",
]

AUTHENTICATION_BACKENDS = [
    # axes says it should be first
    "axes.backends.AxesStandaloneBackend",
    # then default Django auth backend
    "django.contrib.auth.backends.ModelBackend",
]

MIDDLEWARE = [
    "django_prometheus.middleware.PrometheusBeforeMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "django_prometheus.middleware.PrometheusAfterMiddleware",
    "semsys.monitoring.VmMonitoringMiddleware",
    "axes.middleware.AxesMiddleware",
]

CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.db.DatabaseCache",
        "LOCATION": "compressed_terms",
        # cache keys never expire
        "TIMEOUT": None,
    }
}

APPINSIGHTS_INSTRUMENTATION_KEY = os.environ.get("APPINSIGHTS_INSTRUMENTATIONKEY")

if APPINSIGHTS_INSTRUMENTATION_KEY:
    MIDDLEWARE.append("opencensus.ext.django.middleware.OpencensusMiddleware")

    OPENCENSUS = {
        "TRACE": {
            "SAMPLER": "opencensus.trace.samplers.ProbabilitySampler(rate=1)",
            "EXPORTER": """opencensus.ext.azure.trace_exporter.AzureExporter(
                connection_string="InstrumentationKey={ikey}"
            )""".format(
                ikey=APPINSIGHTS_INSTRUMENTATION_KEY
            ),
        }
    }

ROOT_URLCONF = "semsys.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [
            os.path.join(BASE_DIR, "templates"),
            os.path.join(BASE_DIR, "frontend/html"),
        ],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
                "semsys.context_processors.metrics_site",
                "semsys.context_processors.auth_framework",
            ],
        },
    },
]

ASGI_APPLICATION = "semsys.asgi.application"


# Database
# https://docs.djangoproject.com/en/2.2/ref/settings/#databases

# Monkey patch scheme paths to add prometheus db monitoring
dj_database_url.SCHEMES = {
    k: v.replace("django.db.backends.", "django_prometheus.db.backends.")
    for k, v in dj_database_url.SCHEMES.items()
}

# Connection string env var for Azure Web App version of the SMP
maybe_connstr = os.environ.get("POSTGRESQLCONNSTR_DATABASE_URL")

if TESTING:
    db_config = {"ENGINE": "django.db.backends.sqlite3", "NAME": "vm-local"}
elif maybe_connstr:
    # SSL should always be on as connections will fail without it
    assert "sslmode=require" in maybe_connstr
    db_config = dj_database_url.parse(
        maybe_connstr,
        conn_max_age=600,
    )
else:
    db_config = dj_database_url.config(
        # Per previous config, may not actually be the right value?
        conn_max_age=600,
        # Should always be True except with local developlment?
        ssl_require=_is_heroku,
    )

DATABASES = {
    "default": db_config,
}


# Password validation
# https://docs.djangoproject.com/en/2.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/2.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_L10N = True

USE_TZ = True


# No /accounts/profile/ path set up which is the default
LOGIN_REDIRECT_URL = "/"


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.2/howto/static-files/

STATIC_URL = "/static/"


# Collect static files into here on heroku deploy
# Should be empty in repo except for .gitkeep

STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")

# This is another local filesystem path, does it work in deployment?
WHITENOISE_ROOT = os.path.join(BASE_DIR, "frontend", "root")


# Bring together static files from all these dirs into static root

STATICFILES_DIRS = [
    ("css", os.path.join(BASE_DIR, "frontend/js/dist/css/")),
    ("js", os.path.join(BASE_DIR, "frontend/js/dist/js/")),
    ("img", os.path.join(BASE_DIR, "frontend/img/")),
    ("lang", os.path.join(BASE_DIR, "frontend/js/dist/lang/")),
]

STATICFILES_STORAGE = "whitenoise.storage.CompressedManifestStaticFilesStorage"

# Email config
# see https://docs.djangoproject.com/en/3.2/topics/email/

# We use the default Django email backend, but if we're in debug mode we switch that
# out for the console backend so that emails are not sent but printed to stdout instead
if DEBUG:
    EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"

# Use Amazon SES details in standard Django email param fields
# see https://docs.aws.amazon.com/ses/latest/dg/send-email-smtp.html
EMAIL_HOST = "email-smtp.eu-west-2.amazonaws.com"
EMAIL_PORT = 587
EMAIL_HOST_USER = os.environ.get("AWS_SES_USERNAME")
EMAIL_HOST_PASSWORD = os.environ.get("AWS_SES_PASSWORD")
EMAIL_USE_TLS = True
DEFAULT_FROM_EMAIL = "Visual Meaning <<EMAIL>>"

SESSION_ENGINE = "django.contrib.sessions.backends.signed_cookies"

# Seeting these is mostly pointless (as documented by the Django project) but
# gives Highways a feeling they're doing all the right things on security.
# Breaks local testing with http: though so don't want to set generally.
if _is_azure:
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_HTTPONLY = True
    CSRF_COOKIE_SECURE = True

LOGGING = custom_logging.DJANGO_LOGGING_CONFIG

# Django-axes configuration for blocking brute force of login
# see https://django-axes.readthedocs.io/en/latest/4_configuration.html

# Lockout after 5 failed login attempts
AXES_FAILURE_LIMIT = 5
# Clear lockout after 10 minutes of no login attempts
AXES_COOLOFF_TIME = datetime.timedelta(minutes=10)
# Use combination of username+IP when counting failed logins instead of just IP - important for testing
AXES_LOCKOUT_PARAMETERS = [["username", "ip_address"]]
# Always allow GET requests -- otherwise the lockout is total and a user cannot access the site at all
AXES_NEVER_LOCKOUT_GET = True
AXES_LOCKOUT_TEMPLATE = "login_lockout.html"
# All requests to Heroku dyno come through a router, need to get the real IP of the client
# from x-forwarded-for - see https://devcenter.heroku.com/articles/http-routing
AXES_IPWARE_META_PRECEDENCE_ORDER = [
    "HTTP_X_FORWARDED_FOR",
    "REMOTE_ADDR",
]

# System configuration for the blob store details to proxy through to
BLOBPROXY_CONN_STR = os.environ.get(
    "CUSTOMCONNSTR_BLOBPROXY_CONN_STR"
) or os.environ.get("BLOBPROXY_CONN_STR")
BLOBPROXY_CONTAINER = os.environ.get("BLOBPROXY_CONTAINER")

# Application role to enforce for map access in Azure permissions scheme
AZURE_REQUIRED_ROLES = os.environ.get("AZURE_REQUIRED_ROLES")

# Configuration for SSO signin availability
MSAL_APP_CLIENT_SECRET = os.environ.get("MICROSOFT_APP_REGISTRATION_CLIENT_SECRET")
MSAL_APP_CLIENT_ID = os.environ.get("MICROSOFT_APP_REGISTRATION_CLIENT_ID")
SSO_AVAILABLE = bool(MSAL_APP_CLIENT_SECRET and MSAL_APP_CLIENT_ID)

# these have special case handling for map display, metrics tracking and branding
DEV_SITES = (
    "experiments.ecosystem.guide",
    "staging.ecosystem.guide",
    "localhost:8000",
    "127.0.0.1:8000",
)
