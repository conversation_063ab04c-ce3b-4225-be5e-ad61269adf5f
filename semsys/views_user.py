# Copyright 2020-2021 Visual Meaning Ltd

"""
View functions for surfacing user data.
"""

from django import http
import django.views.decorators.http as http_decorators


@http_decorators.require_http_methods(["GET"])
def get_user_data(request):
    """API for accessing data about the current user."""
    user = request.user
    email = user.email if user.is_authenticated else request.session.get("email")
    user_data = {
        "email": email,
    }
    # TODO 2022-10-27: Gives `null` for email for anon access, should probably not?
    return http.JsonResponse({"user": user_data})
