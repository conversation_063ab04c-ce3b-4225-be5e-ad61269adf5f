"""
Helper for efficient return of JSON data from database to client.

Views that want this should also use the `@gzip_page` decorator.

For a very large map model, like ~12MiB Highways, measured on staging:
Before: around 0.8s waiting + 2.2s transferring
After: around 0.9s waiting + 0.2s transferring
"""

import zlib
from django.db.models.functions import Cast
from django.db.models import TextField
import django.core.serializers.json as django_json
from django.http import Http404, HttpResponse
from django.shortcuts import _get_queryset
from django.core.cache import cache


from .shortercuts import get_latest_or_None


def spliced_terms(list_terms_queryset, *latest_fields):
    terms = []
    for qset in list_terms_queryset:
        # none of this is executed yet, it's still a QuerySet with a bunch of
        # queries and casts and filters hanging off of it
        annotated = (
            _get_queryset(qset)
            .annotate(raw_terms=Cast("terms", TextField()))
            .values_list("raw_terms", flat=True)
        )
        # *now* it gets executed -- return the raw terms for the MapTermsBag matching
        # the parameter in the kwargs
        raw = get_latest_or_None(annotated, *latest_fields)
        if not raw:
            raise Http404("missing terms bag")
        elif raw != "[]":
            # strip off square brackets as we want to combine into single list
            terms.append(raw[1:-1])
    return "[" + ", ".join(terms) + "]"


def result_including_terms(template, map):
    """Give response from json template and JsonField terms.

    Template must be a json-serialiseable object with exactly one placeholder
    string which will be replaced with terms fetched from model.
    """
    jsoner = django_json.DjangoJSONEncoder(ensure_ascii=False)
    raw = jsoner.encode(template)
    head, _, tail = raw.partition('"__PLACEHOLDER"')
    if not tail:
        raise ValueError("invalid template")

    return HttpResponse(
        [head, map.all_terms_as_string, tail], content_type="application/json"
    )


def cache_compressed_terms(map_iri, string_terms):
    """Compress stringified map terms and store in cache."""
    # Used by post-model-save methods to rebuild full terms cache on bag save.
    compressed_terms = zlib.compress(string_terms.encode("utf-8"), 9)
    cache.set(map_iri, compressed_terms)
    return compressed_terms


def get_cached_terms(map):
    cached_model = cache.get(map.iri)
    # If cache doesn't exist, saves the cache
    if cached_model is None:
        cached_model = cache_compressed_terms(map.iri, map.all_terms_as_string)
    response = HttpResponse(cached_model, content_type="application/json")
    response.headers["Content-Encoding"] = "deflate"
    return response


# somewhat redundant to have helper functions for these, but there's benefit
# in having all terms caching operations in one place.
def delete_cached_terms(map_iri):
    cache.delete(map_iri)


def clear_all_cached_terms():
    cache.clear()
