# Copyright 2020 Visual Meaning Ltd

"""
View functions for serving frontend index pages.

Resolving the awkwardness of both the JS build process and Django caring about
the contents of html index pages. For now, these are helper functions to match
current use by helping to serve the frontend index pages from local disk, but
without requiring the ``collectstatic`` management command.

Note, this relies on ``DEBUG = True`` only behaviour of staticfiles, this could
be handled at the WSGI or proxy server level by giving a response with headers
``X-Sendfile`` or ``X-Accel-Redirect`` instead.

The sane solution is probably to remove all auth checking from index pages and
instead validate only at the api level. Then have the React app aware of
403 Forbidden responses and be able to client side redirect for login.


https://docs.djangoproject.com/en/3.0/howto/static-files/
https://www.nginx.com/resources/wiki/start/topics/examples/x-accel/
"""

import django.views.decorators.http as http_decorators
import django.views.decorators.vary as vary_decorators

from django.shortcuts import render


@vary_decorators.vary_on_cookie
@http_decorators.require_safe
def safe_render(req, path, context):
    return render(req, path, context)
