# Copyright 2020 Visual Meaning Ltd

"""
View functions for handling user annotations.

This is intended to become a generic interface, and built off Django user
details but for now is a pretty dumb json store.
"""

import collections
import itertools

from django import http
from django.shortcuts import get_object_or_404
import django.contrib.auth.decorators as auth_decorators
import django.views.decorators.http as http_decorators

from . import annotations_report
from .custom_decorators import load_map_from_iri
from .helpers import get_auth_module, excel_response
from .json import load_json_body
from .models import (
    Map,
    UserMapAnnotationBag,
    MapTermsBag,
)
from .rdf import IRIS, from_qname
from .shortercuts import get_latest_or_None, get_object_or_None


auth_module = get_auth_module()


def validate_user_stub(request, claimed_who):
    """Check `claimed_who` against user authenticated in request."""
    email = auth_module.get_email_from_req(request)
    if email is None:
        return claimed_who.startswith("anon-") and "@" not in claimed_who
    # No case normalisation, expect stored values to exactly match
    return email == claimed_who


@http_decorators.require_http_methods(["GET", "POST"])
def user_map_annotations(request):
    """API for accessing user specific map annotations."""
    try:
        map_iri = from_qname(request.GET["map"])
        who_ish = request.GET["who"]
    except KeyError as e:
        return http.JsonResponse(dict(error=str(e)), status=404)

    map_obj = get_object_or_404(Map, iri=map_iri)
    if not validate_user_stub(request, who_ish):
        return http.JsonResponse(dict(error="mismatched who"), status=401)

    if request.method == "GET":
        annotations = get_object_or_None(
            UserMapAnnotationBag.objects.filter(
                map__iri=map_iri, user_stub=who_ish
            ).order_by("-created_on")[0:1]
        )
        return http.JsonResponse({"terms": annotations.terms if annotations else []})

    content, err = load_json_body(request)
    if err:
        return err

    annotations = UserMapAnnotationBag.objects.create(
        map=map_obj, user_stub=who_ish, terms=content
    )

    return http.JsonResponse(dict(created_on=annotations.created_on))


def _count_terms(terms):
    # For now special casing votes rather than accounting for all annotations
    _as_count = {IRIS["VM"]["voteOne"]: 1, IRIS["VM"]["voteTwo"]: 2}
    return sum(_as_count.get(t["obj"], 0) for t in terms)


@http_decorators.require_http_methods(["GET"])
def annotations_summary(request):
    """Admin-only API to create reports of user annotations over time."""
    if not request.user.is_superuser:
        return http.JsonResponse(dict(error="unauthorized"), status=401)
    try:
        map_iri = from_qname(request.GET["map"])
    except KeyError as e:
        return http.JsonResponse(dict(error=str(e)), status=404)

    summaries = [
        dict(
            created_on=obj.created_on,
            user=obj.user_stub,
            count=_count_terms(obj.terms),
        )
        for obj in UserMapAnnotationBag.objects.filter(map__iri=map_iri).order_by(
            "user_stub", "-created_on"
        )
    ]
    return http.JsonResponse(dict(annotations=summaries))


def _flattened_latest_user_map_annotation_bag(map_iri):
    umabs = UserMapAnnotationBag.objects.filter(map__iri=map_iri)
    # need most recent bag per user -- no good way of doing HAVING max(created_on)
    # equivalent with a Django Queryset, so need to do it in a more ugly way
    users = {umab.user_stub for umab in umabs}
    terms = [umabs.filter(user_stub=u).latest("created_on").terms for u in users]
    return itertools.chain.from_iterable(terms)


def _terms_to_dict(terms):
    output = collections.defaultdict(dict)
    for t in terms:
        output[t["subj"]][t["pred"]] = t["obj"]
    return output


@auth_decorators.login_required
@http_decorators.require_http_methods(["GET"])
@load_map_from_iri(read_only=False)
def map_votes_report(request, map_obj):
    """Return a count of votes per voted-on object for a given map."""
    # this should always exist, even if it's an empty list
    bag = get_latest_or_None(MapTermsBag.objects.filter(map=map_obj))

    # convert terms into crude dict so we can look up object name by IRI later
    map_terms_dict = _terms_to_dict(bag.terms)

    flattened = _flattened_latest_user_map_annotation_bag(map_obj.iri)

    annotations_dict = _terms_to_dict(flattened)

    pred_lookup = annotations_report.PREDICATES
    votes = collections.defaultdict(int)
    for item in annotations_dict:
        body = annotations_dict[item].get(pred_lookup["comment_body"])
        # very awkward way of identifying annotations which are votes
        if body and (body.endswith("vote-1") or body.endswith("vote-2")):
            iri = annotations_dict[item][pred_lookup["target"]]
            votes[iri] += int(body[-1])

    def voterows():
        # yield header first
        yield ["iri", "name", "type", "votes"]
        for iri, votecount in votes.items():
            yield {
                "iri": iri,
                "name": map_terms_dict[iri].get(pred_lookup["name"]),
                "type": map_terms_dict[iri].get(pred_lookup["type"]),
                "votes": votecount,
            }

    filename = "{}-voting-report.csv".format(map_obj.iri.split("/")[-1])

    return excel_response(filename, voterows())


# TODO: appropriate permission requirement for reading this report API
@auth_decorators.login_required
@http_decorators.require_http_methods(["GET"])
def delete_annotations_for_map(request):
    """For a named map, deletes all annotations owned by the current user."""
    user = request.user
    other_email = request.GET.get("user_email")
    if not other_email:
        # by default, delete logged in user's own annotations for this map
        email = user.email
    else:
        # if user has delete permissions on model, may provide other user's
        # email to instead delete annotations for that user
        if not user.has_perm("semsys.delete_usermapannotationbag"):
            return http.JsonResponse(dict(error="unauthorized"), status=401)
        email = other_email

    map_iri = from_qname(request.GET["map"])

    annotations = UserMapAnnotationBag.objects.filter(map__iri=map_iri, user_stub=email)

    deleted = annotations.delete()

    return http.JsonResponse(
        {
            "map_iri": map_iri,
            "user_email": email,
            "deleted_annotation_bags": deleted[0],
        }
    )


@auth_decorators.login_required
@http_decorators.require_http_methods(["GET"])
def map_annotations_report(request):
    """Return a .csv report of annotation data for given map."""
    if "map" not in request.GET:
        return http.JsonResponse(dict(error="'map' parameter is required"), status=400)

    map_iri = from_qname(request.GET["map"])

    bag = get_latest_or_None(MapTermsBag.objects.filter(map__iri=map_iri))
    if bag is None:
        return http.JsonResponse(dict(error="map does not exist"), status=404)

    raw = request.GET.get("raw", False)

    lang = request.GET.get("lang")

    flattened = _flattened_latest_user_map_annotation_bag(map_iri)

    rows = annotations_report.generate_rows(flattened, bag.terms, lang, raw)
    # then translate into structured data and write to csv
    filename = "{}-comments-report.csv".format(map_iri.split("/")[-1])

    return excel_response(filename, rows)
