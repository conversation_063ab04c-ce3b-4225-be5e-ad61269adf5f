# Copyright 2022 Visual Meaning Ltd

"""
A set of functions for handling data in a consistent way.
"""

from django import http
from django.contrib.auth.decorators import login_required
import django.views.decorators.http as http_decorators

from .helpers import get_auth_module
from .terms_response import clear_all_cached_terms

auth_module = get_auth_module()


@http_decorators.require_http_methods(["GET"])
def get_maps(req):
    """Returns JSON for top level details for all maps in the system."""
    maps = auth_module.get_map_queryset(req)

    return http.JsonResponse(
        {
            "maps": [
                {
                    "iri": m.iri,
                    "name": m.name,
                    "description": m.description,
                    "created-on": m.created_on.isoformat(),
                    "project-iri": m.project.iri,
                }
                for m in maps
            ]
        }
    )


@login_required
def clear_terms_cache(req):
    """Clear the terms cache table. Will delete all cached compressed mapterms."""
    if not req.user.is_superuser:
        return http.JsonResponse(dict(error="unauthorized"), status=401)
    clear_all_cached_terms()
    return http.JsonResponse({"result": "success"})
