"""Helpers for handling json in requests and responses."""

import json
import zlib

from django import http


def load_json_body(request):
    """
    Decode body from request as (possibly compressed) json.

    Returns a Go-style 2-tuple of `(None, response)` or `(parsedJson, None)`
    so error responses can be returned by callers.
    """
    content_type = request.headers.get("content-type")
    if content_type.startswith("multipart/form-data"):
        # Form data upload which should have file containing json
        jsonfile = request.FILES.get("json")
        if jsonfile is None:
            return None, http.JsonResponse(
                dict(error="missing json file in form"), status=400
            )
        suffix = jsonfile.name.rsplit(".")[-1]
        # Not trying to map file suffix to encoding name as deflate only
        encoding = None if suffix == "json" else suffix
        postbody = jsonfile.read()
    else:
        # Raw POST body that should be (maybe encoded) json
        if content_type != "application/json":
            return None, http.JsonResponse(
                dict(error="unsupported content-type: " + repr(content_type)),
                status=415,
            )
        encoding = request.headers.get("content-encoding")
        postbody = request.body

    if encoding is None:
        body = postbody
    elif encoding == "deflate":
        # TODO: See httpx.DeflateDecoder for fancier but no bomb handling
        try:
            body = zlib.decompress(postbody)
        except zlib.error as e:
            return None, http.JsonResponse(
                dict(error="deflate error: " + str(e)), status=400
            )
    else:
        return None, http.JsonResponse(
            dict(error="unsupported content-encoding: " + repr(encoding)), status=415
        )
    return json.loads(body), None
