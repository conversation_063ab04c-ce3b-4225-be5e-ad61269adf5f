# Copyright 2020 Visual Meaning Ltd

"""
View functions for persistence of external purpose tool.

Basically everything punted on here, as there's no good way of relating the
created model to anything within the platform without also fixing auth. So,
really just very slightly fancier way of shoving JSON somewhere.
"""

import json

from django import http
from django.shortcuts import get_object_or_404
import django.views.decorators.csrf as csrf_decorators
import django.views.decorators.http as http_decorators

from .models import (
    PurposeBag,
)


@csrf_decorators.csrf_exempt
@http_decorators.require_http_methods(["GET", "POST"])
def purpose(request):
    """API for accessing purpose model."""
    purpose_name = request.GET.get("name")

    if request.method == "GET":
        if purpose_name:
            purpose_model = get_object_or_404(
                PurposeBag.objects.filter(name=purpose_name).order_by("-created_on")[
                    0:1
                ]
            )
            return http.JsonResponse(
                {
                    "name": purpose_model.name,
                    "terms": purpose_model.terms,
                    "stamp": purpose_model.created_on,
                }
            )
        purpose_names = list(PurposeBag.objects.values("name").distinct())
        return http.JsonResponse({"purposes": purpose_names})

    # TODO: general loading of JSON and error handling
    content = json.loads(request.body)

    # TODO: Throw 409 on existing stamp mismatch?
    purpose_model = PurposeBag.objects.create(**content)
    return http.JsonResponse(dict(stamp=purpose_model.created_on))
