# Copyright 2021-2022 Visual Meaning Ltd

"""Context processors that run on every request."""

from django.conf import settings
from django.contrib.sites.shortcuts import get_current_site


STAGING_SITES = [
    "staging.ecosystem.guide",
]


def metrics_site(request):
    """Which endpoint should we send tracking metrics to?"""
    metrics_site = None

    if settings.PLAUSIBLE_METRICS:
        site = get_current_site(request)

        if site.name in STAGING_SITES:
            metrics_site = "staging.ecosystem.guide"
        else:
            metrics_site = "eom.ecosystem.guide"
        # plausible turns itself off if running under localhost, so no point
        # adding condition for dev

    return {"metrics_site": metrics_site}


def auth_framework(request):
    # TODO: this is a hack fix to work around page templates being aware
    # of request.user ID, which is behaviour that needs to be turned off
    # if we're using a non-VM auth framework that doesn't have that info.
    # If we fix the page templates this won't be necessary any more.
    return {"auth_framework": settings.AUTH_FRAMEWORK}
