# Copyright 2022 Visual Meaning Ltd

"""Stub permissions interfaces for Azure deployments.

As authentication is externally handled via EasyAuth, this just permits all
access, and relies on the `/.auth/` paths for frontend integration.
"""

import base64
import binascii
import json
import logging

from django.conf import settings

from .models import Map
from .views_frontend import safe_render


ME_PATH = "/.auth/me"


# TODO: loggers suck, should really be using metrics instead
logger = logging.getLogger(__name__)


def get_email_from_req(req):
    # In the easyauth headers, id is a uuid and name is the user_id email
    return req.headers.get("x-ms-client-principal-name")


def _try_decode_claims(encoded_claims):
    """Take claims from header set by easyauth and yield resulting pairs."""
    if not encoded_claims:
        logger.warning("azure claims not present")
        return None

    try:
        cred = json.loads(base64.b64decode(encoded_claims))
    except (binascii.Error, json.JSONDecodeError) as err:
        logger.warning("azure claims decode fail: %s", err)
        return None

    try:
        return [(claim["typ"], claim["val"]) for claim in cred["claims"]]
    except (<PERSON><PERSON><PERSON><PERSON>, TypeError) as err:
        logger.warning("azure claims bad format: %s", err)


def _claims_access_role(request, role_required):
    claims = _try_decode_claims(request.headers.get("x-ms-client-principal"))
    if claims is not None:
        roles = set(v for k, v in claims if k == "roles")
        logger.debug("azure user has roles: %r", roles)
        if not role_required or role_required in roles:
            return True
        logger.warning("azure role missing: %r not in %r", role_required, roles)
    return False


def user_map_access(request, target_map):
    if request.user.is_superuser:
        return "full"
    if _claims_access_role(request, settings.AZURE_REQUIRED_ROLES):
        return "read"
    return "none"


def get_map_queryset(request):
    if _claims_access_role(request, settings.AZURE_REQUIRED_ROLES):
        return Map.objects.filter(public=True)
    return Map.objects.none()


def showTemplate(req, map, template_name, context):
    return safe_render(req, template_name, context)
