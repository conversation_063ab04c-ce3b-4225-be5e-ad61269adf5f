## RDF constants
##
## The contents of this file MUST be essentially identical to rdf.js in the
## 'frontend' tree; modifications made to one must be reflected in the other.

import re

##  ----------------------------------------------------------------------
##  Stems of IRIs

PREFIXES = {
    "rdf": "http://www.w3.org/1999/02/22-rdf-syntax-ns#",
    "rdfs": "http://www.w3.org/2000/01/rdf-schema#",
    "vm": "http://visual-meaning.com/rdf/",
}


##  ----------------------------------------------------------------------
##  Whole IRIs, as used across the apps

IRIS = {
    "RDF": {
        "type": PREFIXES["rdf"] + "type",
    },
    "RDFS": {
        "subClassOf": PREFIXES["rdfs"] + "subClassOf",
        "subPropertyOf": PREFIXES["rdfs"] + "subPropertyOf",
        "label": PREFIXES["rdfs"] + "label",
        "comment": PREFIXES["rdfs"] + "comment",
        "domain": PREFIXES["rdfs"] + "domain",
        "range": PREFIXES["rdfs"] + "range",
    },
    "VM": {
        "atGeoPoint": PREFIXES["vm"] + "atGeoPoint",
        "atGeoPoly": PREFIXES["vm"] + "atGeoPoly",
        "minGeoPoint": PREFIXES["vm"] + "minGeoPoint",
        "maxGeoPoint": PREFIXES["vm"] + "maxGeoPoint",
        "stakeholder": PREFIXES["vm"] + "Stakeholder",
        "painpoint": PREFIXES["vm"] + "Painpoint",
        "brightspot": PREFIXES["vm"] + "Brightspot",
        "story": PREFIXES["vm"] + "Story",
        "storypoint": PREFIXES["vm"] + "Storypoint",
        "firstPoint": PREFIXES["vm"] + "firstPoint",
        "followedBy": PREFIXES["vm"] + "followedBy",
        "Issue": PREFIXES["vm"] + "Issue",
        "usesMapTiles": PREFIXES["vm"] + "usesMapTiles",
        "hasIcon": PREFIXES["vm"] + "hasIcon",
        "hasAspect": PREFIXES["vm"] + "hasAspect",
        "ofStakeholder": PREFIXES["vm"] + "ofStakeholder",
        "ofStory": PREFIXES["vm"] + "ofStory",
        "hasContentItem": PREFIXES["vm"] + "hasContentItem",
        "name": PREFIXES["vm"] + "name",
        "description": PREFIXES["vm"] + "description",
        "harmsCapitalType": PREFIXES["vm"] + "harmsCapitalType",
        "Voter": PREFIXES["vm"] + "Voter",
        "Campaign": PREFIXES["vm"] + "Campaign",
        "invitedOn": PREFIXES["vm"] + "invitedOn",
        "hasVoter": PREFIXES["vm"] + "hasVoter",
        "email": PREFIXES["vm"] + "email",
        "url": PREFIXES["vm"] + "url",
        "voteOne": PREFIXES["vm"] + "vote-1",
        "voteTwo": PREFIXES["vm"] + "vote-2",
        "ContentItem": PREFIXES["vm"] + "ContentItem",
        "hasUUID": PREFIXES["vm"] + "hasUUID",
        "hasContentType": PREFIXES["vm"] + "hasContentType",
        "relates": PREFIXES["vm"] + "relates",
        "hasCategory": PREFIXES["vm"] + "hasCategory",
        ## All the capital types we know about
        "CAPTYPE": {
            "social": PREFIXES["vm"] + "capitalTypes/social",
            "sharedFin": PREFIXES["vm"] + "capitalTypes/sharedFinancial",
            "human": PREFIXES["vm"] + "capitalTypes/human",
            "environ": PREFIXES["vm"] + "capitalTypes/environmental",
        },
        ## And all the content types
        "CONTENTTYPE": {
            "image": PREFIXES["vm"] + "contentTypes/image",
            "video": PREFIXES["vm"] + "contentTypes/video",
        },
    },
}

_pre_re = re.compile("^(" + "|".join(sorted(PREFIXES, key=len, reverse=True)) + "):")


def from_qname(maybe_qname):
    match = _pre_re.match(maybe_qname)
    if match:
        prefix = match.group(1)
        return PREFIXES[prefix] + maybe_qname[len(prefix) + 1 :]
    return maybe_qname
