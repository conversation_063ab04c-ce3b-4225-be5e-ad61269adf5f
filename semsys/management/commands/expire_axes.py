# Copyright 2024 Visual Meaning Ltd

"""
Management function to remove old axes database records.

Intended to be run periodically so that potentially identifying details about
users is stored only for as long as needed to protect against attacks and
troubleshoot access problems.
"""

import datetime

from axes.models import AccessLog
from django.utils import timezone
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    """Run with `python3 manage.py expire_axes` at the top level."""

    def handle(self, *args, **opts):
        expiry = timezone.now() - datetime.timedelta(days=30)
        count, _ = AccessLog.objects.filter(attempt_time__lt=expiry).delete()
        self.stdout.write(self.style.SUCCESS(f"Removed {count} access log records"))
