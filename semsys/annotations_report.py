# Copyright 2021 Visual Meaning Ltd

"""
Build and return report data for a summary of all annotations across a single
map.
"""

import re

from .rdf import IRIS


# relevant predicates
PREDICATES = {
    "comment_body": "http://www.w3.org/ns/oa#hasBody",
    "comment_author": "http://purl.org/dc/terms/creator",
    "has_category": IRIS["VM"]["hasCategory"],
    "motivated_by": "http://www.w3.org/ns/oa#motivatedBy",
    "commenting": "http://www.w3.org/ns/oa#commenting",
    "target": "http://www.w3.org/ns/oa#hasTarget",
    "type": IRIS["RDF"]["type"],
    "name": IRIS["VM"]["name"],
    "relates": IRIS["VM"]["relates"],
    "ofStakeholder": IRIS["VM"]["ofStakeholder"],
}

# ordered list of column names for output csv
COLUMNS = [
    "Commented By (Email)",
    "Category",
    "Comment",
    "On Type",
    "On Name",
    "Painpoint/Brightspot Type",
    "Painpoint/Brightspot Name",
    "Object IRI",
]


def lang_filter(terms, lang):
    for term in terms:
        if lang and re.search(r"@[a-z]{2}$", term["obj"]):
            if not term["obj"].endswith(lang):
                continue
            # this is the language we want, so snip off language terminator
            term = {
                "subj": term["subj"],
                "pred": term["pred"],
                "obj": term["obj"][1:-4],
            }
        yield term


def _terms_to_dict(terms, lang):
    holder = {}
    for term in lang_filter(terms, lang):
        subj = holder.setdefault(term["subj"], {})
        subj[term["pred"]] = term["obj"]
    return holder


def _painpoint_fields(target, mterms_dict):
    # which spelling are we using?
    stakeholder = target.get(PREDICATES["ofStakeholder"])
    if not stakeholder:
        stakeholder = target.get(PREDICATES["relates"])
    sh_name = mterms_dict.get(stakeholder, {}).get(PREDICATES["name"])
    sh_type = mterms_dict.get(stakeholder, {}).get(PREDICATES["type"], "")
    # get type name from IRI as can't rely on ontology being present
    sh_type = sh_type.rpartition("/")[2]
    return sh_name, sh_type, target.get(PREDICATES["name"])


def _derive_category(category, mterms_dict):
    # if category is empty we can't do anything with it
    if not category:
        return "Unknown"
    # try to look the name up from the class in the map terms
    cname = mterms_dict.get(category, {}).get(PREDICATES["name"])
    if cname:
        return cname
    # IRI that didn't match? return rightmost chunk of URL for somewhat human-parsable output
    if category.startswith("http://"):
        return category.rpartition("/")[2].capitalize()
    # otherwise it's a bare category name, so just return
    return category


def _name_for_class(type_iri, mterms_dict):
    name = mterms_dict.get(type_iri, {}).get(PREDICATES["name"])
    if not name:
        name = type_iri.partition("#")[2] or type_iri.rpartition("/")[2]
    return name


def generate_row_from_terms(aterms_dict, mterms_dict):
    """Construct a single row of data as a dict of form {column: value}."""
    row = {
        "Comment": aterms_dict.get(PREDICATES["comment_body"]),
        "Commented By (Email)": aterms_dict.get(PREDICATES["comment_author"]),
        "Object IRI": aterms_dict.get(PREDICATES["target"]),
    }
    # derive category name from hasCategory - this should be a link to a class
    # with a name predicate, but could also be a bare name
    category = aterms_dict.get(PREDICATES["has_category"], "")
    row["Category"] = _derive_category(category, mterms_dict)

    # derive additional data depending on target type
    target = mterms_dict.get(aterms_dict.get(PREDICATES["target"]), {})
    type_iri = target.get(PREDICATES["type"])

    if type_iri:
        obj_type = _name_for_class(type_iri, mterms_dict)
        # if it's a painpoint or a brightspot we need to get the parent object
        # as the target and separately log the painpoint
        if type_iri in (IRIS["VM"]["painpoint"], IRIS["VM"]["brightspot"]):
            pp_type = obj_type
            target_name, obj_type, pp_name = _painpoint_fields(target, mterms_dict)
        else:
            target_name = target.get(PREDICATES["name"])
            pp_name = None
            pp_type = None

        row.update(
            {
                "On Type": obj_type,
                "On Name": target_name,
                "Painpoint/Brightspot Type": pp_type,
                "Painpoint/Brightspot Name": pp_name,
            }
        )

    return row


def generate_rows(aterms, mterms, lang, raw=False):
    """Generate row data for a report .csv of all annotations on one map."""
    # aterms is a flattened list of all annotation terms for a single map
    # convert list of annotations terms into one dict per individual annotation
    annotations_dict = _terms_to_dict(aterms, lang)

    # mterms is the list of map terms
    # convert into one dict per subject entity so that we can look up things like
    # stakeholder name based on IRI
    mterms_dict = _terms_to_dict(mterms, lang)

    unique_rows = {}

    key_columns = COLUMNS
    if not raw:
        # comments on the same entity with the same category should be aggregated
        # together into a single value, which needs us to key each row on the
        # rest of the column values
        key_columns = [n for n in COLUMNS if n != "Comment"]

    for anno in annotations_dict.values():
        if anno.get(PREDICATES["motivated_by"]) != PREDICATES["commenting"]:
            continue
        row = generate_row_from_terms(anno, mterms_dict)

        # need to find duplicate rows based on key columns and concatenate
        # comment values together if dupe is found, and then discard dupe row
        dedupe_key = ""
        for key in key_columns:
            if row.get(key):
                dedupe_key += row[key]
        if dedupe_key in unique_rows:
            old_comment = unique_rows[dedupe_key]["Comment"]
            new_comment = " | ".join([old_comment, row["Comment"]])
            unique_rows[dedupe_key]["Comment"] = new_comment
        else:
            unique_rows[dedupe_key] = row

    # return headers first so that we can use them to initialise the DictWriter
    yield COLUMNS

    for row in unique_rows.values():
        # newline characters mess with .csv formatting so escape them
        row["Comment"] = row["Comment"].replace("\n", "\\n")
        yield row
