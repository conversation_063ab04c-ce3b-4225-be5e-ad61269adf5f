# Copyright 2022 Visual Meaning Ltd

"""Miscellaneous helper functions used across multiple modules."""

import codecs
import csv
import importlib

from django.conf import settings
from django.http import HttpResponse, HttpResponseRedirect, QueryDict
from django.shortcuts import resolve_url
from django.contrib.admin.models import ADDITION, CHANGE, DELETION, LogEntry
from django.contrib.contenttypes.models import ContentType
from django.contrib.sites.requests import RequestSite
from django.contrib.sites.shortcuts import get_current_site

from urllib.parse import unquote

_auth_module = None


def get_auth_module():
    """Return the appropriate auth module, lazy-loading if necessary."""
    # AUTH_MODULE controls which auth framework is used for access to the SMP
    global _auth_module
    if not _auth_module:
        _auth_module = importlib.import_module(settings.AUTH_MODULE)
    return _auth_module


def redirect_with_parameters(maybe_url, params, *args, **kwargs):
    """Redirect to path with params dict attached as query parameters."""
    # maybe_url is either a raw url or the name of a path in urls.py, so resolve to URL
    root_path = resolve_url(maybe_url, *args, **kwargs)
    # I can't remember why I've done this extremely weird method of param generation but I'm
    # sure there's a good reason
    qdict = QueryDict(mutable=True)
    for key, value in params.items():
        qdict[key] = value
    redirect_url = "{}?{}".format(root_path, qdict.urlencode(safe="/:"))
    return HttpResponseRedirect(redirect_url)


def _unquote(param):
    # next may be urlencoded and there's potential edge cases where it can be encoded multiple times
    # so unencode, check whether it has any further encoding, and then either run again if it does
    # or return if it doesn't
    unquoted = unquote(param)
    if not param == unquoted:
        return _unquote(unquoted)
    return param


def get_next_from_req(req, default="/"):
    """Get the `next` parameter from a Django request object"""
    next_params = None
    if req.method == "GET":
        next_params = req.GET.get("next")
    if req.method == "POST":
        next_params = req.POST.get("next")
    # Most instances of next are urlencoded using a filter tag in the HTML templates that
    # construct the URLs used to pass it around.
    # TODO: This should not be necessary as the QueryDict on the request should automatically
    # apply one level of decoding to any params. Unfortunately there's an edge case with the
    # guest-email form where next gets double-encoded on the POST which I haven't been able
    # to debug, so _unquote is unfortunately necessary.
    if next_params:
        return _unquote(next_params)
    return default


def get_site_with_override(req):
    """Get the current site with an optional override from the request parameters."""
    site = req.session.get("site")
    if site:
        override = RequestSite(req)
        override.name = site
        return override
    return get_current_site(req)


def excel_response(filename, rows):
    response = HttpResponse(
        content_type="text/csv",
        headers={"Content-Disposition": "attachment; filename={}".format(filename)},
    )
    # add BOM so Excel can detect UTF8 encoding properly
    response.write(codecs.BOM_UTF8)
    # the first item returned from rows is a list of headers
    writer = csv.DictWriter(response, fieldnames=next(rows))
    writer.writeheader()
    for row in rows:
        writer.writerow(row)
    return response


ACTION_FLAGS = {
    "create": ADDITION,
    "change": CHANGE,
    "delete": DELETION,
}


def log_to_django_admin_table(action, request, obj, message):
    # TODO: There is a log_actions method coming in a future version of Django (>5.0.4) that
    # should make this easier.
    LogEntry.objects.create(
        user_id=request.user.pk,
        content_type_id=ContentType.objects.get_for_model(obj).pk,
        object_id=obj.pk,
        # This field has max length of 200. Django's own code just truncates if longer, so
        # we'll do the same
        object_repr=str(obj)[:200],
        action_flag=ACTION_FLAGS[action],
        change_message=message,
    )
