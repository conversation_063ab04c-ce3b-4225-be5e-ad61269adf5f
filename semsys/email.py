# Copyright 2021 Visual Meaning Ltd

import email

from django.conf import settings
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string


def _format_from(from_name, from_address):
    # make a from field that shows up as a human-readable name in email client
    name = str(email.header.Header(from_name, "utf-8"))
    return email.utils.formataddr((name, from_address))


def send_email_from_template(
    subject,
    to_address,
    text_template,
    from_name=None,
    from_address=None,
    html_template=None,
    render_args={},
):
    """Render text email template into string and send as email, with optional html template component."""
    text_content = render_to_string(text_template, render_args)

    if from_address and from_name:
        formatted_from = _format_from(from_name, from_address)
    elif from_address:
        formatted_from = from_address
    else:
        formatted_from = settings.DEFAULT_FROM_EMAIL

    msg = EmailMultiAlternatives(subject, text_content, formatted_from, [to_address])
    if html_template:
        html_content = render_to_string(html_template, render_args)
        msg.attach_alternative(html_content, "text/html")
    msg.send()
