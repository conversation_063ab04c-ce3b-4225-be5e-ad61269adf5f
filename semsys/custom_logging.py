# Copyright 2021 Visual Meaning Ltd

"""Custom logging behaviour for Django and Gunicorn."""

import os

# This config only applies for local development, as Gunicorn logging takes over when
# run in production. We have custom logging config for local dev because Django
# swallows stack traces by default.
# See https://github.com/django/django/blob/master/django/utils/log.py for original.
DJANGO_LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "django.server": {
            "()": "django.utils.log.ServerFormatter",
            "format": "[{server_time}] {message}",
            "style": "{",
        }
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
        },
        "django.server": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "django.server",
        },
    },
    "loggers": {
        "django": {
            "handlers": ["console"],
            "level": "INFO",
        },
        "django.server": {
            "handlers": ["django.server"],
            "level": "INFO",
            "propagate": False,
        },
        "semsys": {
            "handlers": ["console"],
            "level": "INFO",
        },
    },
}


# if we're in the Azure environment and we have an App Insights instance this env var should be set to use it
if os.environ.get("APPINSIGHTS_INSTRUMENTATIONKEY"):
    # add a handler that exports logs to Azure Application Insights
    DJANGO_LOGGING_CONFIG["handlers"]["azure"] = {
        "level": "DEBUG",
        "class": "opencensus.ext.azure.log_exporter.AzureLogHandler",
        "instrumentation_key": os.environ.get("APPNINSIGHTS_INSTRUMENTATIONKEY"),
        "connection_string": os.environ.get("APPLICATIONINSIGHTS_CONNECTION_STRING"),
    }
    # attach this handler to all loggers in the config - send everything that's being logged to App Insights
    for logger in DJANGO_LOGGING_CONFIG["loggers"]:
        DJANGO_LOGGING_CONFIG["loggers"][logger]["handlers"].append("azure")
