"""Proxy through requests to Azure blob storage."""

from azure.core import MatchConditions
from azure.core.exceptions import HttpResponseError
from azure.storage.blob.aio import BlobClient
from django import http
from django.conf import settings
from django.utils import http as http_utils


def _meta_to_conditions(meta):
    # TODO: Parse HTTP_IF_MATCH too and handle?
    none_etags = http_utils.parse_etags(meta.get("HTTP_IF_NONE_MATCH", ""))
    if len(none_etags) == 1:
        # Not clear what the interface would be for more than one etag passed
        return dict(
            etag=none_etags[0],
            match_condition=MatchConditions.IfModified,
        )
    # Not handled
    return {}


async def blob_get(request, blob_key):
    """Get blob from globally configured container."""
    if request.method not in ("GET", "HEAD"):
        return http.HttpResponseNotAllowed(("GET", "HEAD"))
    _download_args = _meta_to_conditions(request.META)
    async with BlobClient.from_connection_string(
        conn_str=settings.BLOBPROXY_CONN_STR,
        container_name=settings.BLOBPROXY_CONTAINER,
        blob_name=blob_key,
    ) as client:
        try:
            stream = await client.download_blob(
                # TODO: range requests
                **_download_args,
            )
        except HttpResponseError as e:
            if e.status_code == 304:
                # TODO: need to include etag on response here too?
                return http.HttpResponseNotModified()
            return http.JsonResponse(dict(error=e.reason), status=e.status_code)
        content_type = stream.properties.content_settings.content_type
        # cache_control?
        response = http.StreamingHttpResponse(
            # TEMPORARY avoiding async as part of stream
            streaming_content=stream.chunks(),
            content_type=content_type,
        )
        response["Content-Length"] = stream.size
        response["E-Tag"] = stream.properties.etag
        # stream.properties.content_range
        return response
