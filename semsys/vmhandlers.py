# Copyright 2020-2022 Visual Meaning Ltd

## HTTP request handlers for viewmap API

import itertools
import json
import re
from urllib.parse import urlsplit, urlunsplit

from django.http import (
    Http404,
    HttpResponsePermanentRedirect,
    HttpResponseRedirect,
)
from django.shortcuts import render, get_object_or_404
from django.views.decorators.gzip import gzip_page
from django.views.decorators.http import condition, require_http_methods

from .helpers import get_auth_module
from .custom_decorators import load_map_from_iri
from .models import Map
from .shortercuts import get_object_or_None, get_value_from_json
from .terms_response import (
    get_cached_terms,
    result_including_terms,
)

auth_module = get_auth_module()

# TODO: Replace users with MapTermsBag or delete calling code
MapTerm = None


def welcomePage(req):
    return render(req, "welcome.html")


def privacy_policy(req):
    return render(req, "privacy.html", {"hide_footer": True})


def _key_map_proj(m):
    """Map key for arranging by project."""
    return m.project.iri


def _key_map_full(m):
    """Map key for applying consistent order to maps, by project then name."""
    return (m.project.iri, m.name.lower(), m.iri)


def get_index_src(map_tiles_src):
    cmp_pat = re.compile(r"^(.+)\{z\}-\{x\}-\{y\}(\.\w+)")
    return cmp_pat.sub(r"\g<1>0-0-0\g<2>", map_tiles_src)


def get_first_path_seg(url_path):
    match = re.search(r"^/?[^/?#]+", url_path)
    if match:
        return match.group(0)
    return None


def get_settings(map):
    return {
        "api-root": "/api/",
        "me-url": auth_module.ME_PATH,
        "map-iri": map.iri,
        "self": map.guest_map_path,
    }


def get_map_info(map):
    return {
        "iri": map.iri,
        "name": map.name,
        "tiles-src": map.tiles_src,
        "voting-enabled": bool(map.voting_map_path),
        "voting-splash": map.voting_splash,
        "welcome-splash": map.welcome_splash,
        "map-zoom-classes": (
            json.loads(map.map_zoom_classes_json)
            if len(map.map_zoom_classes_json)
            else []
        ),
        "map-extra-css": map.map_extra_css,
    }


def mapIndexPage(req):
    """Show and link all maps the current user can access."""
    maps = auth_module.get_map_queryset(req)
    grouped = itertools.groupby(sorted(maps, key=_key_map_full), key=_key_map_proj)
    return render(
        req,
        "maps.html",
        {
            "map_groups": [
                [
                    dict(
                        name=m.name,
                        description=m.description,
                        path="/maps/" + m.guest_map_path,
                        tile=get_index_src(m.tiles_src),
                        access=getattr(m, "access", []),
                    )
                    for m in g
                    if m.guest_map_path
                ]
                for _, g in grouped
            ]
        },
    )


def fallback_to_map(req, maybe_map):
    """Redirect to current map page if maybe_map exists."""
    m = get_object_or_None(Map, guest_map_path=maybe_map + "/")
    if m is None:
        raise Http404("no such page")
    return HttpResponsePermanentRedirect("/maps/" + m.guest_map_path)


def _base_tiles_url(map):
    """Give url with scheme and host only of location of tiles for map.

    Currently this relies on the mostly-redundant `tiles_src` field to be
    accurate but could be updated the model is updated.
    """
    return urlunsplit(urlsplit(map.tiles_src)[:2] + 3 * ("",))


##  ======================================================================
##  == DB helpers


def make_next_path(req, path, remaining, default_path=""):
    """Create new path to navigate to from current request."""
    search = req.GET.urlencode()
    path += remaining or ("" if search else default_path)
    path += "?" + search if search else ""
    return path


##  ======================================================================
##  == HTML-giving entrypoint


def showMapSlash(req, mapSlug, remaining=None):
    this_map = get_object_or_404(Map, guest_map_path=mapSlug + "/")
    # returns a render() object with appropriate behaviour based on selected auth flow/permissions

    map_settings = get_settings(this_map)
    map_info = get_map_info(this_map)

    view = get_first_path_seg(remaining) if remaining else None
    tiles_src = get_value_from_json(this_map.loading_src, view or "")
    tiles_src = tiles_src or get_index_src(this_map.tiles_src)
    map_settings["placeholder-src"] = tiles_src
    map_settings["expiry-date"] = (
        req.session.get_expiry_date() if not req.session.is_empty() else None
    )

    return auth_module.showTemplate(
        req,
        this_map,
        "maps/viewmap.html",
        {
            "base_path": "/maps/",
            "next_path": make_next_path(req, mapSlug + "/", remaining),
            "map_name": this_map.name,
            "map_settings": map_settings,
            "map_info": map_info,
            "tiles_base": _base_tiles_url(this_map),
        },
    )


##  ======================================================================
##  == Map terms entrypoint


def showMapTerms(req, mapSlug, remaining=None):
    """Show and search all map terms the current user can access."""
    if remaining:
        redirect_url = "/mapterms/" + mapSlug
        return HttpResponseRedirect(redirect_url)

    this_map = get_object_or_404(Map, guest_map_path=mapSlug + "/")

    map_settings = get_settings(this_map)
    map_info = get_map_info(this_map)

    return auth_module.showTemplate(
        req,
        this_map,
        "maps/mapterms.html",
        {
            "base_path": "/mapterms/",
            "next_path": make_next_path(req, mapSlug + "/", None, default_path=""),
            "map_settings": map_settings,
            "map_info": map_info,
        },
    )


def _map_weak_etag(_, map):
    """Format for map request ETag based on terms/model last updated stamp."""
    return 'W/"{}"'.format(map.terms_updated_on.strftime("%Y%m%dT%H%M%SZ"))


##  ======================================================================
##  == Map models store/access


# TODO: This duplicates much of the functionality in edhandlers.mapModel_get, although
# they're not exactly the same and this one is the endpoint the client is hooked up to
# to get map data. Pick one to kill and then kill it.
@load_map_from_iri(read_only=True)
@gzip_page
@condition(etag_func=_map_weak_etag)
@require_http_methods(["GET"])
def mapModel_get(request, map):
    """Returns JSON of structure:
    {
        iri: String (map IRI)
        name: String
        tiles-src: String (leaflt-friendly tile source string)
        terms: [{subj:String, pred:String, obj:String}...]
        voting-enabled: Boolean
        voting-splash: String
        welcome-splash: String
        map-zoom-classes: [{name:String, min:Int, max:Int}...]
        map-extra-css: String
    }"""
    map_info = get_map_info(map)
    map_info["terms"] = "__PLACEHOLDER"
    return result_including_terms(
        map_info,
        map,
    )


@load_map_from_iri(read_only=True)
@condition(etag_func=_map_weak_etag)
@require_http_methods(["GET"])
def mapModel_get_terms(request, map):
    """Return model terms from cache. If cache doesn't exist, creates a copy in the cache."""
    return get_cached_terms(map)
