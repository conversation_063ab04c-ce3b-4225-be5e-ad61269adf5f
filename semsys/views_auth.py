# Copyright 2022 Visual Meaning Ltd

from django.conf import settings
from django.contrib.auth import login
from django.contrib.auth.views import LoginView, LogoutView, PasswordResetView
from django.contrib.sites.shortcuts import get_current_site
from django.http import Http404, HttpResponseRedirect
from django.contrib.auth import logout
from django.core.exceptions import PermissionDenied, SuspiciousOperation
from django.shortcuts import render, redirect

from .email import send_email_from_template
from .helpers import get_site_with_override, redirect_with_parameters, get_next_from_req
from .models import Map, Site
from .shortercuts import get_object_or_None
from .sso_msal import get_auth_flow, get_token, get_user_email
from .users import User
from .forms import AnonymousEmailForm
from .tokens import default_email_token_generator


class CustomLoginView(LoginView):
    # this is just the normal LoginView with a single extra line after login
    # which sets their session email to their user email, as session email
    # is what gets checked for their identity

    def form_valid(self, form):
        """Security check complete. Log the user in."""
        login(self.request, form.get_user())
        self.request.session["email"] = self.request.user.email
        return HttpResponseRedirect(self.get_success_url())


def _logout_except_site(request):
    # logout scrubs all params from session cookie, but we want to preserve the test site
    # param so that we can test signing in to different sites on staging as a logged-out user
    site = request.session.pop("site", None)
    logout(request)
    if site:
        request.session["site"] = site


class CustomLogoutView(LogoutView):
    # it's the normal LogoutView except with _logout_except_site in place of Django's logout function.

    def post(self, request, *args, **kwargs):
        _logout_except_site(request)
        redirect_to = self.get_success_url()
        if redirect_to != request.get_full_path():
            return HttpResponseRedirect(redirect_to)
        return super().get(request, *args, **kwargs)


class CustomPasswordResetView(PasswordResetView):
    # PasswordResetView has a field for html template but none set by default
    # so need to create custom version if we want to use html
    email_template_name = "registration/password_reset_email.txt"
    html_email_template_name = "registration/password_reset_email.html"


########################
# VM-native auth views #
########################


def send_validation_email(req, email, next_path):
    """Send an email containing a link with a verification token to prove they own the email."""
    site = get_current_site(req)
    token = default_email_token_generator.make_token(email)
    # these arguments are assembled into a link in the email template
    render_args = {
        "protocol": req.scheme,
        "domain": site.domain,
        "site_name": site.name,
        "token": token,
        "next": next_path,
    }
    template = "registration/validation_email.txt"
    html_template = "registration/validation_email.html"
    # if the given email matches that of an existing registered user, send a different email asking
    # them to log in instead of using an access token link.
    if User.objects.filter(email=email, is_active=True).exists():
        template = "registration/validation_email_existing_user.txt"
        html_template = "registration/validation_email_existing_user.html"

    send_email_from_template(
        "Email verification for {}".format(site.name),
        email,
        template,
        html_template=html_template,
        render_args=render_args,
    )


def guest_access_email_submitted(req):
    if not _has_guest(req):
        raise Http404()
    next_path = get_next_from_req(req)
    email = req.GET.get("email")
    if not email:
        raise SuspiciousOperation()
    return render(
        req,
        "registration/guest_email_done.html",
        {"email": email, "next": next_path},
    )


def process_next_path(next_path):
    """Processes the next_path to get map name."""
    # Strip leading and trailing slashes
    next_path = next_path.strip("/")

    if next_path.startswith("maps/"):
        parts = next_path.split("/")
        if len(parts) > 1:
            return parts[1]  # Return the map name part
    return None


def anonymous_email(req):
    """A form that takes a user's email and sends a verification email to that address."""
    if not _has_guest(req):
        raise Http404()
    next_path = get_next_from_req(req)
    # links redirecting here can optionally log the user out before doing anything, which comprehensively avoids any
    # awkwardness with user state and the PermissionDenied badness below
    # TODO: https://forum.djangoproject.com/t/deprecation-of-get-method-for-logoutview/25533
    # Logging out via a GET method is not considered safe. We could simply ask the user to click the sign out
    # button, but then we lose the next_path and token (if present). Needs some thought.
    if req.GET.get("logout"):
        _logout_except_site(req)
    # if they're still logged in they can't use this page - raise PermissionDenied, which will have a link to log them out
    # and try again
    if req.user.is_authenticated:
        raise PermissionDenied()
    if req.method == "POST":
        form = AnonymousEmailForm(req.POST)
        if form.is_valid():
            email = form.cleaned_data["email"]
            send_validation_email(req, email, next_path)
            return redirect_with_parameters(
                "guest-email-submitted", {"email": email, "next": next_path}
            )
    else:
        form = AnonymousEmailForm()

    form_vars = {"form": form, "next": next_path}
    guest_map_path = "{}/".format(process_next_path(next_path))
    map = get_object_or_None(Map, guest_map_path=guest_map_path)
    if map:
        form_vars["mapName"] = map.name
        form_vars["projectName"] = map.project.name

    return render(req, "registration/guest_email.html", form_vars)


def guest_email_logout_user(req):
    if not _has_guest(req):
        raise Http404()
    next_path = get_next_from_req(req)
    token = req.GET.get("token")
    # can't use this page if you're not coming from the Guest Access flow
    if not token:
        # this returns a 400 Bad Request response and renders 400.html
        raise SuspiciousOperation()
    # if they're not logged in as admin or guest,
    # there's nothing to do, bounce them back to the validation page
    if not req.user.is_authenticated and "email" not in req.session:
        return redirect_with_parameters(
            "guest-email-validate", {"next": next_path, "token": token}
        )
    signed_in_email = (
        req.user.email if req.user.is_authenticated else req.session.get("email")
    )
    return render(
        req,
        "registration/guest_email_logout_user.html",
        {"next": next_path, "token": token, "email": signed_in_email},
    )


def validate_token_for_email(req):
    """Check the access token in the verification email is valid, and redirect to next_path if so."""
    if not _has_guest(req):
        raise Http404()
    next_path = get_next_from_req(req)
    token = req.GET.get("token")
    if not token:
        raise SuspiciousOperation()
    # TODO: https://forum.djangoproject.com/t/deprecation-of-get-method-for-logoutview/25533
    # Logging out via a GET method is not considered safe. We could simply ask the user to click the sign out
    # button, but then we lose the next_path and token (if present). Needs some thought.
    if req.GET.get("logout"):
        _logout_except_site(req)
    email = default_email_token_generator.get_email_from_token(token)
    # if the email link they're using matches the email in their current session cookie,
    ## forward them straight on to the map - no need to re-validate
    if req.session.get("email") == email:
        return HttpResponseRedirect(next_path)
    if req.user.is_authenticated or "email" in req.session:
        return redirect_with_parameters(
            "/guest-email-logout/", {"next": next_path, "token": token}
        )
    # check email hashed in token matches base64-encoded email prefix, and that the token hasn't expired
    if default_email_token_generator.check_token(token):
        req.session["email"] = email
        return HttpResponseRedirect(next_path)
    # "expired" is not quite true, they can also get here by attempting to hack the token
    return render(
        req,
        "registration/guest_email_validated_but_expired.html",
        {"email": email, "next": next_path},
    )


def _has_guest(request):
    curr_site = get_site_with_override(request)
    site = get_object_or_None(Site, name=curr_site.name)
    return bool(site and site.guest_signin)


def _has_sso(request):
    def sso_enabled():
        curr_site = get_site_with_override(request)
        site = get_object_or_None(Site, name=curr_site.name)
        return site and site.sso_signin

    return bool(settings.SSO_AVAILABLE and sso_enabled())


def sso_login(request):
    if not _has_sso(request):
        raise Http404()
    if request.session.get("email"):
        # redirect shortcut has a built-in reverse lookup that allows redirect to
        # named URL
        return redirect("signin")
    request.session["next_url"] = get_next_from_req(request)
    auth_flow = get_auth_flow(request)
    return HttpResponseRedirect(auth_flow["auth_uri"])


def sso_callback(request):
    if not _has_sso(request):
        raise Http404()

    next_url = request.session.pop("next_url", "/")

    # In case, microsoft authentication fails or is cancelled.
    error = request.GET.get("error")
    if error:
        request.session["sso_error"] = error
        return redirect_with_parameters("signin", {"next": next_url})

    token = get_token(request)
    request.session["email"] = get_user_email(token)
    return HttpResponseRedirect(next_url)


def signin(req):
    """Handles user sign-in and processes the next path for redirection."""
    next_path = get_next_from_req(req)
    sso_error = req.session.pop("sso_error", None)

    guest_signin = _has_guest(req)
    sso_signin = _has_sso(req)
    # user might have come in via admin login, so we still need to check req.user.email
    user_email = (
        req.user.email if req.user.is_authenticated else req.session.get("email")
    )

    # if they're not signed in and they don't have sso enabled, we might be able to skip the signin
    # page. if they are signed in we want them to land on /signin so that we can tell them to
    # sign out first.
    if not sso_signin and not user_email:
        # redirect to guest email page if only guest_signin is turned on
        if guest_signin:
            return redirect_with_parameters("guest-email", {"next": next_path})
        # otherwise they have no external auth options enabled, punt them straight through to Django
        # login
        return redirect_with_parameters("login", {"next": next_path})

    processed_next_path = process_next_path(next_path)
    guest_map_path = f"{processed_next_path}/"
    map_obj = get_object_or_None(Map, guest_map_path=guest_map_path)

    render_vars = {
        "next": next_path,
        "mapName": map_obj.name if map_obj else None,
        "guest": guest_signin,
        "sso": sso_signin,
        "user_email": user_email,
        "sso_error": sso_error,
    }

    return render(req, "registration/signin.html", render_vars)


def signout(req):
    next_path = req.GET.get("next", "/")
    if req.GET.get("logout-intent") != "frontend":
        raise Http404("Page not found")

    return render(req, "registration/signout.html", {"next": next_path})
