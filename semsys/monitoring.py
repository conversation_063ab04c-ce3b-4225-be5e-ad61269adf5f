# Copyright 2020-2021 Visual Meaning Ltd

"""
Custom metrics and middleware for monitoring requests via Prometheus.
"""

import time

from django_prometheus.conf import NAMESPACE
from prometheus_client import Counter, Histogram


class VMMetrics:
    _instance = None

    @classmethod
    def get_instance(cls):
        """Return existing Metrics instance, or create if it doesn't exist."""
        if not cls._instance:
            cls._instance = cls()
            cls._instance.register()
        return cls._instance

    def register(self):
        self.requests_by_status = Counter(
            "requests_by_status",
            "Request counter by path and status.",
            ["path", "status"],
            namespace=NAMESPACE,
        )
        self.requests_duration = Histogram(
            "requests_duration",
            "Request duration histogram",
            ["path"],
            namespace=NAMESPACE,
        )


class VmMonitoringMiddleware:
    """Middleware wrapping Prometheus metrics around every request/response."""

    metrics_cls = VMMetrics

    _paths_to_label_all = (
        "/api",
        "/file-content",
        "/metrics",
    )
    _paths_to_label_top = (
        "/accounts",
        "/admin",
        "/auth-map",
        "/guest-email",
        "/maps",
    )

    def __init__(self, get_response):
        self.get_response = get_response
        self.metrics = self.metrics_cls.get_instance()

    def label_metric(self, metric, **labels):
        return metric.labels(**labels) if labels else metric

    def whitelist_path(self, path):
        """Whitelist paths we're actually interested in distinguishing."""
        if path == "/" or path.startswith(self._paths_to_label_all):
            return path
        if path.startswith(self._paths_to_label_top):
            bits = path.split("/", 2)
            if len(bits) == 3 and bits[2]:
                bits[2] = "**"
            return "/".join(bits)
        # Other stuff likely to 404 like wordpress vuln probing
        return "/junk"

    def __call__(self, request):
        t = time.perf_counter()
        response = self.get_response(request)
        duration = time.perf_counter() - t

        path = self.whitelist_path(request.path)

        self.label_metric(
            self.metrics.requests_duration,
            path=path,
        ).observe(duration)

        self.label_metric(
            self.metrics.requests_by_status,
            path=path,
            status=response.status_code,
        ).inc()

        return response
