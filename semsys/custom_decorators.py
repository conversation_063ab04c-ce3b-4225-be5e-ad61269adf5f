# Copyright 2021 Visual Meaning Ltd

"""Custom decorators encapsulating common behaviour for API requests etc."""

from functools import wraps

from django.http import JsonResponse

from .helpers import get_auth_module
from .models import Map
from .rdf import from_qname
from .shortercuts import get_object_or_None

auth_module = get_auth_module()


def resolve_map(request, map_qs, read_only):
    """Load map from iri given in request if user has access to the project.

    Return is a go-style (result, error) tuple where exactly one will be None.
    """

    # Both request query dict and model key use "iri" but are different things
    param_key = "iri"
    param_val = request.GET.get(param_key)

    if not param_val:
        message = "'{}' parameter must be passed".format(param_key)
        return None, JsonResponse(dict(error=message), status=400)

    model = get_object_or_None(map_qs, **{param_key: from_qname(param_val)})

    if model is None:
        message = "Map with this {} does not exist".format(param_key)
        return None, JsonResponse(dict(error=message), status=404)

    access = auth_module.user_map_access(request, model)
    if not (access == "full" or read_only and access == "read"):
        message = "Insufficient permissions for map {}".format(param_val)
        return None, JsonResponse(dict(error=message), status=403)

    return model, None


def load_map_from_iri(read_only=False):
    """Get `map_parameter` from request parameters and use it to look up a Map."""

    def decorator(function):
        @wraps(function)
        def wrapper(request, *args, **kwargs):
            request_map, error_response = resolve_map(request, Map, read_only)
            if error_response is not None:
                return error_response
            return function(request, request_map, *args, **kwargs)

        return wrapper

    return decorator
