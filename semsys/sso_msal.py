# Copyright 2024 Visual Meaning Ltd

"""Utility framework for Microsoft SSO Sign In Flow using MSAL"""

import re
import requests

from django.conf import settings
from django.core.exceptions import SuspiciousOperation
from django.urls import reverse

import msal

from .helpers import get_site_with_override
from .models import Site
from .shortercuts import get_object_or_None


_ms_settings = {}


def _ensure_localhost(url):
    if re.match(r"^(http://)?127\.0\.0\.1", url):
        return url.replace("127.0.0.1", "localhost", 1)
    return url


def _get_ms_settings():
    global _ms_settings
    if not _ms_settings:

        def get_redirect(request):
            url = request.build_absolute_uri(reverse("ms-auth-callback"))
            # App Registration only allows https or http://localhost redirect_uri
            return _ensure_localhost(url)

        # Microsoft Entra Tenant ID of organisation or
        # common for work and school accounts or personal Microsoft accounts
        # Should it only be for organisation?
        def get_authority(request):
            curr_site = get_site_with_override(request)
            site = get_object_or_None(Site, name=curr_site.name)
            auth_endpoint = site and site.microsoft_tenant_id or "common"
            return "https://login.microsoftonline.com/{}/".format(auth_endpoint)

        _ms_settings = {
            "app_id": settings.MSAL_APP_CLIENT_ID,
            "app_secret": settings.MSAL_APP_CLIENT_SECRET,
            # needs to be one of the callback URLs configured on the App Registration
            "get_redirect": get_redirect,
            # desired scopes for returned user access tokens
            "scopes": ["user.read"],
            # endpoint to use for SSO login
            "get_authority": get_authority,
        }
    return _ms_settings


# A msal.SerializableTokenCache can be created for more advanced usage (e.g. timeout),
# after initial sign in completes.
# Storing though is an issue at this stage. Cache is 7k, way bigger than max cookie size,
# so saving in the cookie will not work and will break the cookie.
def _get_msal_app(request, cache=None):
    ms_settings = _get_ms_settings()
    authority = ms_settings["get_authority"](request)
    auth_app = msal.ConfidentialClientApplication(
        ms_settings["app_id"],
        authority=authority,
        client_credential=ms_settings["app_secret"],
        token_cache=cache,
    )
    return auth_app


def get_token(request):
    auth_app = _get_msal_app(request)
    flow = request.session.pop("auth_flow", {})
    try:
        result = auth_app.acquire_token_by_auth_code_flow(flow, request.GET)
    # returns ValueError if they haven't gone to Microsoft to get their auth_code_flow state
    # so they're either doing something sus or generally misusing the endpoints, return 400
    except ValueError:
        raise SuspiciousOperation()
    return result.get("access_token")


def get_auth_flow(request):
    ms_settings = _get_ms_settings()
    auth_app = _get_msal_app(request)
    flow = auth_app.initiate_auth_code_flow(
        ms_settings["scopes"],
        redirect_uri=ms_settings["get_redirect"](request),
    )
    request.session["auth_flow"] = flow
    return flow


def get_user_email(token):
    # Token cache does come with a user email, can we use that instead?
    r = requests.get(
        url="https://graph.microsoft.com/v1.0/me",
        headers={"Authorization": "Bearer {0}".format(token)},
    )
    user = r.json()
    return user.get("mail")
