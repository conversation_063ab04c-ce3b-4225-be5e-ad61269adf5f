"""Tests MSAL utility functions."""

from unittest import TestCase

from .. import sso_msal


class SsoMsalCase(TestCase):
    def test_ensure_localhost(self):
        cases = {
            "127.0.0.1": "localhost",
            "http://127.0.0.1": "http://localhost",
            "http://127.0.0.1/next/1": "http://localhost/next/1",
            "https://127.0.0.1": "https://127.0.0.1",
            "http://localhost": "http://localhost",
            "http://example.com": "http://example.com",
        }
        for url in cases:
            with self.subTest(url):
                self.assertEqual(sso_msal._ensure_localhost(url), cases[url])
