"""Tests for the helper functions in helpers.py"""

from django.test import RequestFactory, TestCase

from .. import helpers


class GetNextFromReqTestCase(TestCase):
    def setUp(self):
        self.request_factory = RequestFactory()

    def test_get(self):
        req = self.request_factory.get("/guest-email?next=/map/testmap")
        next_param = helpers.get_next_from_req(req)
        self.assertEqual(next_param, "/map/testmap")

    def test_post(self):
        req = self.request_factory.post(
            "/guest-email",
            {"next": "/map/testmap"},
        )
        next_param = helpers.get_next_from_req(req)
        self.assertEqual(next_param, "/map/testmap")

    def test_put_defaults(self):
        # only meant to be used with GET and POST
        # other methods should default to / without erroring
        req = self.request_factory.put(
            "/guest-email",
            {"next": "/map/testmap"},
        )
        next_param = helpers.get_next_from_req(req)
        self.assertEqual(next_param, "/")

    def test_get_no_next(self):
        req = self.request_factory.get("/guest-email")
        next_param = helpers.get_next_from_req(req)
        self.assertEqual(next_param, "/")

    def test_get_encoded(self):
        req = self.request_factory.get(
            "/guest-email?next=/map/testmap/_testview%3Fplottable%3Drelated%26a%3DLens",
        )
        next_param = helpers.get_next_from_req(req)
        self.assertEqual(
            next_param,
            "/map/testmap/_testview?plottable=related&a=Lens",
        )

    def test_get_double_encoded(self):
        # should never happen, but just in case...
        req = self.request_factory.get(
            "/guest-email?next=/map/testmap/_testview%253Fplottable%253Drelated%2526a%253DLens",
        )
        next_param = helpers.get_next_from_req(req)
        self.assertEqual(
            next_param,
            "/map/testmap/_testview?plottable=related&a=Lens",
        )
