"""Tests for Login redirection."""

from django.test import TestCase
from django.urls import reverse


class LoginTestCase(TestCase):
    """Tests for Login workflow."""

    def test_login_form_next_encoding(self):
        """Test login template does *not* urlencode next parameter"""
        # If somebody has been redirected to the login template their next param
        # will already be urlencoded. Urlencoding again via template filter tags
        # will break it on the POST. So check that we *don't* apply any additional
        # url encoding here.
        # TODO: This should be fixed as and when we do something a bit more sane
        # with urlencoding params in general.
        next_param = "/maps/testmap/testview%3Fplottable%3Dany%26a%3DDocument"
        url = reverse("login")
        response = self.client.get(url, {"next": next_param})
        self.assertEqual(response.status_code, 200)
        # really not sure about this, testing too much template content makes
        # the tests brittle
        expected_action = 'action="{}?next={}"'.format(url, next_param)
        expected_value = 'name="next" value="{}"'.format(next_param)
        self.assertContains(response, expected_action)
        self.assertContains(response, expected_value)
