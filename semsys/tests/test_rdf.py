"""Tests for basic namespace/iri handling."""

from unittest import TestCase

from .. import rdf


class RdfCase(TestCase):
    def test_from_qname(self):
        cases = {
            "http://ns.test/": "http://ns.test/",
            "rdf:type": rdf.IRIS["RDF"]["type"],
            "rdfs:comment": rdf.IRIS["RDFS"]["comment"],
            "vm:Stakeholder": rdf.IRIS["VM"]["stakeholder"],
            "vm:_a_map": rdf.PREFIXES["vm"] + "_a_map",
        }
        for qname in cases:
            with self.subTest(qname):
                self.assertEqual(rdf.from_qname(qname), cases[qname])
