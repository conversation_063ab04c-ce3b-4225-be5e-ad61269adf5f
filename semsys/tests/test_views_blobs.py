"""Tests for Azure blobstore proxying.

Could do via mocking at the azure library level or the http request level but
for the moment really make blobstore requests as part of the testing.
"""

from unittest import skipIf

from django.conf import settings
from django.test import SimpleTestCase


def _has_blobproxy():
    return bool(settings.BLOBPROXY_CONN_STR and settings.BLOBPROXY_CONTAINER)


async def read_content(response):
    """Get content bytes out of a response.

    Should not really be needed but the test client exposes these details?
    """
    stream = response.streaming_content
    return b"".join([chunk async for chunk in stream])


@skipIf(not _has_blobproxy(), "no backing blobstore")
class BlobGetCase(SimpleTestCase):
    async def test_get(self):
        res = await self.async_client.get("/blobs/test.txt")
        self.assertEqual(res.status_code, 200)
        self.assertEqual(res.headers["Content-Type"], "text/plain")
        self.assertEqual(await read_content(res), b"Some content\n")

    async def test_missing(self):
        res = await self.async_client.get("/blobs/nothere")
        self.assertEqual(res.status_code, 404)
        self.assertEqual(res.json(), {"error": "The specified blob does not exist."})

    async def test_conditional(self):
        path = "/blobs/test.txt"
        res = await self.async_client.get(path, IF_NONE_MATCH='W/"fictional"')
        self.assertEqual(res.status_code, 200)
        self.assertIn("E-Tag", res.headers)
        etag = res.headers["E-Tag"]
        res = await self.async_client.get(path, IF_NONE_MATCH=etag)
        self.assertEqual(res.status_code, 304)
        self.assertEqual(res.headers["Content-Length"], "0")

    async def test_head(self):
        res = await self.async_client.head("/blobs/test.txt")
        self.assertEqual(res.status_code, 200)
        self.assertEqual(await read_content(res), b"")

    async def test_no_post(self):
        res = await self.async_client.post("/blobs/new", {})
        self.assertEqual(res.status_code, 405)
        self.assertEqual(res.headers["Allow"], "GET, HEAD")

    async def test_no_put(self):
        res = await self.async_client.put("/blobs/new", b"")
        self.assertEqual(res.status_code, 405)
        self.assertEqual(res.headers["Allow"], "GET, HEAD")
