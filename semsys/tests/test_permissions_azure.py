"""Tests for Azure specific permissions functions.

These are used by various view methods to present a uniform interface with the
default permissions logic using Django users and guest email access, but all
authentication logic is delegated to easyauth.
"""

import base64
import json

from django.test import RequestFactory, TestCase, override_settings

from .. import (
    models,
    permissions_azure,
)


def _generate_claims(*claims):
    return base64.b64encode(json.dumps({"claims": claims}).encode("utf-8"))


class StubUser:
    is_superuser = False


@override_settings(AZURE_REQUIRED_ROLES="User")
class RolesCase(TestCase):
    """Base test case for functions requiring role access."""

    request_factory = RequestFactory()

    def make_request(self, path="/example", **kwargs):
        request = self.request_factory.get(path, **kwargs)
        request.user = StubUser()
        return request


class UserCanViewMapCase(RolesCase):
    """Tests for permissions for map access."""

    def test_no_header(self):
        req = self.make_request()
        with self.assertLogs("semsys") as logger:
            access = permissions_azure.user_map_access(req, None)
        self.assertRegex(
            "\n".join(logger.output),
            r"^WARNING:.*:azure claims not present$",
        )
        self.assertEqual(access, "none")

    def test_no_role(self):
        req = self.make_request(
            headers={"X-MS-Client-Principal": _generate_claims()},
        )
        with self.assertNoLogs("semsys"):
            with self.settings(AZURE_REQUIRED_ROLES=""):
                access = permissions_azure.user_map_access(req, None)
        self.assertEqual(access, "read")

    def test_role(self):
        req = self.make_request(
            headers={
                "X-MS-Client-Principal": _generate_claims(
                    {"typ": "name", "val": "A. User"}, {"typ": "roles", "val": "User"}
                ),
            },
        )
        with self.assertNoLogs("semsys"):
            access = permissions_azure.user_map_access(req, None)
        self.assertEqual(access, "read")

    def test_role_missing(self):
        cred = _generate_claims(
            {"typ": "name", "val": "A. User"}, {"typ": "roles", "val": "Minion"}
        )
        req = self.make_request(
            headers={"X-MS-Client-Principal": cred},
        )
        with self.assertLogs("semsys") as logger:
            access = permissions_azure.user_map_access(req, None)
        self.assertRegex(
            "\n".join(logger.output),
            r"^WARNING:.*:azure role missing: 'User' not in {'Minion'}$",
        )
        self.assertEqual(access, "none")

    def test_bad_base64_length(self):
        req = self.make_request(headers={"X-MS-Client-Principal": "AA"})
        with self.assertLogs("semsys") as logger:
            access = permissions_azure.user_map_access(req, None)
        self.assertRegex(
            "\n".join(logger.output),
            r"^WARNING:.*:azure claims decode fail: Incorrect padding$",
        )
        self.assertEqual(access, "none")

    def test_bad_base64_alpha(self):
        req = self.make_request(headers={"X-MS-Client-Principal": "X!"})
        with self.assertLogs("semsys") as logger:
            access = permissions_azure.user_map_access(req, None)
        self.assertRegex(
            "\n".join(logger.output),
            r"^WARNING:.*:azure claims decode fail: Invalid base64.*$",
        )
        self.assertEqual(access, "none")

    def test_bad_json(self):
        req = self.make_request(
            headers={"X-MS-Client-Principal": base64.b64encode(b"{")}
        )
        with self.assertLogs("semsys") as logger:
            access = permissions_azure.user_map_access(req, None)
        self.assertRegex(
            "\n".join(logger.output),
            r"^WARNING:.*:azure claims decode fail: Expecting.*$",
        )
        self.assertEqual(access, "none")

    def test_bad_object(self):
        req = self.make_request(
            headers={"X-MS-Client-Principal": base64.b64encode(b"{}")}
        )
        with self.assertLogs("semsys") as logger:
            access = permissions_azure.user_map_access(req, None)
        self.assertRegex(
            "\n".join(logger.output),
            r"^WARNING:.*:azure claims bad format: 'claims'$",
        )
        self.assertEqual(access, "none")

    def test_bad_list(self):
        req = self.make_request(
            headers={"X-MS-Client-Principal": base64.b64encode(b'{"claims": 1.0}')},
        )
        with self.assertLogs("semsys") as logger:
            access = permissions_azure.user_map_access(req, None)
        self.assertRegex(
            "\n".join(logger.output),
            r"^WARNING:.*:azure claims bad format: .*not iterable$",
        )
        self.assertEqual(access, "none")


class GetMapsCase(RolesCase):
    """Tests for what maps are returned as a listing."""

    def setUp(self):
        """Prepare some fake model data for use with the tests."""
        super().setUp()
        # TODO: Models should use auto_now_add
        when = "2024-01-31"
        project = models.Project.objects.create(
            iri="http://semsys.test/a-proj",
            created_on=when,
        )
        models.Map.objects.create(
            iri="http://semsys.test/map-draft",
            created_on=when,
            voting_open=False,
            project_id=project.iri,
            public=False,
        )
        models.Map.objects.create(
            iri="http://semsys.test/map-final",
            created_on=when,
            voting_open=False,
            project_id=project.iri,
            public=True,
        )

    @staticmethod
    def get_maps(req):
        return list(
            permissions_azure.get_map_queryset(req).values_list("iri", flat=True)
        )

    def test_without_role(self):
        req = self.make_request(
            headers={"X-MS-Client-Principal": _generate_claims()},
        )
        with self.assertLogs("semsys") as logger:
            maps = self.get_maps(req)
        self.assertRegex(
            "\n".join(logger.output),
            r"^WARNING:.*:azure role missing: 'User' not in set\(\)$",
        )
        self.assertEqual([], maps)

    def test_right_role(self):
        req = self.make_request(
            headers={
                "X-MS-Client-Principal": _generate_claims(
                    {"typ": "roles", "val": "User"}
                ),
            },
        )
        with self.assertNoLogs("semsys"):
            maps = self.get_maps(req)
        self.assertEqual(["http://semsys.test/map-final"], maps)
