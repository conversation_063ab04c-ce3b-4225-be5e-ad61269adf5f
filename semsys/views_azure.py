# Copyright 2022 Visual Meaning Ltd

"""
Views specific to the Azure deployment of the SMP.
"""
from django import db, http
import django.views.decorators.http as http_decorators


@http_decorators.require_http_methods(["GET"])
def get_instance_health(request):
    """Pinged by App Service Health Check. Tests that the instance is up and that it can connect to a DB."""
    # this function throws an exception if there's no connection, which should result in a 500 response
    db.connection.ensure_connection()
    return http.HttpResponse("OK", status=200)


@http_decorators.require_http_methods(["GET"])
def explode(request):
    raise AttributeError("explode!")
