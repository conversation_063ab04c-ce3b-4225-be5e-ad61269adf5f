# Copyright 2022 Visual Meaning Ltd

from django.conf import settings
from django.http import Http404
from django.contrib.sites.shortcuts import get_current_site
from django.core.exceptions import PermissionDenied

from .helpers import redirect_with_parameters, get_site_with_override
from .models import Map, Project, Site
from .views_frontend import safe_render
from .shortercuts import get_object_or_None


ME_PATH = "/api/user"


def _user_map_access_detailed(request, target_map):
    """
    Determine what access request user has on target map and what kind of user.

    Access levels: 'full', 'read', 'none'
    User kinds: 'super', 'login', 'guest', 'anon'
    """
    project = target_map.project
    user = request.user

    if user.is_superuser:
        return "full", "super"

    if user.is_authenticated:
        user_info = "login"
        if user.groups.filter(name=project.name).exists():
            return "full", user_info
        email = user.email
    else:
        email = request.session.get("email")
        user_info = "guest" if email else "anon"

    if target_map.public or email_whitelisting(project.whitelist, email):
        return "read", user_info

    return "none", user_info


def user_map_access(request, target_map):
    """
    Gives level of access request user has on target map.

    One of 'full' for edit and administrative level, 'read' for viewing only,
    or 'none' for no access.
    """
    return _user_map_access_detailed(request, target_map)[0]


def get_email_from_req(req):
    return req.user.email if req.user.is_authenticated else req.session.get("email")


def _group_maps(req):
    if req.user.is_anonymous:
        return Map.objects.none()
    groups = req.user.groups.values("name")
    return Map.objects.filter(project__name__in=groups)


def _whitelist_maps(req):
    email = get_email_from_req(req)
    if not email:
        return Map.objects.none()
    projects = [
        p.iri for p in Project.objects.all() if email_whitelisting(p.whitelist, email)
    ]
    return Map.objects.filter(project__iri__in=projects)


def _public_maps():
    return Map.objects.filter(public=True)


def _get_map_queryset(req):
    """Return a queryset with all maps a user is allowed to access."""
    # return all maps if superuser, unless site override is set - if so, we
    # probably want to see the normal list of maps for testing purposes
    curr_site = get_site_with_override(req)
    if req.user.is_superuser and curr_site.name == get_current_site(req).name:
        maps = Map.objects.all()
    else:
        # Start with public maps
        # Add maps that a user has access to via group membership
        # Add maps that a user has access to via email whitelisting
        maps = _public_maps() | _group_maps(req) | _whitelist_maps(req)
        # remove maps that are not part of the domain on the incoming request
        if curr_site.name not in settings.DEV_SITES:
            site = get_object_or_None(Site, name=curr_site.name)
            maps = maps.filter(project__related_site=site)
    return maps


def _debug_map_queryset(req, maps):
    """Unpack map queryset and label with debug parameters."""
    # this needs to be kept up to date with any additions/removals to access methods
    # in get_map_queryset
    access = {
        "public": [m.iri for m in _public_maps()],
        "group": [m.iri for m in _group_maps(req)],
        "whitelist": [m.iri for m in _whitelist_maps(req)],
    }
    for m in maps:
        m.access = []
        if req.user.is_superuser:
            m.access.append("superuser")
        for key in access:
            if m.iri in access[key]:
                m.access.append(key)
    return maps


def get_map_queryset(req):
    """Return an iterable of map objects for the map index page"""
    dev_view = req.GET.get("dev_view")
    maps = _get_map_queryset(req)
    if dev_view:
        maps = _debug_map_queryset(req, maps)
    return maps


##  ----------------------------------------------------------------------
##  Drawing guest maps
##    'Guest map key' cookies are set on first successful login, tied to
##    the path of the guest map they correspond to.
##    If you change the path to a map its old cookies will linger, which
##    shouldn't really matter.


def email_whitelisting(whitelist, email):
    """Check an email against the whitelist for that project."""
    if not email or not whitelist:
        return False
    # force to lowercase as the email form does allow uppercase characters
    email = email.lower()
    # Django otherwise validates email input so shouldn't need anything more sophisticated
    # than this for domain
    email_domain = email.split("@")[1]
    whitelist = whitelist.splitlines()
    # check domain and email against whitelist
    domain_cond = email_domain in {d.strip().lower() for d in whitelist if "@" not in d}
    email_cond = email in {e.strip().lower() for e in whitelist if "@" in e}
    return domain_cond or email_cond


def showTemplate(req, map, template_name, context):
    """Check user access rights for a map and render an HTML template containing map data if successful."""
    project = map.project

    # Site check: is the user allowed to access this map on this domain? If not, return 404.
    # TODO: This should be aligned with the logic in get_map_queryset, however it's difficult to
    # write common code and using get_map_queryset to look up this map in the list of user's allowed
    # maps is 4 times as slow.
    site = get_site_with_override(req)
    site_ok = (
        # Superusers may access all maps from any domain and this overrides all other conditions.
        req.user.is_superuser
        # If the site is a dev Site such as staging or localhost, also allow access to all maps.
        or site.name in settings.DEV_SITES
        # Otherwise, check that the domain associated with the map's project is the same as the domain
        # of the incoming request.
        or (project.related_site and project.related_site.name == site.name)
    )
    if not site_ok:
        raise Http404()

    perms, user_kind = _user_map_access_detailed(req, map)

    if perms in ("full", "read"):
        return safe_render(req, template_name, context)

    # if the map has no whitelist it can only be accessed with a user account, so if they're
    # not logged in redirect them to the login screen
    if user_kind in ("guest", "anon") and not project.whitelist:
        return redirect_with_parameters(
            "login", {"next": context.get("base_path") + context.get("next_path")}
        )

    # if no identity info, at least get their email before redirecting back here
    if user_kind == "anon" and project.whitelist:
        return redirect_with_parameters(
            "signin", {"next": context.get("base_path") + context.get("next_path")}
        )

    # if they haven't satisfied any of the access conditions then they can't get in
    raise PermissionDenied()
