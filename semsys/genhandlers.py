## HTTP request handlers for cross-app API

from django.http import HttpResponse
import hashlib
from uuid import uuid4

from .models import ContentItem


##  ======================================================================
##  == Content Items


def contentItem(req):
    """GET/PUT an item of content in the DB."""

    if req.method == "GET":
        return contentItem_get(req)
    elif req.method == "POST":
        return contentItem_post(req)
    else:
        raise RuntimeError("Bad request method")


def contentItem_get(req):
    """Get a named piece of content from the db.
    Query string must include:
      - uuid : String"""

    uu = req.GET.get("uuid")
    if uu is None:
        return HttpResponse("Need content item uuid", status=404)

    content = ContentItem.objects.filter(uuid=uu).first()
    if content is not None:
        res = HttpResponse(bytes(content.data), content_type="application/octet-stream")
        # TODO increase the cache time length when our code settles down
        #      or handle in caching decorator
        res["Cache-Control"] = "private, max-age=" + str(10 * 60)
        return res
    else:
        return HttpResponse("No such content item", status=404)


def contentItem_post(req):
    """POST a named piece of content in the db, based on html formdata with
    a filepicker in it.

    Form file should be named 'file-to-insert'

    If the item is already stored, a ref to the original will be returned
    and no copy created."""

    file = req.FILES.get("file-to-insert")
    if file is None:
        return HttpResponse("Provide file to insert", status=400)
    if file.size > (50 * 1024 * 1024):  ## max file size 50MB TODO consider process
        return HttpResponse("File too large", status=400)

    data = file.read()
    sha1 = hashlib.sha1(data).digest()

    item = ContentItem.objects.filter(data_sha1=sha1).first()
    if item is not None:
        ## Item already stored, return its uuid
        return HttpResponse(str(item.uuid))
    else:
        ## New item, insert and return uuid
        uuid = uuid4()
        newItem = ContentItem(data=data, data_sha1=sha1, uuid=uuid)
        newItem.save()
        ## TODO poss run gc for data store here, maybe
        return HttpResponse(str(uuid))
