# Generated by Django 4.1.5 on 2023-01-19 10:46

from django.db import migrations, models
import django.db.models.deletion


# create a default empty LensTermsBag for all existing maps
def forwards_func(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    Map = apps.get_model('semsys', 'Map')
    LensTermsBag = apps.get_model('semsys', 'LensTermsBag')
    for m in Map.objects.using(db_alias).all():
        ltb, created = LensTermsBag.objects.using(db_alias).get_or_create(map=m, terms=[])
        ltb.save()


# shouldn't need a reverse func? we just want to delete all LensTermsBags if we undo
# which should be covered by Django deleting the table when it deletes the model
def reverse_func(apps, schema_editor):
    pass


class Migration(migrations.Migration):

    dependencies = [
        ("semsys", "0009_site_remove_project_site_project_related_site"),
    ]

    operations = [
        migrations.CreateModel(
            name="LensTermsBag",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("terms", models.JSONField()),
                (
                    "map",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="semsys.map"
                    ),
                ),
            ],
            options={
                "get_latest_by": "created_on",
            },
        ),
        migrations.RunPython(forwards_func, reverse_func),
    ]
