# Generated by Django 3.1.5 on 2021-01-25 17:35

from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.Group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.Permission', verbose_name='user permissions')),
            ],
            options={
                'db_table': 'auth_user',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='ContentItem',
            fields=[
                ('uuid', models.UUIDField(primary_key=True, serialize=False)),
                ('data', models.BinaryField()),
                ('data_sha1', models.BinaryField(unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='Map',
            fields=[
                ('iri', models.TextField(primary_key=True, serialize=False)),
                ('name', models.TextField()),
                ('description', models.TextField()),
                ('created_on', models.DateField()),
                ('voting_open', models.BooleanField()),
                ('tiles_src', models.TextField()),
                ('guest_map_path', models.CharField(blank=True, max_length=96, null=True, unique=True)),
                ('guest_map_pw', models.CharField(blank=True, max_length=96)),
                ('voting_map_path', models.CharField(blank=True, max_length=96, null=True, unique=True)),
                ('welcome_splash', models.TextField(blank=True)),
                ('voting_splash', models.TextField(blank=True)),
                ('terms_updated_on', models.DateTimeField(null=True)),
                ('terms_updated_uuid', models.UUIDField(null=True)),
                ('map_zoom_classes_json', models.TextField(blank=True)),
                ('leaflet_map_settings_json', models.TextField(blank=True)),
                ('map_extra_css', models.TextField(blank=True)),
            ],
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('iri', models.TextField(primary_key=True, serialize=False)),
                ('name', models.TextField()),
                ('description', models.TextField()),
                ('created_on', models.DateField()),
                ('takes_login', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='PurposeBag',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('name', models.TextField()),
                ('terms', models.JSONField()),
            ],
        ),
        migrations.CreateModel(
            name='UserMapAnnotationBag',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('terms', models.JSONField()),
                ('user_stub', models.TextField()),
                ('map', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='semsys.map')),
            ],
        ),
        migrations.CreateModel(
            name='MapTermsBag',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('terms', models.JSONField()),
                ('map', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='semsys.map')),
            ],
        ),
        migrations.AddField(
            model_name='map',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='semsys.project'),
        ),
        migrations.AddField(
            model_name='map',
            name='terms_updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='UserMapEditPermission',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('map', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='semsys.map')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'map')},
            },
        ),
        migrations.CreateModel(
            name='MapPainpointVote',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('painpoint_iri', models.TextField()),
                ('voter_iri', models.TextField()),
                ('score', models.IntegerField()),
                ('map', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='semsys.map')),
            ],
            options={
                'unique_together': {('map', 'painpoint_iri', 'voter_iri')},
            },
        ),
        migrations.AddConstraint(
            model_name='map',
            constraint=models.CheckConstraint(check=models.Q(('guest_map_path', None), ('guest_map_path__endswith', '/'), _connector='OR'), name='guest_map_path_empty_or_ends_with_slash'),
        ),
        migrations.AddConstraint(
            model_name='map',
            constraint=models.CheckConstraint(check=models.Q(('voting_map_path', None), ('voting_map_path__endswith', '/'), _connector='OR'), name='voting_map_path_empty_or_ends_with_slash'),
        ),
    ]
