# Generated by Django 5.1 on 2024-08-23 15:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('semsys', '0013_map_loading_src'),
    ]

    operations = [
        migrations.AddField(
            model_name='site',
            name='guest_signin',
            field=models.BooleanField(default=True, help_text='Allows Guest Sign In via email admission.'),
        ),
        migrations.AddField(
            model_name='site',
            name='microsoft_tenant_id',
            field=models.CharField(blank=True, help_text='Microsoft Entra Tenant ID of Organisation. Used as an endpoint for Microsoft SSO sign in.', max_length=36),
        ),
        migrations.AddField(
            model_name='site',
            name='sso_signin',
            field=models.BooleanField(default=False, help_text='Allows access via SSO sign in.'),
        ),
    ]
