# Generated by Django 4.0 on 2022-10-24 18:24

from django.db import migrations, models
import django.db.models.deletion


def forwards_func(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    Project = apps.get_model('semsys', 'Project')
    Site = apps.get_model('semsys', 'Site')
    for proj in Project.objects.using(db_alias).all():
        if proj.site:
            site, created = Site.objects.using(db_alias).get_or_create(name=proj.site)
            proj.related_site = site
            proj.save()


def reverse_func(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    Project = apps.get_model('semsys', 'Project')
    for proj in Project.objects.using(db_alias).all():
        if proj.related_site:
            proj.site = proj.related_site.name
            proj.save()


class Migration(migrations.Migration):

    dependencies = [
        ('semsys', '0008_alter_map_terms_updated_on'),
    ]

    operations = [
        migrations.CreateModel(
            name='Site',
            fields=[
                ('name', models.TextField(primary_key=True, serialize=False)),
                ('custom_css', models.TextField(blank=True, help_text='Custom branding that will be applied to site base pages such as maps index, login etc.')),
                ('favicon', models.TextField(blank=True, help_text='A favicon PNG converted to a base64 text string.')),
            ],
        ),
        migrations.AddField(
            model_name='project',
            name='related_site',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='semsys.site'),
        ),
        migrations.RunPython(forwards_func, reverse_func),
        migrations.RemoveField(
            model_name='project',
            name='site',
        ),
    ]
