# Generated by Django 4.1.5 on 2023-01-19 10:47

from django.db import migrations


def forwards_func(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    Map = apps.get_model('semsys', 'Map')
    LensTermsBag = apps.get_model('semsys', 'LensTermsBag')
    for m in Map.objects.using(db_alias).all():
        if m.leaflet_map_settings_json:
            ltb, created = LensTermsBag.objects.using(db_alias).get_or_create(map=m)
            ltb.terms.append(
                {
                    "subj": m.iri,
                    "pred": "http://visual-meaning.com/rdf/leafletMapSettings",
                    "obj": m.leaflet_map_settings_json,
                }
            )
            ltb.save()


def reverse_func(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    Map = apps.get_model('semsys', 'Map')
    LensTermsBag = apps.get_model('semsys', 'LensTermsBag')
    for m in Map.objects.using(db_alias).all():
        ltb, created = LensTermsBag.objects.using(db_alias).get_or_create(map=m)
        if not created:
            for index, triple in enumerate(ltb.terms):
                if triple['pred'] == "http://visual-meaning.com/rdf/leafletMapSettings":
                    m.leaflet_map_settings_json = triple['obj']
                    ltb.terms.pop(index)
            m.save()
            ltb.save()


class Migration(migrations.Migration):

    dependencies = [
        ("semsys", "0010_lenstermsbag"),
    ]

    operations = [
        migrations.RunPython(forwards_func, reverse_func),
        migrations.RemoveField(
            model_name="map",
            name="leaflet_map_settings_json",
        ),
    ]
