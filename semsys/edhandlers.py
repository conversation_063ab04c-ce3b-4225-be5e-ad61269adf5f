## HTTP request handlers for modeleditor

import json
from uuid import uuid4

from django.contrib.auth.decorators import login_required
from django.db import transaction
from django.http import JsonResponse
from django.views.decorators.gzip import gzip_page
from django.views.decorators.http import require_http_methods

from django_simple_task import defer

from .custom_decorators import load_map_from_iri, resolve_map
from .forms import MapAttrsForm
from .helpers import log_to_django_admin_table
from .json import load_json_body
from .rdf import IRIS
from .models import Map, MapTermsBag, MapPainpointVote, Project
from .shortercuts import get_latest_or_None
from .terms_response import result_including_terms, cache_compressed_terms


##  ======================================================================
##  == 'All projects and maps' summary


@login_required
def projectsAndMaps(req):
    """Returns JSON for top level details for all projects and project
    maps in the system. Structure:
    { projects: [
        { name:        String,
          description: String,
          created-on:  String (date, YYYY-MM-DD),  // FIXME consider timezones
          iri:         String,
          maps: [{
              name:        String,
              description: String,
              iri:         String,
              created-on:  String (date, YYYY-MM-DD),  // FIXME consider timezones
              voting-open: Bool,
              project-iri: String,
              count-stakeholders: Int,
              count-painpoints:   Int,
          }...]
       }...]
    }"""

    ## Which is the user allowed to see and edit?
    groups = [group["name"] for group in req.user.groups.values()]
    visibleProjs = Project.objects.filter(name__in=groups)
    visibleMaps = Map.objects.filter(project__name__in=groups)

    ## Fetchers for extra data in the db
    def stakeholderTermsForMap(map):
        bag = get_latest_or_None(MapTermsBag.objects.filter(map=map))
        return (
            list(
                filter(
                    lambda t: t["pred"] == IRIS["RDF"]["type"]
                    and t["obj"] == IRIS["VM"]["stakeholder"],
                    bag.terms,
                )
            )
            if bag
            else []
        )

    def painpointTermsForMap(map):
        bag = get_latest_or_None(MapTermsBag.objects.filter(map=map))
        return (
            list(
                filter(
                    lambda t: t["pred"] == IRIS["RDF"]["type"]
                    and t["obj"] == IRIS["VM"]["painpoint"],
                    bag.terms,
                )
            )
            if bag
            else []
        )

    ## Making json-ready dicts for db entries, taking into account visibleMaps
    def makeMapData(map):
        assert map in visibleMaps
        shTerms = stakeholderTermsForMap(map)
        ppTerms = painpointTermsForMap(map)
        return {
            "name": map.name,
            "description": map.description,
            "iri": map.iri,
            "voting-open": map.voting_open,
            "project-iri": map.project.iri,
            "created-on": map.created_on.isoformat(),
            "count-stakeholders": len(shTerms),
            "count-painpoints": len(ppTerms),
        }

    def makeProjectData(project):
        assert project in visibleProjs
        maps = [m for m in Map.objects.filter(project=project) if m in visibleMaps]
        return {
            "name": project.name,
            "description": project.description,
            "created-on": project.created_on.isoformat(),
            "iri": project.iri,
            "maps": [makeMapData(m) for m in maps],
        }

    ## Top level assembly
    return JsonResponse({"projects": [makeProjectData(p) for p in visibleProjs]})


##  ======================================================================
##  == GET/PUT Maps' RDF models
##  ==   We also bundle in some styling data necessary for drawing the
##  ==   map correctly


@login_required
@require_http_methods(["GET", "PUT", "POST"])
def mapModel(request):
    if request.method == "GET":
        return mapModel_get(request)
    elif request.method in ("PUT", "POST"):
        return mapModel_put_post(request)


@load_map_from_iri(read_only=True)
@gzip_page
@login_required
@require_http_methods(["GET"])
def mapModel_get(request, map):
    """Request must have 'iri' in querystring, corresponding to DB map iri.

    Returns the RDF model for one map, merging the Django model Map and
    MapTermsBag, in the following JSON structure:
    {
        iri:           String (map IRI)
        updated-uuid:  String|null (generated each model write)
        updated-email: String|null (from stored last writing User)
        updated-date:  String|null (iso 8601)
        tiles-src:     String (leaflet-friendly tile source string)
        terms: [
            {subj:String, pred:String, obj:String}
            ...
        ]
        map-extra-css: String
        map-zoom-classes: [{name:String, min:Int, max:Int}...]
    }

    TODO handle errors more cleanly (qs, db map get)"""
    return result_including_terms(
        {
            "iri": map.iri,
            "updated-uuid": map.terms_updated_uuid,
            "updated-email": (
                map.terms_updated_by.email if map.terms_updated_by else None
            ),
            "updated-date": (
                map.terms_updated_on.isoformat() if map.terms_updated_on else None
            ),
            "tiles-src": map.tiles_src,
            "terms": "__PLACEHOLDER",
            "map-extra-css": map.map_extra_css,
            "map-zoom-classes": (
                json.loads(map.map_zoom_classes_json)
                if len(map.map_zoom_classes_json)
                else []
            ),
        },
        map,
    )


@login_required
@require_http_methods(["PUT", "POST"])
# TODO: turn non-ok returns into exceptions (perhaps with an outer wrapper for
# this function) so the transaction doesn't commit.
# We get away with this for now because all the save() calls come at the end.
def mapModel_put_post(request):
    """Request must have 'iri' in querystring, corresponding to DB map iri.

    PUT body must be json of format:
    {
        'last-updated-uuid': String  // what was the uuid of the saved model you read?
        'terms': [ {iri:String, pred:String, obj:String}... ]
    }

    Alternatively, update the Django Map object by passing fields as keys:
    {
        'name': String
        'description': String
        ...
    }

    See current Map model definition in models.py for allowed field names.

    The keys for terms update + Map object fields update may be combined into a single
    json body to update both at once.

    If the save succeeds, replies with http 200 and body with following json:
    {
        'updated-uuid': String  // the new uuid of this model write
    }
    If the save fails due to conflict, http 409 is returned and body is as for
    mapModel_get().
    If the save fails due to other reason some other http error is returned.
    TODO more docs
    """
    should_amend = "amend" in request.GET
    js, err = load_json_body(request)
    if err:
        return err
    triples = js.pop("terms", None)
    luUuid = js.pop("last-updated-uuid", "")
    with transaction.atomic():
        map, error_response = resolve_map(
            request, Map.objects.select_for_update(), read_only=False
        )
        if error_response is not None:
            # Could not load map due to parameter/model/permission error
            return error_response

        ## Bail if the client's view of the rdf model is out of date
        curUuid = map.terms_updated_uuid
        if str(luUuid) != str(curUuid):
            return JsonResponse(
                dict(
                    request=dict(terms_updated_uuid=luUuid),
                    server=dict(terms_updated_uuid=curUuid),
                ),
                status=409,
            )

        form = MapAttrsForm(data=js, instance=map)
        if not form.is_valid():
            return JsonResponse(dict(error=form.errors.as_json()), status=400)
        # The map instance has now been modified, using .save() below to commit

        if triples:
            # Remove old terms bags, retaining two latest, affected by 'amend' flag
            bags = MapTermsBag.objects.select_for_update().filter(map=map)
            # Has to be done as two-step query as DELETE doesn't work with LIMIT/OFFSET
            last = get_latest_or_None(bags.values("created_on"), "created_on")
            if last:
                filter_or_exclude = bags.filter if should_amend else bags.exclude
                filter_or_exclude(created_on=last["created_on"]).delete()

            map_terms = []
            terms = []
            for t in triples:
                dict_t = dict(subj=t["subj"], pred=t["pred"], obj=t["obj"])
                terms.append(dict_t)
                ## Filter out leafletMapSettings from MapTermsBag as they live in LensTermsBag
                ## Should add new api to update leafletMapSettings if changed with map model reupload
                if t["pred"] != "http://visual-meaning.com/rdf/leafletMapSettings":
                    map_terms.append(dict_t)

            new_bag = MapTermsBag.objects.create(map=map, terms=map_terms)

            # Rebuild terms cache with all map terms.
            defer(cache_compressed_terms, {"args": [map.iri, map.all_terms_as_string]})

            # Log new model creation to Django admin table
            log_to_django_admin_table(
                "create",
                request,
                new_bag,
                "Created new MapTermsBag via API | {}".format(map.iri),
            )

        ## Map stores deatils of last update
        map.terms_updated_uuid = uuid4()
        map.terms_updated_by = request.user
        map.save()

        # If we updated map fields, log change to admin table so it shows up in history
        if form.has_changed():
            log_to_django_admin_table(
                "change",
                request,
                map,
                "Map fields changed via API: {}".format(", ".join(form.changed_data)),
            )

    return JsonResponse({"updated-uuid": map.terms_updated_uuid})


##  ======================================================================
##  == All votes cast for a given map


@load_map_from_iri(read_only=True)
@login_required
@require_http_methods(["GET"])
def mapVotes(req, map):
    """GET all votes cast by all users for a given map.
    Querystring must have 'iri:String' (giving map iri).
    Response is JSON, grouped by voter, with format:
    {
        voterVotes: [{
                         voter-iri: String,
                         votes: [{painpoint-iri: String,
                                  score:         Int}...]
                     }...]
    }"""
    ## Get votes data from the db
    votes = MapPainpointVote.objects.filter(map=map)

    ## One part of the JSON response
    def voterData(voter):
        voterVotes = [v for v in votes if v.voter_iri == voter]
        return {
            "voter-iri": voter,
            "votes": [
                {"painpoint-iri": v.painpoint_iri, "score": v.score} for v in voterVotes
            ],
        }

    ## Assemble the whole response
    allVoters = set([v.voter_iri for v in votes])
    res = [voterData(voter) for voter in allVoters]

    return JsonResponse({"voterVotes": res})
