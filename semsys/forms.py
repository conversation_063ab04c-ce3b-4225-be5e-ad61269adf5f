# Copyright 2020 Visual Meaning Ltd

from django import forms

from . import models


class AnonymousEmailForm(forms.Form):
    email = forms.EmailField(max_length=150)


class MapAttrsForm(forms.ModelForm):
    """Form for updates to some changable fields only of existing maps.

    Used for legacy map editing that can be done without Django admin.
    """

    class Meta:
        model = models.Map
        # Many of these fields are redundant or at least poorly named, so
        # exposing them to an external api is a bad idea, but migrating to
        # better interfaces is quite a bit of work.
        fields = [
            # name and description are required and probably fine here
            # but could also get derived from triples
            "name",
            "description",
            # public is probably fine? though may be better at project level.
            "public",
            # any voting should be activities on the map, not a flag
            "voting_open",
            # tiles_src should be derived automatically from lens triples
            "tiles_src",
            # loading_src should instead be auto-generated and in lens triples
            "loading_src",
            # paths should be auto-defined from a slug instead
            "guest_map_path",
            "voting_map_path",
            # content should be defined in triples (and not be splash)
            "welcome_splash",
            "voting_splash",
            # map_zoom_classes_json is a hack that should be removed
            "map_zoom_classes_json",
            # map_extra_css is a hack but a useful one that probably stays
            "map_extra_css",
        ]

    def __init__(self, *args, **kwargs):
        super(MapAttrsForm, self).__init__(*args, **kwargs)
        # We want an empty key to be treated as the _current_ value, not a
        # default value, which differs from the standard form handling.
        self.data = dict(self.initial, **self.data)
