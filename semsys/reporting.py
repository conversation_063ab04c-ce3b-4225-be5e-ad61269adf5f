# Copyright 2024 Visual Meaning Ltd

"""
A set of endpoints that provide reporting data.
"""

from datetime import timedelta
from django import http
from django.contrib.admin.models import LogEntry
from django.contrib.auth.decorators import login_required
from django.contrib.contenttypes.models import ContentType

# passthrough of SQL NOW() statement to model query
from django.db.models.functions import Now

from .users import User

# TODO: Refactor the annotations report endpoint and move it in here?
# TODO: Write a new helper function that returns .csvs in a vaguely sane way.
from .helpers import ACTION_FLAGS, excel_response


def _get_model_upload_logs(days):
    ctype = ContentType.objects.get(model="maptermsbag")
    logs = LogEntry.objects.filter(
        action_time__gte=Now() - timedelta(days=days),
        # Note that we will get all model creation events, including via the admin interface.
        # We'll disambiguate by including the creation message in our output.
        action_flag=ACTION_FLAGS["create"],
        content_type_id=ctype.pk,
    )
    # odd spelling - need to yield list of column headers first for excel_response function
    yield ["upload_date", "upload_time", "username", "message"]
    for log in logs:
        yield {
            "upload_date": log.action_time.strftime("%Y-%m-%d"),
            "upload_time": log.action_time.strftime("%H:%M:%S"),
            # users should never be deleted so this is safe
            "username": User.objects.get(id=log.user_id).username,
            "message": log.change_message,
        }


@login_required
def get_model_upload_logs(req):
    """Return a .csv containing map model upload logs for the last X days."""
    if not req.user.is_superuser:
        return http.JsonResponse(dict(error="unauthorized"), status=401)
    try:
        days = int(req.GET.get("days", 30))
    except ValueError as e:
        return http.JsonResponse(dict(error=str(e)), status=400)
    return excel_response(
        "mapmodel_upload_logs.csv",
        _get_model_upload_logs(days),
    )
