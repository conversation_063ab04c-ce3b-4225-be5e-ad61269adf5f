FROM ubuntu:22.04 as build
RUN apt-get update \
    && apt-get dist-upgrade -y \
    && apt-get install -y --no-install-recommends \
    python3-dev \
    python3-pip \
    gcc \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*
WORKDIR /wheels
COPY requirements.txt /app/requirements.txt

# Control source map files inclusion during the build step
ARG OMIT_SOURCE_MAPS=true
ENV OMIT_SOURCE_MAPS=$OMIT_SOURCE_MAPS

RUN pip3 wheel -r /app/requirements.txt


FROM ubuntu:22.04
RUN apt-get update \
    && apt-get dist-upgrade -y \
    && apt-get install -y --no-install-recommends \
    libpq5 \
    python3-pip \
    openssh-server \
    && rm -rf /var/lib/apt/lists/*
WORKDIR /app

COPY --from=build /wheels /wheels
COPY requirements.txt /app/requirements.txt
RUN pip3 install -r requirements.txt -f /wheels \
    && rm -rf /wheels \
    && rm -rf /root/.cache/pip/*

COPY semsys /app/semsys/
COPY templates /app/templates/
COPY staticfiles /app/staticfiles/
COPY frontend/root /app/frontend/root/
COPY manage.py /app/manage.py

# SSH setup to enable SSH via the SCM admin site. Copy-pasted from Azure documentation: 
# https://docs.microsoft.com/en-us/azure/app-service/configure-custom-container?pivots=container-linux#enable-ssh

# Install OpenSSH and set the password for root to "Docker!".
RUN echo "root:Docker!" | chpasswd 

# Copy the sshd_config file to the /etc/ssh/ directory
COPY azure/sshd_config /etc/ssh/

# Copy and configure the ssh_setup file
RUN mkdir -p /tmp
COPY azure/ssh_setup.sh /tmp
RUN chmod +x /tmp/ssh_setup.sh \
    && (sleep 1;/tmp/ssh_setup.sh 2>&1 > /dev/null)

# Open port 2222 for SSH access
EXPOSE 80 2222

# Need to use shell-form because we need to boot SSH process and then run gunicorn server
CMD service ssh restart && gunicorn semsys.asgi -b 0.0.0.0 -w 4 -k uvicorn.workers.UvicornWorker
