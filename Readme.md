# Shared Meaning Platform

A system for storing and modelling research data about social systems, and integrating that content with zoomable visual maps for interaction.


## Getting started

Example commands assume Ubuntu or similar Linux environment or macOS. Either adapt, or install under [WSL] or a virtual machine.

### Git

[Fork on github] and then set up `upstream` and `origin` remotes locally:

```sh
git clone -<NAME_EMAIL>:VisualMeaning/sm_platform.git
cd sm_platform
git remote <NAME_EMAIL>:YOURUSERNAME/sm_platform.git
```

On a new machine, you will need to create an ssh key and add it to your github profile first, see the [github ssh] documentation.


### Nodejs

- For __linux systems__, install [Node LTS] perhaps from [NodeSource].

```sh
curl -sL https://deb.nodesource.com/setup_22.x | sudo -E bash -
sudo apt-get install nodejs
```

Yes, piping things off the internet into an elevated shell prompt is bad practice, but one that still persists.

- For __macOS__, install [Node LTS] from a pre-built installer.

Note you can install a newer version of Node than the LTS for local development, and temporarily that may be required to run the frontend tests.


### Python

`sm_platform` runs Django 5, which requires Python version 3.13. 

For __linux systems__, this causes problems for Ubuntu environments as the current last LTS of Ubuntu is 24.04 which comes with Python 3.12, and which does not have a build of Python newer than this available in its `apt` package repositories. We will need to install Python 3.13 separately.

**Python install method 1: deadsnakes**

One way around this is to add the deadsnakes repository to `apt`, which has unofficial versions of the missing Python builds.

```sh
sudo add-apt-repository ppa:deadsnakes/ppa
```

Update local package lists.

```sh
sudo apt-get update
```

Then install Python 3.13 (the current requirement for `sm_platform`).

```sh
sudo apt-get install python3.13
```

This Python package doesn't appear to come with a corresponding version of `pip`, so we need to [get that separately](https://pip.pypa.io/en/stable/installation/#get-pip-py).

```sh
curl -sS https://bootstrap.pypa.io/get-pip.py | python3.13
```

**Python install method 2: pyenv**

There is also an alternative method for setting up Python 3.13 using `pyenv`, which is documented [here](https://github.com/VisualMeaning/development-texts/blob/master/comments/pyenv_and_python.md) -- this is more heavy-duty since it involves building Python from scratch, but it's also more likely to work because of that.

**Pipenv install**

Now we can install [pipenv] to manage our virtual environment and associated dependencies. This will be invoked by `make` commands later.

```sh
pip install pipenv
```

**macOS Python install**

For __macOS__, first install homebrew:

```sh
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

Install Python 3.13 via homebrew:

```sh
brew install python@3.13
```

Add the following line to `~/.bash_profile`:

```sh
export PATH="/usr/local/opt/python/libexec/bin:$PATH"
```

Install [pipenv]:

```sh
pip install pipenv
```

**Troubleshooting**

If the `pip install` command complains, it might be using the wrong pip. To make absolutely sure you are using the right version, do:

```sh
python3.13 -m pip install pipenv
```

On __WSL__ pipenv may hang for a minute or two when being run for the first time as it creates the virtual environment. There's no good fix for this and you just have to wait - once the venv is created, subsequent runs of commands relying on pipenv will run as normal.


### Install dependencies

Ensure postgresql is installed. It (or rather its libraries) needs to be present even if not developing locally or the psycopg2 install will fail when installing server dependencies.

For __linux systems__, .

```sh
sudo apt-get install postgresql
```

For __macOS__, install postgresql with homebrew:

```sh
brew install postgresql
```

Locally install react and such from npm and django and the rest from pypi.

```sh
make get-deps-frontend
make get-deps-server
```


### Serve from Heroku staging DB

This is a means of backing a local dev environment onto a remote hosted development database, in this case our Staging DB running in Heroku. Using this method means we don't have to worry about running a local DB instance, populating it with data, making sure Django migrations are applied etc. etc. It is appropriate for all development work that does not require changing the shape or content of data in the database.

Install the `heroku` command line tool and login to access staging environment:

For __linux systems__, install via pipe-to-shell:

```sh
curl https://cli-assets.heroku.com/install.sh | sh
```

On __WSL__, also set up your `BROWSER` environment variable so that the Heroku CLI can use it to open the login link. This involves making a symlink to your Windows browser executable and then setting `BROWSER` in your `.profile` so that it's configured on terminal boot.

```sh
ln -s "/mnt/c/Program Files/Google/Chrome/Application/chrome.exe" ~/.local/bin/chrome
echo 'export BROWSER=~/.local/bin/chrome' >> ~/.profile
```

Replace path to browser executable/symlink name with your preferred browser details. Reload your `.profile`, and do `heroku login`.

For __macOS__, install via homebrew:

```sh
brew tap heroku/brew && brew install heroku
```

Will open browser to do heroku login.

Can then run local code connected through to staging database on heroku:

```sh
make serve-staging
```

This will serve the staging environment at http://localhost:8000

Run local code connected through to a standalone dev database with no customer data on heroku:

```sh
make serve-dev
```


## Serve from local copy of staging DB

Occasionally we will need a fully isolated development environment for working on DB-facing things, which means spinning up a local copy of the staging database and pointing the dev environment at that instead.

Install local postgresql with version > 10.

```sh
sudo apt-get install postgresql
```

Confirm postgresql service is running.

```sh
sudo service postgresql status
```

To access the S3 bucket where our backup files are stored, you will need to configure your local `aws` CLI with appropriate credentials as `boto3` will use these to access the bucket.

Once you have done this, get the most recent backup file from staging and restore it into your local DB.

```sh
make local-restore-db-from-staging
```

Then to build everything and run local development server using local Postgres DB:

```sh
make serve
```


### Frontend only

Optionally can not run an api server locally, and proxy through to staging instead:

```sh
make staging-js
```

Can avoid using `make` at all and just run from under `frontend/js` if that's easier:

```sh
npm run start -- --api=https://staging.ecosystem.guide/
```

This means neither Django dependencies or heroku are strictly required for frontend work.


[WSL]: https://docs.microsoft.com/en-us/windows/wsl/install-win10
[Fork on github]: https://help.github.com/en/github/getting-started-with-github/fork-a-repo
[github ssh]: https://help.github.com/en/github/authenticating-to-github/connecting-to-github-with-ssh
[Node LTS]: https://nodejs.org/en/download/
[NodeSource]: https://github.com/nodesource/distributions/blob/master/README.md
[pipenv]: https://pypi.org/project/pipenv/
[pyenv]: https://github.com/pyenv/pyenv
