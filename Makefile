SHELL := /bin/bash -o pipefail
.DELETE_ON_ERROR:

default: all

PIPENV := pipenv
PYTHON := $(PIPENV) run python

## TODO: remove all rules that manipulate the local db, and replace with
## rules that manipulate the staging environment.


##  ----------------------------------------------------------------------
##  Heroku app names

HAPP_PROD := shared-meaning
HAPP_STAGING  := shared-meaning-staging
# dev is just a standalone Postgres DB with no dyno
HAPP_DEV  := shared-meaning-development


##  ----------------------------------------------------------------------
##  Git remote names (ensure you set up your repo to match)

# https://git.heroku.com/shared-meaning.git
HGIT_PROD := prod
# https://git.heroku.com/shared-meaning-staging.git
HGIT_STAGING := staging


##  ----------------------------------------------------------------------
##  API URLs

API_STAGING := https://staging.ecosystem.guide/

##  ----------------------------------------------------------------------
##  Local DB config
##    TODO do this better

LOCAL_DB         := semsys_dev
LOCAL_DB_USER    := semsys_dev_user
LOCAL_DB_USER_PW := white2paper
LOCAL_DB_URL := postgres://$(LOCAL_DB_USER):$(LOCAL_DB_USER_PW)@localhost/$(LOCAL_DB)

BACKUPS_LOCATION := dbdumps

STAGING_DB_USER = $(shell heroku pg:credentials:url -a $(HAPP_STAGING) | sed -En -e 's/.* user=([^ "]+).*/\1/p')
STAGING_DB_URL  = $(shell heroku config:get DATABASE_URL -a $(HAPP_STAGING))
EMAIL_API_KEY  = $(shell heroku config:get SENDGRID_API_KEY -a $(HAPP_STAGING))

DEV_DB_URL  = $(shell heroku config:get DATABASE_URL -a $(HAPP_DEV))

DJANGO_KEY_CMD = $(PYTHON) -c 'from django.core.management.utils import get_random_secret_key as k; print(k())'

##  ----------------------------------------------------------------------
##  JS build

JS := frontend/js
JSDIST := $(JS)/dist/js
LANGDIST := $(JS)/dist/lang
JS_BUILD := $(JS)/package.json $(JS)/package-lock.json $(JS)/babel.config.js $(JS)/webpack.config.babel.js

CSS_SRC := $(shell find frontend/js/style -regex ".*\.css")
JS_SRC := $(shell find frontend/js/src -regex ".*\.[tj]sx?")

$(JSDIST)/viewmap.js: $(CSS_SRC) $(JS_SRC) $(JS_BUILD)
	# Using touch as webpack doesn't update timestamps
	(cd frontend/js && npm run build) && touch $@

# TODO: Make all languages the target here
$(LANGDIST)/en.json $(LANGDIST)/ru.json $(LANGDIST)/zh.json: $(JS_SRC)
	cd frontend/js && npm run translations

# TODO: Could include all outputs but require grouped rules support.
all-js: $(JSDIST)/viewmap.js $(LANGDIST)/en.json


##  ----------------------------------------------------------------------
##  Utils

### Run local Django server (after building all)
serve: all
	DATABASE_URL=$(LOCAL_DB_URL) $(PYTHON) manage.py runserver

serve-staging: all
	DATABASE_URL=$(STAGING_DB_URL) $(PYTHON) manage.py runserver

serve-dev: all
	DATABASE_URL=$(DEV_DB_URL) $(PYTHON) manage.py runserver

serve-local-asgi: all
	DATABASE_URL=$(LOCAL_DB_URL) $(PYTHON) -m gunicorn semsys.asgi -b 0.0.0.0 -w 4 -k uvicorn.workers.UvicornWorker

# Develop frontend code without local server, using staging instead
staging-js:
	cd frontend/js && npm run translations && npm run start -- --env api=${API_STAGING}

local-js:
	cd frontend/js && npm run translations && npm run start

### Build everything
### TODO: Try to actually use normal prerequisites for staticfiles generation?
all: all-js
	DATABASE_URL= $(PYTHON) manage.py collectstatic --clear --no-input -v0

check: test-py test-js

clean:
	rm -rf frontend/js/dist/*    \
	       frontend/js/.cache/*  \
	       staticfiles/*

### Like clean, but removes node_modules
deepclean: clean
	rm -rf frontend/js/node_modules/*

lint: lint-js lint-py

fix: lint-js-fix lint-py-fix

lint-js:
	cd frontend/js && npm run lint

lint-js-fix:
	cd frontend/js && npm run lint -- --fix

lint-py:
	$(PYTHON) -m ruff check .
	$(PYTHON) -m black semsys --fast --check

lint-py-fix:
	$(PYTHON) -m ruff check --fix .
	$(PYTHON) -m black semsys

# TODO: Remove redundant alias
black: lint-py-fix

test-js:
	cd frontend/js && npm test

test-py: lint-py
	$(PYTHON) manage.py test

manage-staging:
	DATABASE_URL=$(STAGING_DB_URL) $(PYTHON) manage.py $(COMMAND)

## -- Fetching dependencies

get-deps-frontend:
	cd frontend/js && npm ci --include=dev

get-deps-server:
	$(PIPENV) install --dev

## -- Overwrite the staging environment's db with all data from prod
db-copy-prod-over-staging:
	heroku pg:copy $(HAPP_PROD)::DATABASE_URL DATABASE_URL --app $(HAPP_STAGING)

## -- NEW Migration management
apply-django-migrations-staging:
	DATABASE_URL=$(STAGING_DB_URL) $(PYTHON) manage.py makemigrations && \
	DATABASE_URL=$(STAGING_DB_URL) $(PYTHON) manage.py migrate

apply-django-create-cache-table-staging:
	DATABASE_URL=$(STAGING_DB_URL) $(PYTHON) manage.py createcachetable

apply-django-migrations-dev:
	DATABASE_URL=$(DEV_DB_URL) $(PYTHON) manage.py makemigrations && \
	DATABASE_URL=$(DEV_DB_URL) $(PYTHON) manage.py migrate

apply-django-create-cache-table-dev:
	DATABASE_URL=$(DEV_DB_URL) $(PYTHON) manage.py createcachetable

## -- Django-related DB and user changes

local-apply-migrations:
	DATABASE_URL=$(LOCAL_DB_URL) $(PYTHON) manage.py makemigrations semsys && \
	DATABASE_URL=$(LOCAL_DB_URL) $(PYTHON) manage.py makemigrations && \
	DATABASE_URL=$(LOCAL_DB_URL) $(PYTHON) manage.py migrate semsys && \
	DATABASE_URL=$(LOCAL_DB_URL) $(PYTHON) manage.py migrate

local-create-cache-table:
	DATABASE_URL=$(LOCAL_DB_URL) $(PYTHON) manage.py createcachetable

local-create-superuser:
	DATABASE_URL=$(LOCAL_DB_URL) $(PYTHON) manage.py createsuperuser \
		--username admin

## -- Local database backup/restore

# keep these for now, although not integrated with new backup/restore script
local-backup-db:
	PGPASSWORD="$(LOCAL_DB_USER_PW)" pg_dump -U $(LOCAL_DB_USER) -h localhost  \
		$(LOCAL_DB) > dbdumps/current.sql


local-backup-db-pgdump:
	PGPASSWORD=$(LOCAL_DB_USER_PW) pg_dump -Fc --no-acl --no-owner -h \
		localhost -U $(LOCAL_DB_USER) $(LOCAL_DB) > dbdumps/current.dump


local-restore-db-from-staging:
	$(PYTHON) scripts/db_backup_restore_s3.py --heroku-app $(HAPP_STAGING) restore \
		--method localdb --use-latest-backup


## -- Heroku deployment
heroku-git-deploy-prod:
	@read -p "Deploy code to prod? Enter $(HAPP_PROD) to confirm: " prodconf; \
	case $$prodconf in                                                        \
		'$(HAPP_PROD)' ) git push $(HGIT_PROD) master;;                   \
		* ) echo "Cancelling"; exit;;                                     \
	esac

heroku-git-deploy-staging:
	git push $(HGIT_STAGING) master --no-verify

heroku-set-sendgrid-apikey:
	heroku config:set SENDGRID_API_KEY=$(SENDGRID_API_KEY) -a $(HAPP_PROD)
	heroku config:set SENDGRID_API_KEY=$(SENDGRID_API_KEY) -a $(HAPP_STAGING)

heroku-rotate-django-secret:
	heroku config:set DJANGO_SECRET_KEY="$(shell $(DJANGO_KEY_CMD))" -a $(HAPP_STAGING)


## -- Docker image build

Pipfile.lock: Pipfile
	pipenv lock

# feels like we're doing an end run around Pipenvs repeatability here
requirements.txt: Pipfile.lock
	pipenv requirements > requirements.txt
	echo 'opencensus-ext-azure==1.1.14' >> requirements.txt
	echo 'opencensus-ext-django==0.8.0' >> requirements.txt

IMAGE_NAME="smp"

# separate build from clean as it's useful to build without cleaning sometimes
docker-build-only: requirements.txt all
	DOCKER_DEFAULT_PLATFORM=linux/amd64 docker build --build-arg OMIT_SOURCE_MAPS=true . -t $(IMAGE_NAME)

docker-build: clean docker-build-only

acr:
	@echo "using container registry $${ACR:?}" && \
	az acr login --name $(ACR)

docker-acr-push: acr
	docker tag $(IMAGE_NAME) $(ACR)/$(IMAGE_NAME) && \
	docker push $(ACR)/$(IMAGE_NAME) && \
	NOW=$$(date '+%Y%m%d%H%M%S') && \
	docker tag $(IMAGE_NAME) $(ACR)/$(IMAGE_NAME):$$NOW && \
	docker push $(ACR)/$(IMAGE_NAME):$$NOW
