# Microsoft SSO and the SMP

This doc is an outline of research around and a proposal for how we integrated Microsoft single sign-on into the Heroku deployment of the Shared Meaning Platform.

## Existing authentication and authorization methods

Note: authentication is "proving who you are". Authorization is "deciding what you're allowed to do".

### Authentication

**Logging in with a Django user account**

- User objects and associated permissions are stored directly in the Django application database.
- Drawbacks are high overhead of Django user account management, and the fact that this *has* to be managed internally to VM as being able to manage (i.e. edit) User objects in Django is equivalent to superuser access. (As you can just edit your own User object to give yourself superuser access.)
- Therefore we only use Django user accounts for internal VM users who require permissions to do things like creating maps.

**Email verification ("Guest Access")**

- Unauthenticated users attempting to access a map will be redirected to a page where they are prompted to enter their email into the form.
- Once the form has been submitted, an email will be sent to that email account with a link that attaches their email to their session cookie as a form of identity.
- Email verification is used for all of our end users who wish to view a map.
- Drawbacks are that we are increasingly running into scenarios where the identity verification emails are being trapped in corporate IT spam filters as coming from an untrusted source.

### Authorization

**Django user groups**

- Only used with Django user accounts. Each client project has an associated user group. A Django user account which is part of that user group is given access to the map, including access for uploading new map models via our APIs.
- Mostly not used, the only exception is when someone needs to upload map models, as this is the only way this level of permission can be granted.

**Email whitelist**

- Each client project has an associated list of specific emails or email domains (e.g. visual-meaning.com) which are allowed to access that project's maps.
- An authenticated user will have an email identity, either from email verification or from the email field in their Django user account settings.
- On attempting to access a map for a given client project, the user's email identity is checked against the email whitelist for that project. If the specific email or the email domain is included on the whitelist, the user is permitted to view the map.
- Access granted via the email whitelist grants read-only access to a map. An email identity allows users to leave comments (annotations) where this feature is enabled, and this is the only write operation permitted.

## Microsoft SSO

### Why?

- Not having a form of SSO available has been a sticking point with some clients - specifically, their IT security departments.
- The email verification authentication method used for end users has proven to be unreliable due to spam filters. SSO with a corporate account managed from the client's side would be a relatively low-overhead authentication alternative for end users.
- Most of our clients are backed onto the Microsoft ecosystem, therefore implementing SSO with Microsoft accounts would be a feature that they could take advantage of.

A proof-of-concept has been implemented to demonstrate how this could work.

### App registration

We start with an [Application Registration] created in VM's corporate Microsoft directory. This represents the SMP, and is the application that users will sign into.

The Application Registration is configured to allow sign-in from _"Accounts in any organizational directory (Any Microsoft Entra ID tenant - Multitenant) and personal Microsoft accounts (e.g. Skype, Xbox)"_.

Note that the Application will appear as "Unverified" to all users who sign into it. There are warning messages about sign-in being blocked for Unverified applications, and while I could sign into the Application with my personal Microsoft account it is highly likely that external corporate Microsoft accounts will block sign-in to an Unverified application. 

**WORK ITEM**: We can make the App Registration "Verified" by going through Microsoft's [Publisher Verification process]. This is a moderately-complex admin task that requires no development work.

Additionally, it's also very possible that external corporate Microsoft accounts are only allowed to sign into specific whitelisted applications. This is something that we'd have to deal with on a per-client basis.

We copy/create the following config on the Application Registration:

- **The Application Client ID**
- **A Client Secret** (sensitive, should be transmitted and stored securely)
- **One or more callback URLs**. This is a URL on the SMP where the user will be redirected to after signing in, and where we can configure their identity on the app before forwarding them on to the page they were originally trying to access.
    - The callback URL is supplied by the SMP as part of its invocation of the SSO flow, however attempting to call back to URLs which are not registered on the Application Registration will not be permitted and will result in an error.
    - We will need multiple callback URLs per environment, including dev environments such as `localhost`.

### How Django invokes SSO

See [Plectica diagram of proof-of-concept flow].

The proof-of-concept uses a Microsoft-maintained authorization package called [MSAL].

The core object we use from MSAL is a `ConfidentialClientApplication`. This is used in secure environments (such as an application web server that end users cannot directly access) and is instantiated with the following:

- The Application Client ID
- The Client Secret (would have to be ingested from an environment variable in production)
- The "authority", or in other words the Microsoft endpoint to use for authorization. https://login.microsoftonline.com/common works for all use cases that were tried during the PoC (VM Microsoft account, personal Microsoft account and a client Microsoft account), however if we work with a client who has their own segregated Azure environment with their own authorization endpoint this might be different.

We then call the `initiate_auth_code_flow` method on the application instance. This takes a further two arguments:

- An array of desired scopes for the user login. What gets returned from the SSO login is a set of tokens that can be used to make calls to Microsoft APIs and services on the user's behalf. The scopes determine exactly what sort of information we will be allowed to access.
   - The `user.read` scope was used in the proof-of-concept.
   - The scope of the user data that we're trying to access will be surfaced to the user as part of the SSO login flow prior to them authorizing the login.
- A redirect URI to send the user to after they have logged in. This is one of the callback URLs that we configured on the Application Registration.

This creates an `auth_flow` dictionary that is attached to the session cookie.

The `auth_flow` dictionary contains an `auth_uri` field, which will be the authorization endpoint we selected as our authority with a bunch of pre-configured authorization data jammed onto the end. The final step of initiating SSO from Django is to redirect the user to the Microsoft URL in this field, where they will go through the SSO process and then be redirected back to our application callback URL.

When the user re-enters the SMP application via the callback URL, there will be additional authorization parameters included in the `request.GET` arguments by the SSO flow that are used to complete the auth process and acquire tokens for the user. We call the `acquire_token_by_auth_code_flow` method on the application instance with the `auth_flow` dictionary we stored in the session cookie and the `request.GET` arguments as parameters. This makes another request out to a Microsoft token endpoint, which returns a token cache for the user.

For the proof-of-concept, the very last step of the process is to use the user's access token from the token cache to call the Microsoft graph API https://graph.microsoft.com/v1.0/me. This will return user details for this user, including their email address. We attach the email address to their session cookie just like we would an email acquired during email verification, and then we discard the token cache. The email identity in the cookie will expire when the cookie does.

Once we have their email identity, we redirect them onto the map where their email identity is checked against the email whitelist and they are authorized to view it (or not) based on whether their email is present in the whitelist.

### Work required for productionalisation

- If we're building the SSO in its own test environment with safe data, then that test environment needs to be spun up and populated and we need to *not* merge our changes into the master branch (which automatically deploys to staging). Instead we'll need a common feature branch for SSO that we manually deploy to the test environment via `git push`.
- The Client ID and secret should be stored and accessed securely, not as part of the application code.
- We need to decide how we're storing other SSO settings. The model I've seen in an example project is a big dict of stuff in `settings.py`, however we do need things like a dynamically-determined callback URL based on environment.
- Clients should be able to opt-in to enabling Microsoft SSO on their projects. This means that Microsoft SSO should be a configurable option on the Django Project object.
- If Microsoft SSO is enabled for a Project, it should _not_ be possible to access that project's map's via email verification. This means two things:
    - We need to disable the Guest Access flow for SSO-enabled projects and redirect these users down an SSO flow.
    - We need to include additional information about the authorization type in the user's session cookie. This can then be used as an additional piece of information to determine user type, which is a piece of information used to decide whether a user can access a map.
- We need some basic UX design for initiating SSO from the SMP.
- There should be some obvious way of logging out and scrubbing the SSO identity. There is currently a logout endpoint but it's hidden.
- The SSO paths should only be included in `urls.py` if deployed into the Heroku environment. Our Azure deployment already does SSO via an Easy Auth sidecar on the App Service.
- The [Publisher Verification process] needs to be done as per the above work item.

### Additional questions to answer

- Do we need to use `msal`? It's currently providing us utility functions for pre-configuring the request out to the SSO endpoint, and the subsequent request required to use the returned auth parameters to retrieve the access token. Auth is complicated and we definitely don't want to be trying to build the URLs from first principles (good luck figuring out nonces), but there may be alternative packages out there that can help us.
    - A trawl through the `msal` source code reveals that [it's sending telemetry](https://github.com/AzureAD/microsoft-authentication-library-for-python/blob/ca0877e953200cb9c3e68b572143442349bd1927/msal/telemetry.py#L22) to Microsoft when we invoke its methods to make external requests. Which I _really_ do not like.
- Users signing in once with SSO and then having an identity that persists for two weeks (the cookie expiry length) even if they subsequently sign out of their SSO account elsewhere. We might want to consider either having a shorter expiry time on the SSO identity (which would require storing the expiry time in the cookie and writing some code to check it), or just shortening the expiry time on the whole cookie.
- We currently have a policy that you can't use email verification with an email that's associated with a Django user account. Do we want to enforce this policy for SSO logins? A reasonably easy alternative would be logging the user into their Django account if a matching one is found, but that's putting a lot of trust in the SSO process.
- Most end users access maps via a direct link and never see the login or maps pages, which is where the current identity info is surfaced. Do we want to surface this in the React app somewhere?
- This proposal uses SSO for authentication (proving user identity), but not authorization. Authorization is managed via the existing method of the per-project email whitelist. It might be possible to do something more granular based on groups or grants on the SSO identity, but this would be a lot of work. It would be good to get some idea of exactly how much in case that's something we want to implement in the future.

[Application Registration]: https://learn.microsoft.com/en-us/entra/identity-platform/quickstart-register-app?tabs=certificate
[Publisher Verification process]: https://learn.microsoft.com/en-gb/entra/identity-platform/publisher-verification-overview
[Plectica diagram of proof-of-concept flow]: https://www.plectica.com/maps/KRPZHBLKA
[MSAL]: https://learn.microsoft.com/en-us/entra/msal/python/
