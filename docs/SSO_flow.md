## Shared Meaning Platform Application Single Sign-on Flow

### SSO Flow Breakdown

The application uses the the OAuth2 **authorization code grant flow** as defined in [RFC 6749, Section 4.1](https://datatracker.ietf.org/doc/html/rfc6749#section-4.1). In this flow, the user is redirected to a trusted identity provider (Microsoft) for authentication, and the application receives an authorization code upon successful login. All interactions with Microsoft's identity platform are performed over HTTPS, ensuring that sensitive data such as the authorization code and access token are securely transmitted.

This diagram shows a high-level view of the authentication flow:
![SSO flow diagram](sso.png)

1. **User Initiates Login**:
   - The user begins the SSO process by clicking the "Sign in with Microsoft" button on the application’s `/signin` page.
   - The application checks the user’s session to see if the user is already signed in by looking for an email address in the session cookie. If the email exists, the user is redirected back to the `/signin` page with information about their currently signed-in account.
   - If the user is not already signed in, the application initiates the SSO flow and redirects the user to Microsoft’s login page via the `/microsoft_authentication/login` path.
   - If SSO is disabled on the site and the user attempt to access `/microsoft_authentication/login` URL, they will be redirected to a 404 error page.
   - As part of the OAuth2 authorization process, the application maintains and verifies the state parameter to protect against cross-site request forgery (CSRF) attacks. This ensures that the authorization response is correlated with the request initiated by the same user.

2. **Redirect to Microsoft for Authentication**:
   - The user is redirected to Microsoft’s authentication endpoint, where they enter their credentials (username and password) on Microsoft’s secure login page, and Microsoft handles the authentication process.
   - The application requests minimal scopes (`user.read`), adhering to the OAuth2 **principle of least privilege**.
   - The user is explicitly asked for consent to these scopes during login, ensuring transparency in what data is accessed.

3. **User Authenticated and Redirected Back**:
   - After successful authentication, Microsoft redirects the user back to the application’s callback URL `microsoft_authentication/callback`, including an authorization code in the request.
   - If the user encounters an error (e.g., cancels login or fails authentication), they are redirected back to `/signin` page with an error message `Microsoft Sign In was unsuccessful!`.
   - If SSO is disabled and the user attempts to access the `/microsoft_authentication/callback` URL, they will be redirected to a 404 error page.
   - If the user is logged in and attempts to access the `/microsoft_authentication/callback` URL, they will be redirected to a 400 bad request error page.
   - The application is configured to support only defined callback URIs. This ensures that callback URIs are limited to domains we fully control, enhancing security by preventing unauthorized redirections.

4. **Exchange Authorization Code for Access Token**:
   - The application exchanges the authorization code from the callback request for an access token. The application ensures that the redirect_uri is included in the POST request to the token endpoint. This guarantees that the redirect URI used during the token request matches the one initially used during the authorization request, as required by the OAuth2 specification.
   - The access token is not stored in the application’s database or session at any point. The access token is only temporarily used to retrieve user email from Microsoft’s APIs. This adheres to the OAuth2 best practice of minimizing token exposure and limiting the duration of its use.
   - The application validates the tokens received from Microsoft, ensuring their integrity and authenticity as a requirement of the OAuth2 standard. Additionally, the application validates incoming callback requests to ensure they originate from Microsoft.

5. **Access Microsoft Graph API**:
   - Once the application has obtained the access token, it makes an API call to Microsoft Graph API https://graph.microsoft.com/v1.0/me, to fetch the user's email.
   - The user’s email is stored in a session cookie, allowing the application to persist the user’s identity across requests and logins.
   - The session cookie has expiry of two weeks, meaning the user can stay signed in with the same SSO identity for that period, even if they sign out of their Microsoft account elsewhere during this time.
   - The user’s identity is managed via session cookies, not access tokens. This separation of authentication and session management ensures that tokens are not unnecessarily exposed during normal application use, adhering to OAuth2 security guidelines.

6. **User Redirected to Post-Login Destination**:
   - Once the authentication process is complete and the user email is retrieved, the user is redirected to the original page they were trying to access.
